# CosBigCustomerHistoryChangeController 使用说明

## 概述

`CosBigCustomerHistoryChangeController` 负责大客户历史变动数据的增删改查操作，对应数据库表 `cos_longterm_predict_input_big_customer_change`。

## 接口说明

### 1. 查询大客户历史变动数据

**接口路径**: `/cos-longterm-predict/queryBigCustomerHistoryChange`

**请求参数** (`QueryBigCustomerHistoryChangeReq`):
```json
{
  "categoryId": 1,     // 必须参数：方案id
  "taskId": 0          // 可选参数：任务id，为0时查询默认数据
}
```

**响应数据** (`QueryBigCustomerHistoryChangeResp`):
```json
{
  "dataList": [
    {
      "id": 1,
      "categoryId": 1,
      "taskId": 0,
      "customerName": "快手",
      "isOutCustomer": true,
      "startDate": "2024-01-01",
      "endDate": "2024-04-30",
      "netChange": 600
    }
  ]
}
```

**查询逻辑**:
- 当 `taskId` 为 0 或未传时，查询当前方案 `categoryId` 下的默认大客户历史变动信息
- 当 `taskId` 有值时，查询指定任务的大客户历史变动信息
- 结果按开始时间和客户名称排序

### 2. 保存大客户历史变动数据

**接口路径**: `/cos-longterm-predict/saveBigCustomerHistoryChange`

**请求参数** (`SaveBigCustomerHistoryChangeReq`):
```json
{
  "categoryId": 1,     // 必须参数：方案id
  "taskId": 100,       // 可选参数：任务id，未传时填0
  "dataList": [
    {
      "customerName": "快手",
      "isOutCustomer": true,
      "startDate": "2024-01-01",
      "endDate": "2024-04-30",
      "netChange": 600
    },
    {
      "customerName": "快手",
      "isOutCustomer": true,
      "startDate": "2024-04-30",
      "endDate": "2024-12-31",
      "netChange": 600
    }
  ]
}
```

**响应数据**: 
```json
"success"
```

**保存逻辑**:
- 采用覆盖策略：先删除已存在的数据，再全量插入新数据
- 当 `taskId` 没传时，填 0，表示这份数据归属于 `categoryId`
- 当 `taskId` 有值时，表示属于当前的任务
- 系统会自动设置每条数据的 `categoryId` 和 `taskId`

## 数据库表结构

表名：`cos_longterm_predict_input_big_customer_change`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| category_id | BIGINT | 方案id |
| task_id | BIGINT | 任务id，当值为0时表示默认数据 |
| customer_name | VARCHAR | 客户名称 |
| is_out_customer | BOOLEAN | 是否外部客户，0表示否，1表示是 |
| start_date | DATE | 变动开始时间 |
| end_date | DATE | 变动结束时间 |
| net_change | DECIMAL | 变化量(单位PB) |
| deleted | BOOLEAN | 删除标志 |
| create_time | TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | 更新时间 |

## 使用示例

### 查询示例
```javascript
// 查询方案ID为1的默认大客户历史变动数据
{
  "categoryId": 1,
  "taskId": 0
}

// 查询方案ID为1，任务ID为100的大客户历史变动数据
{
  "categoryId": 1,
  "taskId": 100
}
```

### 保存示例
```javascript
// 保存默认数据（taskId为0）
{
  "categoryId": 1,
  "dataList": [
    {
      "customerName": "快手",
      "isOutCustomer": true,
      "startDate": "2024-01-01",
      "endDate": "2024-04-30",
      "netChange": 600
    }
  ]
}

// 保存任务数据
{
  "categoryId": 1,
  "taskId": 100,
  "dataList": [
    {
      "customerName": "快手",
      "isOutCustomer": true,
      "startDate": "2025-01-01",
      "endDate": "2025-03-31",
      "netChange": -500
    }
  ]
}
```

## 注意事项

1. `categoryId` 是必须参数，不能为空
2. 保存操作会先删除已存在的数据，再插入新数据，请确保传入完整的数据列表
3. 数据按开始时间和客户名称排序返回
4. 支持外部客户和内部客户的区分
5. 变化量支持正负值，正值表示增长，负值表示减少
