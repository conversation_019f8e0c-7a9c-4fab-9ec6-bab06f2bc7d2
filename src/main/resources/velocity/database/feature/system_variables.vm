<script type="text/x-template" id="systemVariables">
    <div>
        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="variable" label="variable" min-width="150"></el-table-column>
            <el-table-column prop="variableName" label="变量名称" min-width="150"></el-table-column>
            <el-table-column prop="value" label="实际值" min-width="160"></el-table-column>
            <el-table-column prop="defaultValue" label="默认值" min-width="120"></el-table-column>
            <el-table-column prop="note" label="说明" min-width="300"></el-table-column>
        </el-table>
    </div>
</script>

<script>
Vue.component('system-variables', {
    template: '#systemVariables',
    data: function () {
        return {
            tableLoading: false,
            tableData: []
        }
    },
    props: {
        databaseId: Number
    },
    created: function() {
        this.getData()
    },
    methods: {
        getData: function() {
            var that = this
            that.tableLoading = true
            Resource.get("${_contextPath_}/database_feature/query_system_variables", {
                databaseId: that.databaseId
            }, function(resp){
                that.tableData = resp.data.items
                that.tableLoading = false
            })
        }
    }
})
</script>