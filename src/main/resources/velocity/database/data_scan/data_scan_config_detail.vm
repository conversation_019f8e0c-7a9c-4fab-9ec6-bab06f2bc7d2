<!-- 扫描规则明细弹框 -->

<style>
    .database-table-expand label {width: 120px;color: #99a9bf;}
    .database-table-expand .el-form-item {width: 100%;}
</style>

<script type="text/x-template" id="databaseScanDetail">
<div>
    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>
                <span>结果列：</span>
                    <el-input v-model="resultMainColumns" style="width: 1300px"></el-input>
            </span>
        </div>
        <div>
            <el-button type="success" @click="addNewMatch()">新增一行匹配</el-button>
            <div v-for="item in scanConfigDetail.matchTextContent">
                <span>匹配mvel脚本:</span>
                <el-input v-model="item.mvelScript" placeholder="" style="width: 400px"></el-input>
                <span>匹配SQL条件：</span>
                <el-input v-model="item.sqlCondition" placeholder="column like '%foo%'" style="width: 400px"></el-input>
            </div>
        </div>
        <el-divider></el-divider>
        <div>
            <el-button type="success" @click="addNewFilter()">新增一行过滤</el-button>
            <div v-for="item in scanConfigDetail.filterTextContent">
                <span>匹配mvel脚本:</span>
                <el-input v-model="item.mvelScript" placeholder="" style="width: 400px"></el-input>
                <span>过滤SQL条件：</span>
                <el-input v-model="item.sqlCondition" placeholder="column not like '%foo%'" style="width: 400px"></el-input>
            </div>
        </div>
        <div style="float: right">
            <span>试运行分组，输入起始ID：</span><el-input v-model="tryStartKey" placeholder="输入起始ID" style="width: 120px"></el-input>
            <el-button type="plain" @click="tryScan()">试运行</el-button>
        </div>
    </el-card>
    <div style="height: 20px">
        <el-button type="primary" style="float: right; margin-top: 10px" @click="saveAll">保存</el-button>
    </div>

    <el-dialog title="匹配结果" :visible.sync="showMatchResultDialog" top="20px" width="1200px" :append-to-body="true">
        <el-table :data="matchResultList" border stripe>
            <el-table-column prop="targetTableId" label="目标表ID" width="100"></el-table-column>
            <el-table-column prop="targetMainColumn" label="目标表主列" v-if="!resultMainColumns" ></el-table-column>
            <el-table-column v-for="(item,index) in resultMainColumns.split(',')" :key="index" :label="item" align="center" v-if="resultMainColumns">
                <template slot-scope="props">
                    <div>{{JSON.parse(props.row.targetMainColumn)[item]}}</div>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</div>
</script>

<script>
    Vue.component('database-scan-detail', {
        template: '#databaseScanDetail',
        data: function() {return {
            scanConfigDetail: [],
            tryStartKey: '',
            matchResultList: [],
            showMatchResultDialog: false
        }},
        props: {
            databaseScanConfigId: Number,
            resultMainColumns: String
        },
        created: function() {
            this.getData()
        },
        watch: {
            databaseScanConfigId() {
                this.getData()
            }
        },
        methods: {
            getData: function() {
                var that = this
                Resource.get("${_contextPath_}/database_scan_config/query_scan_config_detail",
                        {configId: that.databaseScanConfigId}, function(resp){
                    that.scanConfigDetail = resp.data.scanConfigDetail
                })
            },
            addNewMatch: function() {
                this.scanConfigDetail.matchTextContent.push({})
            },
            addNewFilter: function() {
                this.scanConfigDetail.filterTextContent.push({})
            },
            saveAll: function() {
                Resource.postJson("${_contextPath_}/database_scan_config/save_scan_config_detail",
                        {scanConfigId: this.databaseScanConfigId,
                            scanConfigDetail: this.scanConfigDetail,
                            resultMainColumns: this.resultMainColumns})
            },
            tryScan: function() {
                var that = this
                Resource.postJson("${_contextPath_}/database_scan_config/try_scan",
                        {scanConfigId: this.databaseScanConfigId,
                            scanConfigDetail: this.scanConfigDetail,
                            lastKey: this.tryStartKey,
                            resultMainColumns: this.resultMainColumns},
                function(resp) {
                    that.showMatchResultDialog = true
                    that.matchResultList = resp.data
                })
            }
        }
    })
</script>