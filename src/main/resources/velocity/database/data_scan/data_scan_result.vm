<!-- data_scan_result.vm -->

<style>
    .database-scan-result-table-expand label {width: 120px;color: #99a9bf;}
    .database-scan-result-table-expand .el-form-item {width: 100%;}
</style>

<script type="text/x-template" id="databaseScanResult">
    <div>
##        <el-form :inline="true" @keyup.native.enter="getData">
##            <el-form-item label="">
##                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
##            </el-form-item>
##            <el-form-item>
##                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
##                <el-button @click="resetQuery">重置</el-button>
##                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
##            </el-form-item>
##        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="id" width="80px"></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column prop="targetTableId" label="命中的表的id"></el-table-column>
            <el-table-column prop="targetMainColumn" label="命中的表的主要字段" width="800px"></el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>
    </div>
</script>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    Vue.component('database-scan-result', {
        template: '#databaseScanResult',
        data: function() {return {
            queryForm: Utils.copy(defaultQueryForm),
            total: 0, tableData: [], tableLoading: false
        }},
        props: {
            databaseScanConfigId: Number
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                that.queryForm.databaseScanConfigId = that.databaseScanConfigId
                Resource.get("${_contextPath_}/database_scan_config/get_scan_result_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            }
        }
    });
</script>