<!-- database_check_item.vm -->

<style>
  .database-check-item-table-expand label {width: 120px;color: #99a9bf;}
  .database-check-item-table-expand .el-form-item {width: 100%;}
</style>

<script type="text/x-template" id="databaseCheckItem">
<div>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item>
      <el-button type="primary" @click="(queryForm.isSuccess=false) || (queryForm.page=1) && getData()">只看失败</el-button>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="(queryForm.isSuccess=null) || (queryForm.page=1) && getData()">重置</el-button>
    </el-form-item>
    <el-form-item style="float: right">
      <el-button type="primary" @click="resetFailCount">重置失败数</el-button>
      <el-button @click="deleteFailRecord">删除失败结果</el-button>
    </el-form-item>
  </el-form>

  <el-table :data="tableData" border stripe v-loading.body="tableLoading">
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-form :data="props" class="database-check-item-table-expand">
          <el-form-item label="检查sql">{{props.row.sql}}</el-form-item>
          <el-form-item label="sql执行信息">{{props.row.errorMsg}}</el-form-item>
          <el-form-item label="检查明细sql">{{props.row.detailSql}}</el-form-item>
          <el-form-item label="检查明细结果">
            <el-input
                    type="textarea"  readonly
                    v-model="props.row.detailRowsJson"
                    placeholder="请输入地址"
                    :rows="20">
            </el-input>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column prop="id" label="检查id"></el-table-column>
    <el-table-column prop="assertion" label="断言表达式"></el-table-column>
    <el-table-column prop="sqlRowsJson" label="sql执行返回值"></el-table-column>
    <el-table-column prop="sqlTimeMs" label="sql执行时间(ms)"></el-table-column>
    <el-table-column label="执行成功">
      <template slot-scope="props">
        <span v-show="props.row.isSuccess" style="color: green">成功</span>
        <span v-show="!props.row.isSuccess" style="color: red">失败</span>
      </template>
    </el-table-column>
    <el-table-column prop="createTime" label="执行时间"></el-table-column>
  </el-table>

  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                 :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>
</div>
</script>

<script>
  var defaultQueryForm = {page: 1, pageSize: 10}
  Vue.component('database-check-item', {
    template: '#databaseCheckItem',
    data: function() {return {
      queryForm: Utils.copy(defaultQueryForm),
      total: 0, tableData: [], tableLoading: false
    }},
    props: {
      databaseCheckConfigId: Number
    },
    created: function() {
      this.getData()
    },
    watch: {
      databaseCheckConfigId() {
        this.queryForm = Utils.copy(defaultQueryForm)
        this.getData()
      }
    },
    methods: {
      getData: function() {
        var that = this
        that.tableLoading = true
        if (this.databaseCheckConfigId) this.queryForm.databaseCheckConfigId = this.databaseCheckConfigId
        Resource.get("${_contextPath_}/database_check_item/get_page", this.queryForm, function(resp){
          that.tableData = resp.data.data
          that.total = resp.data.total
          that.tableLoading = false
        })
      },
      pageChange: function(page) {
        this.queryForm.page = page
        this.getData()
      },
      resetFailCount: function() {
        Resource.post("{_contextPath_}/database_check_item/reset_fail_count", {databaseCheckConfigId: this.databaseCheckConfigId}, function(resp) {
          Message.success("重置成功，请自行刷新页面查看")
        })
      },
      deleteFailRecord: function() {
        Resource.post("{_contextPath_}/database_check_item/delete_fail_record", {databaseCheckConfigId: this.databaseCheckConfigId})
      }
    }
  })
</script>
