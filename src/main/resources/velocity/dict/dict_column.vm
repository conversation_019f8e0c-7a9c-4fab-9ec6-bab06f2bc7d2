<script type="text/x-template" id="dictColumn">
    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="字段名称">
                <el-input v-model="queryForm.name" placeholder="字段名称，支持模糊查询"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input>
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="id" width="80"></el-table-column>
            <el-table-column prop="name" label="字段名称"></el-table-column>
            <el-table-column prop="dbColumnName" label="数据库列名"></el-table-column>
            <el-table-column prop="columnType" label="列类型"></el-table-column>
            <el-table-column prop="description" label="描述" min-width="300px"></el-table-column>
            <el-table-column label="是否虚拟列">
                <template slot-scope="scope">
                    <span v-text="scope.row.isVirtualColumn ? 'true' : 'false'"></span>
                </template>
            </el-table-column>
            <el-table-column prop="matchExpression" label="匹配表达式"></el-table-column>
            <el-table-column prop="displayExpression" label="显示表达式"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" ref="addEditForm">
                <el-form-item label="字段名称" prop="name">
                    <el-input v-model="addEditForm.name" placeholder="列名，这个会作为外部引用，中文"></el-input>
                </el-form-item>
                <el-form-item label="数据库列名" prop="dbColumnName">
                    <el-input v-model="addEditForm.dbColumnName" placeholder="数据库中的列名"></el-input>
                </el-form-item>
                <el-form-item label="列类型" prop="columnType">
                    <el-input v-model="addEditForm.columnType" placeholder="列类型，String、Date、DateTime、Integer、Decimal等"></el-input>
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input v-model="addEditForm.description" placeholder="描述信息"></el-input>
                </el-form-item>
                <el-form-item label="是否虚拟列" prop="isVirtualColumn">
                    <el-input v-model="addEditForm.isVirtualColumn" placeholder="是否虚拟列，虚拟列就是数据库不存在的列，默认false"></el-input>
                </el-form-item>
                <el-form-item label="匹配表达式" prop="matchExpression">
                    <el-input v-model="addEditForm.matchExpression" placeholder="匹配表达式，一般只有虚拟列才需要，例如 ?>=start and ?<=end这样，sql语法，问号是占位符"></el-input>
                </el-form-item>
                <el-form-item label="显示表达式" prop="displayExpression">
                    <el-input v-model="addEditForm.displayExpression" placeholder="展示的表达式，sql语法，一般是虚拟列才需要这个表达式"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</script>

<script>
var defaultQueryForm = {page: 1, pageSize: 10}
var defaultAddForm = {}
Vue.component('dict-column', {
    template: '#dictColumn',
    data: function () {
        return {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            tableLoading: false,
            tableData: [],
            showDialog: false, dialogTitle: ''
        }
    },
    props: {
        dictId: Number
    },
    created: function() {
        var that = this
        that.getData()
    },
    methods: {
        getData: function() {
            var that = this
            that.tableLoading = true
            that.queryForm.dictId = that.dictId
            Resource.get("${_contextPath_}/dict_column/get_all", this.queryForm, function(resp){
                that.tableData = resp.data
                that.tableLoading = false
            })
        },
        resetQuery: function() {
            this.queryForm = Utils.copy(defaultQueryForm)
        },
        handleDelete: function(row) {
            var that = this
            Message.confirm("确定要删除吗?", function(){
                Resource.post("${_contextPath_}/dict_column/delete", {id: row.id}, function(){
                    that.showDialog = false
                    Message.success("删除成功，列表已刷新")
                    that.getData()
                })
            })
        },
        handleAddOrEdit: function(isAdd, row) {
            this.showDialog = true
            this.dialogTitle = isAdd ? '新增字典列' : '编辑'
            Form.clearError(this, 'addEditForm')
            this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
        },
        doAddOrEdit: function() {
            var that = this
            var isEdit =  this.addEditForm.id ? true : false
            that.addEditForm.dictId = that.dictId
            Form.validate(this, 'addEditForm', function() {
                Resource.post("${_contextPath_}/dict_column/add_or_update", that.addEditForm, function(resp){
                    Message.success(isEdit ? "修改成功" : "新增成功")
                    isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                    that.getData()
                })
            })
        }
    }
})
</script>