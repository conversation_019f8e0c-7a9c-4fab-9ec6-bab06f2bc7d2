#set($page_title='分支模型管理')
<style>
    a {
        text-decoration: none
    }
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData(false)">
        <el-form-item label="">
            <el-input v-model="queryForm.name" placeholder="仓库名"></el-input>
        </el-form-item>
        <el-form-item label="">
            <el-input v-model="queryForm.branchName" placeholder="分支名"></el-input>
        </el-form-item>
        <el-form-item>
            <el-tooltip class="item" effect="dark" content="延迟最长1分钟" placement="bottom">
                <el-button type="primary" @click="(queryForm.page=1) && getData(false)">查询</el-button>
            </el-tooltip>
            <span>自动刷新:</span><el-switch v-model="isAutoRefreshRepo"></el-switch>
            <el-button type="primary" @click="getData(true)">强制刷新</el-button>
            <span>显示全部分支:</span><el-switch v-model="queryForm.showAllBranch"></el-switch>
            <el-button type="success" @click="handleAddOrEdit(true)">新增仓库</el-button>
            <el-button @click="permission.dialogVisible=true" v-show="permission.isCheckPermission">刷新权限</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column label="仓库名" width="300">
            <template slot-scope="props">
                <a @click="jumpGitUrl(props.row.url)" style="cursor:pointer;color:blue">{{props.row.name}}</a>
            </template>
        </el-table-column>
        <el-table-column label="开发分支" min-width="130">
            <template slot-scope="props">
                <ul>
                    <li style="display: flex;justify-content: space-between;margin-top: 4px;"
                        v-for="o in props.row.developList">
                        <div style="padding-top: 5px">{{o.name}} <span v-show="o.creator">({{o.creator}})</span>
                            <span style="color:gray; background-color: lightyellow">({{o.behindCount}},{{o.aheadCount}})</span>
                            <i class="el-icon-top-right" style="cursor:pointer" @click="doJumpBranch(props.row,o.name)"></i>
                            <el-tooltip content="是否自动合并master" placement="right" v-if="!o.isAutoMergeRemote">
                                <el-switch v-model="o.isAutoMergeMaster" style="height: 0px"
                                           @change="switchAutoMergeMaster(o.isAutoMergeMaster,props.row.id,o.name)"></el-switch>
                            </el-tooltip>
                            <el-tooltip placement="right" v-if="o.isAutoMergeRemote">
                                <div slot="content">自动同步{{o.remoteUrl}} {{o.isMergeRemoteFail ? ("失败,原因:" + o.mergeRemoteFailMsg) : "成功"}}</div>
                                <i class="el-icon-refresh" style="color: red" v-if="o.isMergeRemoteFail"></i>
                                <i class="el-icon-refresh" v-else></i>
                            </el-tooltip>
                            <el-tag type="danger" v-show="o.isMergeMasterFail" size="mini">合入master失败:{{o.mergeMasterFailMsg}}</el-tag>
                            <el-tag :type="tag.newCommit || tag.newMaster || tag.isScanError || tag.buildStatus=='FAILURE' ? 'danger' : (tag.buildStatus=='NEW'||tag.buildStatus=='RUNNING' ? 'warning' : 'success')"
                                    v-for="tag in o.tags" size="mini" style="display: inline">
                                {{tag.newMaster ? "Master有更新" : ""}} {{tag.name}}
                                {{tag.buildStatus=='FAILURE' ? "编译失败" : ""}}
                                {{tag.buildStatus=='RUNNING' ? "编译中" : ""}}
                                {{tag.buildStatus=='NEW' ? "编译未启动" : ""}}
                                {{tag.buildStatus=='SUCCESS' ? "✔" : ""}}
                                {{tag.isScanError ? "扫出错误" : ""}}
                                {{tag.isLock ? "🔒" : ""}}
                                <el-popover v-if="tag.isScanError" placement="top-start" width="500"
                                            trigger="click" :content="tag.scanErrMsg">
                                    <el-tag slot="reference" style="display: inline; cursor: hand" type="warning">详情</el-tag>
                                </el-popover>
                            </el-tag>
                            <el-tag type="warning" v-if="o.newCommit" size="mini" style="display: inline; cursor: hand"
                                    @click.native="handleDevCompareDirect(props.row, o.oldCommitId, o.newCommitId)">开发NEW
                            </el-tag>
                        </div>

                        <el-dropdown split-button type="primary" @click="doCompareMaster(props.row,o.name)" @command="branchMore" size="small">对比
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="branchMoreValue('mergeMaster', props.row, o.name)">合并master</el-dropdown-item>
                                <el-dropdown-item :command="branchMoreValue('mergeOtherBranch', props.row, o.name)">合并其它分支</el-dropdown-item>
                                <el-dropdown-item :command="branchMoreValue('delete', props.row, o.name)">删除该分支</el-dropdown-item>
                                <el-dropdown-item :command="branchMoreValue('copy', props.row, o.name)">复制该分支</el-dropdown-item>
                                <el-dropdown-item :command="branchMoreValue('resetOneCommit', props.row, o.name)">回退最后一次commit</el-dropdown-item>
                                <el-dropdown-item :command="branchMoreValue('snapshotAPI', props.row, o.name)">发布SnapshotAPI</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </li>
                </ul>
            </template>
        </el-table-column>
        <el-table-column width="360" label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddBranch(scope.row, '')">新分支</el-button>
                <el-button-group>
                    <el-button type="primary" size="small" @click="handleDevelop(scope.row)">开发
                      <span @click.stop>
                        <el-tag v-if="scope.row.developBuildStatus=='FAILURE'" type="danger" size="mini">编译失败</el-tag>
                        <el-tag v-if="scope.row.developBuildStatus=='RUNNING'" type="warning" size="mini">编译中</el-tag>
                        <el-tag v-if="scope.row.developBuildStatus=='NEW'" type="warning" size="mini">编译未开始</el-tag>
                      </span>
                        <span v-if="scope.row.developBuildStatus=='SUCCESS'">✔</span>
                    </el-button>
                    <el-button type="primary" size="small" @click="handleRelease(scope.row)">测试</el-button>
                    <el-button type="primary" size="small" @click="handleMaster(scope.row)">合master</el-button>
                </el-button-group>
                <el-button type="primary" size="small" @click="getIngressData(scope.row)">操作log</el-button>
            </template>
        </el-table-column>
        <el-table-column type="expand" label="更多操作" width="100">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false,scope.row)">编辑</el-button>
                <el-button type="primary" size="small" @click="handleTags(scope.row)">删除tag</el-button>
                <el-button type="primary" size="small" @click="handleReleaseApi(scope.row)">发布api</el-button>
                <el-button type="primary" size="small" @click="handleResetMasterUndoOneCommit(scope.row)">回退master最后一个commit</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right;margin-bottom: 100px;" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="160px" :rules="rules" ref="addEditForm">
            <el-form-item label="* 仓库名" prop="name">
                <el-input v-model="addEditForm.name" placeholder="仓库名"></el-input>
            </el-form-item>
            <el-form-item label="* 仓库地址" prop="url">
                <el-input v-model="addEditForm.url" placeholder="仓库地址"></el-input>
            </el-form-item>
            <el-form-item label="* 用户名" prop="username">
                <el-input v-model="addEditForm.username" placeholder="用户名"></el-input>
            </el-form-item>
            <el-form-item label="* 密码">
                <el-input v-model="addEditForm.password" show-password placeholder="密码"></el-input>
            </el-form-item>
            <el-form-item label="droneBaseUrl" v-show="permission.droneBaseUrls.length>0">
                <el-select v-model="addEditForm.droneBaseUrl" placeholder="请选择" style="width: 300px">
                    <el-option v-for="item in permission.droneBaseUrls"
                               :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="API pom.xml位置" prop="apiPomLocation">
                <el-input v-model="addEditForm.apiPomLocation" placeholder="例如：common-service-api/pom.xml"></el-input>
            </el-form-item>
            <el-form-item label="序号" prop="seq">
                <el-input v-model="addEditForm.seq" placeholder="用于仓库排序，值越大越靠后，可不填"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog :title="branch.form.srcBranchName ? '复制分支'+branch.form.srcBranchName : '新增分支'"
               :visible.sync="branch.visible" top="10px">
        <div v-show="!branch.allDevelops">加载中...</div>
        <div v-show="branch.allDevelops">
            <div style="margin-left: 20px;">已存在分支列表</div>
            <ul style="margin-left: 60px">
                <li v-for="dev in branch.allDevelops">{{dev.name}}</li>
            </ul>
        </div>
        <div style="margin-top: 20px">
            <el-form :model="branch" label-position="right" label-width="80px" ref="branch">
                <el-form-item label="* 分支名">
                    <el-input v-model="branch.form.name" placeholder="分支名，不需输入develop-"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="branch.visible = false">取 消</el-button>
            <el-button type="primary" @click="doAddBranch">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="合并其它分支" :visible.sync="branch.mergeOtherBranchVisible" top="10px">
        <div v-show="!branch.allDevelops">加载中...</div>
        <div v-show="branch.allDevelops">
            <p>请选择要合并的分支</p>
            <el-checkbox-group v-model="branch.form.otherBranches">
                <el-checkbox v-for="dev in branch.allDevelops" :label="dev"></el-checkbox>
            </el-checkbox-group>
        </div>
        <div slot="footer">
            <el-button @click="branch.mergeOtherBranchVisible = false">取 消</el-button>
            <el-button type="primary" @click="doMergeOtherBranch">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="发布开发环境" :visible.sync="develop.visible" top="10" width="600">
        <div v-show="!develop.allDevelops">加载中...</div>
        <div v-show="develop.allDevelops">
            <p style="margin-bottom: 10px">请勾选发布分支</p>
            <el-checkbox-group v-model="develop.form.develops">
                <el-checkbox v-for="dev in develop.allDevelops" :label="dev">
                </el-checkbox>
            </el-checkbox-group>
        </div>

        <div slot="footer">
            <el-button type="success" plain @click="checkDevConflict(develop.form)">冲突检测</el-button>
            <el-button @click="develop.visible=false">取 消</el-button>
            <el-button type="primary" @click="doDevelop">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="发布测试环境" :visible.sync="release.visible" top="10" width="600">
        <div v-show="!release.allDevelops">加载中...</div>
        <div v-show="release.allDevelops">
            <p style="margin-bottom: 10px">请勾选发布分支</p>
            <el-checkbox-group v-model="release.form.develops">
                <el-checkbox v-for="dev in release.allDevelops" :label="dev">
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <div style="margin-top: 20px">
            <el-input v-model="release.form.tag" placeholder="tag 例：1.0.0，不需要加release前缀"></el-input>
        </div>
        <div style="margin-top: 10px">
            <div v-show="!release.allTags">加载中...</div>
            <div v-show="release.allTags">
                <p style="margin-bottom: 10px">已使用tag列表(只显示前20条，点击tag自动填写)</p>
                <ul style="margin-left: 60px">
                    <li v-for="tag in release.allTags">
                        <span @click="autoFillTag(tag.tag, tag.tagBranches)" style="cursor: hand">{{tag.tag}}
                            <span v-show="tag.tagCreateUser">({{tag.tagCreateUser}})</span></span>
                        <el-button type="warning" plain size="mini" v-show="tag.canLock"
                                   @click="doLock(release.form.id, tag.tag, tag.isLock)">{{tag.isLock ? '已锁定,点击解锁' : '锁定'}}</el-button>
                    </li>
                </ul>
            </div>
        </div>

        <div slot="footer">
            <el-button type="primary" @click="handReleaseCompare">版本对比</el-button>
            <el-button @click="release.visible=false">取 消</el-button>
            <el-button type="primary" @click="doRelease">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="合并至master" :visible.sync="master.visible" top="10" width="600">
        <div v-show="!master.allTags">加载中...</div>
        <div v-show="master.allTags">
            <p style="margin-top: 10px">请选择需要合并的tag </p>
            <div style="margin-top: 20px">
                <el-radio-group v-model="master.form.tags" @change="tagChange">
                    <el-radio v-for="tag in master.allTags" :label="tag">
                        {{tag}}
                    </el-radio>
                </el-radio-group>
            </div>
            <div style="margin-top: 20px">
                <p>tag关联到的分支</p>
                <ul style="margin-left: 60px">
                    <li v-for="branch in master.branchs">{{branch}}</li>
                </ul>
            </div>
        </div>
        <div style="margin-top: 40px">
            <template>
                <el-checkbox v-model="master.form.checked">是否自动删除分支</el-checkbox>
                <el-checkbox v-model="master.form.forceMerge">是否强制合并</el-checkbox>
            </template>
        </div>
        <div slot="footer">
            <el-button type="primary" @click="handTagCompare">版本对比</el-button>
            <el-button @click="master.visible=false">取 消</el-button>
            <el-button type="primary" @click="doMaster">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="操作记录" :visible.sync="ingress.visible" top="10" width="960" height="700">
        <div style="margin-bottom: 20px">
          <el-button type="primary" @click="getIngress('BRANCH_NEW,BRANCH_DEL,BRANCH_COPY')">分支操作</el-button>
          <el-button type="primary" @click="getIngress('DEVELOP,DEVELOP_API')">开发环境</el-button>
          <el-button type="primary" @click="getIngress('TEST,TEST_LOCK,TEST_UNLOCK,TEST_TAG_DEL')">测试环境</el-button>
          <el-button type="primary" @click="getIngress('PROD,PROD_API')">生产环境</el-button>
        </div>
        <el-table :data="ingress.tableData" border stripe v-loading.body="ingress.tableLoading">
            <el-table-column label="版本(分支)名称" min-width="60">
                <template slot-scope="props">
                    <div>{{props.row.name}}<br/>{{props.row.commitIdShow}}
                        <el-tag type="warning" v-if="props.row.comparison" size="mini" style="display: inline; cursor: hand"
                                @click.native="handCommitCompare(props.row.otherCommitId,props.row.commitId)">对比
                        </el-tag>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="版本类型" prop="type" min-width="40"></el-table-column>
            <el-table-column label="关联分支" min-width="150">
                <template slot-scope="props">
                    <ul>
                        <li v-for="o in props.row.branchList">{{o.branchName}} {{o.commitIdShow}}
                            <el-tag type="warning" v-if="o.comparison" size="mini" style="display: inline; cursor: hand"
                                    @click.native="handCommitCompare(o.otherCommitId,o.commitId)">对比
                            </el-tag>
                        </li>
                    </ul>
                </template>
            </el-table-column>
            <el-table-column label="发布时间" prop="createTime" width="110"></el-table-column>
            <el-table-column label="发布人" prop="createUser" min-width="40"></el-table-column>
        </el-table>
    ## MARK 这个分页有点问题，所以先不分页了，显示最近100条
    ##<el-pagination style="float:right" @current-change="ingressPageChange" :current-page="ingress.queryForm.page"
    ##               :total="ingress.total" :page-size="ingress.queryForm.pageSize"
    ##               layout="total, prev, pager, next, jumper" background>
    ##</el-pagination>
    </el-dialog>

    <el-dialog title="删除未合并的tag" :visible.sync="tag.visible" top="10" width="600">
        <div v-show="!tag.allTags">加载中...</div>
        <div v-show="tag.allTags">
            <p style="margin-top: 10px">请选择需要删除的tag </p>
            <div style="margin-top: 20px">
                <el-checkbox-group v-model="tag.form.tags">
                    <el-checkbox v-for="tg in tag.allTags" :label="tg">
                        {{tg}}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </div>

        <div slot="footer">
            <el-button @click="tag.visible=false">取 消</el-button>
            <el-button type="primary" @click="doTags">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="新增、刷新仓库权限" :visible.sync="permission.dialogVisible" top="10" width="600">
        <el-form ref="form" :model="permission.form" label-width="120px">
            <el-form-item label="* Drone地址">
                <el-select v-model="permission.form.droneBaseUrl" placeholder="请选择" style="width: 300px">
                    <el-option v-for="item in permission.droneBaseUrls"
                               :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="* 用户名">
                <el-input v-model="permission.form.username" placeholder="填写可以登录drone的用户名"></el-input>
            </el-form-item>
            <el-form-item label="* 密码">
                <el-input v-model="permission.form.password" placeholder="填写可以登录drone的密码" show-password></el-input>
            </el-form-item>
            <br/>
            <p>后台不会保存账号密码，所以每次需要手工刷新权限</p>
        </el-form>
        <div slot="footer">
            <el-button @click="permission.dialogVisible=false">取 消</el-button>
            <el-button type="primary" @click="doRefreshRepo">确 定</el-button>
        </div>
    </el-dialog>

    <el-dialog :visible.sync="conflict.dialogVisible"  top="10" width="1000">
        <span slot="title" style="color: red">检测出冲突</span>
        <p v-for="value in conflict.infos" style="color: red">{{value}}</p>
        <el-tabs type="border-card">
            <el-tab-pane :label="filename" v-for="(content,filename) in conflict.files">
                <pre>{{content}}</pre></el-tab-pane>
        </el-tabs>
    </el-dialog>

</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 50, showAllBranch: false}
var defaultAddForm = {}
var vm = new Vue({
    el: '#app',
    data: {
        queryForm: Utils.copy(defaultQueryForm),
        addEditForm: Utils.copy(defaultAddForm),
        rules: {
            name: Form.notBlankValidator('仓库名不能为空'),
            url: Form.notBlankValidator('仓库地址不能为空'),
            username: Form.notBlankValidator('用户不能为空'),
            password: Form.notBlankValidator('密码不能为空')
        },
        total: 0,
        tableData: [],
        tableLoading: false,
        showDialog: false,
        isForcingGetData: false, // 是否正在强制获取数据
        deletingBranch: {}, // 删除中的分支，row.id -> [branchName]
        dialogTitle: '',
        loading: true,
        conflict: {
            dialogVisible: false
        },
        permission: {
            dialogVisible: false,
            droneBaseUrls: '',
            isCheckPermission: false,
            form: {
                droneBaseUrl: '',
                username: '',
                password: ''
            }
        },
        isAutoRefreshRepo: true,
        isSyncBuildStatus: false,
        expandKeys: [], /** 新增 **/
        expands: [],
        ingress: {
            repositoryId: 0,
            queryForm: {page: 1, pageSize: 100},
            visible: false,
            tableData: [],
            total: 0,
            tableLoading: false,
        },
        branch: {
            form: {
                id: '',
                name: '',
                srcBranchName: '',
                otherBranches: []
            },
            resUrl: "",
            allDevelops: [],
            visible: false,
            mergeOtherBranchVisible: false
        },
        develop: {
            form: {
                id: '',
                develops: []
            },
            allDevelops: [],
            visible: false
        },
        release: {
            form: {
                id: '',
                name: '',
                develops: [],
                tags: []
            },
            allDevelops: [],
            allTags: [],
            visible: false
        },
        master: {
            form: {
                id: '',
                tags: '',
                checked: true,
                forceMerge: false
            },
            allTags: [],
            visible: false,
            branchs: []
        },
        tag: {
            form: {
                id: '',
                tags: []
            },
            allTags: [],
            visible: false
        }
    },
    created: function () {
        this.getData(false)
        var that = this

        setInterval(function() {
            that.syncBuildStatus()
        }, 3000)

        setInterval(function() {
            if (that.isAutoRefreshRepo) {
                that.getData(false, true)
            }
        }, 30000)
    },
    watch: {
        "queryForm.showAllBranch": function() {
            this.getData(false, false)
        }
    },
    methods: {
        getData: function (forceUpdate, dontShowTableLoading) {
            // 如果当前有强制刷新的请求，则不再发起新的请求
            if (this.isForcingGetData) {
                return
            }

            var that = this
            if (!dontShowTableLoading) {
                that.tableLoading = true
            }
            var queryForm2 = Utils.copy(this.queryForm)
            if (forceUpdate) {
                queryForm2.forceUpdate = true // 强制刷新只本次生效
                that.isForcingGetData = true
            }
            Resource.post("${_contextPath_}/git_repository/get_page", queryForm2, function (resp) {
                that.tableData = resp.data.data
                // 移除掉处于删除过程中的分支
                for (var i = 0; i < that.tableData.length; i++) {
                    var row = that.tableData[i]
                    if (that.deletingBranch[row.id]) {
                        for (var j = 0; j < that.deletingBranch[row.id].length; j++) {
                            var branchName = that.deletingBranch[row.id][j]
                            for (var k = 0; k < row.developList.length; k++) {
                                if (row.developList[k].name === branchName) {
                                    row.developList.splice(k, 1)
                                    break
                                }
                            }
                        }
                    }
                }

                that.total = resp.data.total
                that.permission.droneBaseUrls = resp.data.droneBaseUrls
                that.permission.isCheckPermission = resp.data.isCheckPermission
                that.isSyncBuildStatus = resp.data.isSyncBuildStatus
                if(that.tableData.length === 0 && that.permission.isCheckPermission) {
                    Message.confirm("系统已开启仓库权限校验，请点击页面上方[刷新权限]按钮，输入自己的drone用户名和密码，增加权限。")
                }
            }, null, function() { // finally
                if (forceUpdate) {
                    that.isForcingGetData = false
                }
                that.tableLoading = false
            }, null, dontShowTableLoading) // 如果是强制刷新，则不显示url慢提示
        },
        syncBuildStatus: function() {
            var that = this
            if(!that.isSyncBuildStatus) return
            Resource.post("${_contextPath_}/git_repository/get_build_status", this.queryForm, function (resp) {
                that.tableData.forEach(function(repo) {
                    resp.data.data.forEach(function(repo2) {
                        if(repo.id == repo2.id) {
                            repo.developBuildStatus = repo2.developBuildStatus
                            repo.developList && repo.developList.forEach(function(develop){
                                develop.tags && develop.tags.forEach(function(tag){
                                    tag.buildStatus = repo2.tagBuildStatus[tag.name]
                                })
                            })
                        }
                    })
                })
            })
        },
        pageChange: function (page) {
            this.queryForm.page = page
            this.getData(false)
        },
        handleDelete: function (row) {
            var that = this
            Message.confirm("确定要删除吗?", function () {
                Resource.get("${_contextPath_}/git_repository/delete", {id: row.id}, function () {
                    Message.success("删除成功，列表已刷新")
                    that.showDialog = false
                    that.getData(true)
                })
            })
        },
        expandChange: function (row, expandedRows) {
            if (this.expandKeys.indexOf(row.id) >= 0) {
                //收起当前行
                this.expandKeys.shift()
                return;
            }
            var that = this
            var id = row.id;
            that.loading = true
            that.expands = null;
            Resource.get('$!{_contextPath_}/git_repository/get_dev_list', {id: id}, function (resp) {
                if (row.id == id) {
                    that.expands = resp.data;
                    that.loading = false
                    that.expandKeys.shift()
                    /** 新增 **/
                    that.expandKeys.push(row.id)       /** 新增 **/
                }
            })
            if (expandedRows.length > 1) {
                //只展开当前选项
                expandedRows.shift()
            }
        },
        handleAddOrEdit: function (isAdd, row) {
            this.showDialog = true
            this.dialogTitle = isAdd ? '新增' : '编辑'
            Form.clearError(this, 'addEditForm')
            this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
        },
        doAddOrEdit: function () {
            var that = this
            var isEdit = this.addEditForm.id ? true : false
            Form.validate(this, 'addEditForm', function () {
                Resource.post("${_contextPath_}/git_repository/add_or_update", that.addEditForm, function (resp) {
                    Message.success(isEdit ? "修改成功" : "新增成功")
                    isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                    that.getData(true)
                })
            })
        },
        handleAddBranch: function (row, srcBranchName) {
            var that = this;
            this.branch.visible = true
            this.branch.allDevelops = null;
            this.branch.form = {id:row.id, name:'', srcBranchName:srcBranchName}
            Resource.get('$!{_contextPath_}/git_repository/get_dev_list', {id: row.id}, function (resp) {
                if (row.id === that.branch.form.id) {
                    that.branch.allDevelops = resp.data;
                }
            })
        },
        // 回退分支 master分支回退一个版本
        handleResetMasterUndoOneCommit: function (row) {
            this.doResetUndoOneCommit(row, "master")
        },
        // 回退分支 回退一个版本
        doResetUndoOneCommit: function (row, branchName) {
            var that = this
            Message.confirm("确定要回退分支 " + branchName + " 的最后一次commit吗?", function () {
                Resource.post('$!{_contextPath_}/git_repository/reset_branch_undo_one_commit', {
                    id: row.id, branchName: branchName
                }, function (resp) {
                    Message.success("回退最后一次commit成功，列表已刷新")
                    that.getData(true)
                })
            })
        },
        developMergeMaster: function (row, srcBranchName) {
            var that = this
            Resource.get('$!{_contextPath_}/git_repository/develop_branch_merge_master', {id: row.id, name: srcBranchName}, function (resp) {
                Message.success(srcBranchName + "合入master成功")
                that.getData(true, true)
            })
        },
        developMergeOtherBranch: function (row, srcBranchName) {
            var that = this;
            this.branch.mergeOtherBranchVisible=true
            this.branch.allDevelops = [];
            this.branch.form = {id:row.id, name:'', srcBranchName:srcBranchName, otherBranches:[]}
            // 这里可以直接从列表拿，不用请求后台用最新的分支了
            for (var i = 0; i < this.tableData.length; i++) {
                if (this.tableData[i].id === row.id) {
                    that.branch.allDevelops = this.tableData[i].developList.map(o => o.name)
                    that.branch.allDevelops = this.branch.allDevelops.filter(o => o !== srcBranchName)
                    if (that.branch.allDevelops.length === 1) {
                        that.branch.form.otherBranches = [this.branch.allDevelops[0]]
                    }
                }
            }
        },
        doMergeOtherBranch: function() {
            var that = this
            Resource.post('$!{_contextPath_}/git_repository/develop_branch_merge_other_branch', that.branch.form, function(resp) {
                Message.success("合并成功")
                // that.branch.mergeOtherBranchVisible = false // 合并分支不要自动关闭了
                that.getData(true)
            })
        },
        doAddBranch: function () {
            var that = this
            Form.validate(this, 'branch', function () {
                Resource.post("${_contextPath_}/git_repository/add_branch", that.branch.form, function (resp) {
                    Message.success("新增成功")
                    // 从删除分支中删掉
                    if (that.deletingBranch[that.branch.form.id]) {
                        that.deletingBranch[that.branch.form.id] = that.deletingBranch[that.branch.form.id].filter(function (item) {
                            return item !== "develop-" + that.branch.form.name
                        })
                    }

                    that.branch.visible = false
                    that.getData(true)
                })
            })
        },
        handleDevelop: function (row) {
            var that = this;
            var id = row.id;
            this.develop.visible = true;
            this.develop.allDevelops = null;
            this.develop.form = {id: row.id, develops: []}
            Resource.get('$!{_contextPath_}/git_repository/get_dev_list_for_develop', {id: id}, function (resp) {
                if (row.id == that.develop.form.id) {
                    that.develop.allDevelops = resp.data.develops;
                    that.develop.form.develops = resp.data.checkList;
                }
            })
        },
        jumpGitUrl: function (url) {
            window.open(url, "_blank");
        },
        doDevelop: function () {
            var that = this;
            Resource.post('$!{_contextPath_}/git_repository/deploy_develop', this.develop.form, function (resp) {
                Message.success('发布成功，已创建名称为develop的tag');
                that.develop.visible = false;
                that.getData(true)
            }, function(resp) {
                if (resp.code == 10) {
                    that.checkDevConflict(that.develop.form)
                } else {
                    Message.error(resp.msg)
                }
            })
        },
        handleRelease: function (row) {
            var that = this;
            var id = row.id;
            this.release.visible = true;
            this.release.allDevelops = null;
            this.release.allTags = null;
            this.release.form = {id: row.id, tag: '', develops: []}
            Resource.get('$!{_contextPath_}/git_repository/get_release_info', {id: id}, function (resp) {
                if (row.id == that.release.form.id) {
                    that.release.allDevelops = resp.data.developList;
                    that.release.allTags = resp.data.tagList;
                }
            })
        },
        doRelease: function () {
            var that = this;
            Resource.post('$!{_contextPath_}/git_repository/deploy_release', this.release.form
                    , function (resp) {
                        Message.success('发布成功');
                        that.release.visible = false;
                        that.getData(true);
                    }, function(resp) {
                        if (resp.code == 10) {
                            that.checkDevConflict(that.release.form)
                        } else {
                            Message.error(resp.msg)
                        }
                    })
        },
        handleMaster: function (row) {
            var that = this;
            var id = row.id;
            this.master.visible = true;
            this.master.allTags = null;
            this.master.branchs = [];
            this.master.form = {id: row.id, tags: '', checked: true}
            Resource.get('$!{_contextPath_}/git_repository/get_tag_list', {id: id}, function (resp) {
                if (row.id == that.master.form.id) {
                    that.master.allTags = resp.data;
                }
            })
        },
        tagChange: function () {
            var that = this;
            that.master.branchs = null;
            Resource.get("${_contextPath_}/git_repository/get_tag_to_list", {
                id: that.master.form.id,
                tag: that.master.form.tags
            }, function (resp) {
                that.master.branchs = resp.data;
            })
        },
        doMaster: function () {
            var that = this;
            Message.confirm("确定要合并到master吗?", function () {
                Resource.post('$!{_contextPath_}/git_repository/merge_to_master', that.master.form
                        , function (resp) {
                            Message.success('合并成功');
                            that.master.visible = false;
                            that.getData(true);
                        })
            })
        },
        handTagCompare: function () {
            var that = this;
            Resource.post('$!{_contextPath_}/git_repository/tag_compare', this.master.form
                    , function (resp) {
                        var url = resp.data.replace("github.com.cnpmjs.org", "github.com")
                        window.open(url, "_blank");
                    })
        },
        checkDevConflict: function(form) { // 检测代码冲突情况
            var that = this
            Resource.post('$!{_contextPath_}/git_repository/check_dev_conflict', form, function (resp) {
                        if (resp.data.conflict) {
                            that.conflict.dialogVisible = true
                            that.conflict.files = resp.data.files
                            that.conflict.infos = resp.data.conflictInfo
                        } else {
                            Message.success("没有检测到冲突")
                        }
                    })
        },
        handleDevCompareDirect: function (row, oldCommitId, newCommitId) {
            var url = row.url;
            var index = url.lastIndexOf('.git');
            if (index != -1) {
                url = url.substring(0, index);
            }
            url += "/compare/" + oldCommitId + "..." + newCommitId;
            url = url.replace("github.com.cnpmjs.org", "github.com")
            window.open(url, "_blank");
        },
        handReleaseCompare: function () {
            var that = this;
            Resource.post('$!{_contextPath_}/git_repository/release_compare', this.release.form
                    , function (resp) {
                        var url = resp.data.replace("github.com.cnpmjs.org", "github.com")
                        window.open(url, "_blank");
                    })
        },
        doDeleteBranch: function (row, name) {
            var that = this
            Message.confirm("确定要删除分支" + name + "吗?", function () {
                if (!that.deletingBranch[row.id]) {
                    that.deletingBranch[row.id] = []
                }
                if (!that.deletingBranch[row.id].includes(name)) {
                    that.deletingBranch[row.id].push(name)
                }

                Resource.post("${_contextPath_}/git_repository/delete_branch", {
                    id: row.id,
                    name: name
                }, function () {
                    that.getData(true, true)
                }, function(resp) {
                    Message.alert("删除分支" + name + "失败，错误码:" + resp.code + ",错误信息:" + resp.msg);
                }, function() { // finally
                    // 延迟10秒移除分支，一般强制刷新需要大概10秒，这个时间一般也不会再次创建分支；这里在创建分支那里还有兜底，所以没有问题
                    setTimeout(function() {
                        if (that.deletingBranch[row.id]) {
                            that.deletingBranch[row.id] = that.deletingBranch[row.id].filter(function (item) {
                                return item !== name
                            })
                        }
                    }, 10000);
                }, null, true)

                Message.success("已提交删除，并自动删除分支，如删除失败会通知，如删除成功不再通知")
                for (var i = 0; i < that.tableData.length; i++) {
                    if (that.tableData[i].id === row.id) {
                        that.tableData[i].developList = that.tableData[i].developList.filter(function (item) {
                            return item.name !== name
                        })
                        break;
                    }
                }
            })
        },
        /*发布分支临时API*/ doReleaseSnapshotApi: function (row, name) {
            var that = this
            var param = {
                id: row.id,
                name: name
            }
            Resource.get("${_contextPath_}/git_repository/get_branch_api_ver", param,
                function(resp) {
                    if(!resp.data.isConfApiPomLocation) {
                        Message.error("该仓库尚未配置pom.xml位置，请先编辑仓库")
                        return;
                    }
                    if(!resp.data.isSnapshot) {
                        Message.confirm("该分支pom.xml发布版本当前为" + resp.data.mavenVersion + "，请修改为-SNAPSHOT版本")
                        return;
                    }
                    Message.confirm("确定要发布" + resp.data.mavenVersion + "吗?", function () {
                        Resource.get("${_contextPath_}/git_repository/release_branch_snapshot_api", param, function () {
                            Message.success("发布成功")
                        })
                    })
                }
            )
        },
        getIngressData: function (row) {
            var that = this
            var id = 0
            if (!row) {
                id = that.ingress.repositoryId
            } else {
                id = row.id
                that.ingress.repositoryId = row.id
            }
            that.ingress.visible = true
            that.ingress.tableLoading = true
            that.ingress.tableData = null;
            that.ingress.total = 0
            Resource.post("${_contextPath_}/git_ingress/get_page", {
                repositoryId: id, page: that.ingress.queryForm.page,
                pageSize: that.ingress.queryForm.pageSize
            }, function (resp) {
                that.ingress.tableData = resp.data.data
                that.ingress.total = resp.data.total
                that.ingress.tableLoading = false
            })
        },
        getIngress: function (type) {
            var that = this
            var id = that.ingress.repositoryId
            Resource.post("${_contextPath_}/git_ingress/get_page", {
                repositoryId: id,
                type: type,
                page: that.ingress.queryForm.page,
                pageSize: that.ingress.queryForm.pageSize
            }, function (resp) {
                that.ingress.tableData = resp.data.data
                that.ingress.total = resp.data.total
                that.ingress.tableLoading = false
            })
        },
        handCommitCompare: function (commitId, otherCommitId) {
            var that = this
            var id = that.ingress.repositoryId
            Resource.post('$!{_contextPath_}/git_ingress/commit_compare', {
                        repositoryId: id,
                        commitId: commitId,
                        otherCommitId: otherCommitId
                    }
                    , function (resp) {
                        var url = resp.data.replace("github.com.cnpmjs.org", "github.com")
                        window.open(resp.data, "_blank");
                    })
        },
        ingressPageChange: function (page) {
            this.ingress.queryForm.page = page
            this.getIngressData()
        },
        doCompareMaster: function (row, name) {
            var url = row.url;
            var index = url.lastIndexOf('.git');
            if (index != -1) {
                url = url.substring(0, index);
            }
            url += "/compare/master..." + name;
            url = url.replace("github.com.cnpmjs.org", "github.com")
            window.open(url, "_blank");
        },
        handleTags: function (row) {
            var that = this;
            var id = row.id;
            this.tag.visible = true;
            this.tag.allTags = null;
            this.tag.form = {id: row.id, tags: []}
            Resource.get('$!{_contextPath_}/git_repository/get_tag_list', {id: id}, function (resp) {
                if (row.id == that.tag.form.id) {
                    that.tag.allTags = resp.data;
                }
            })
        },
        doTags: function () {
            var that = this;
            Resource.post('$!{_contextPath_}/git_repository/delete_tag', this.tag.form
                    , function (resp) {
                        Message.success('发布成功，列表已刷新');
                        that.tag.visible = false;
                        that.getData(true)
                    })
        },
        /*发布正式的API*/ handleReleaseApi: function (row) {
            var that = this
            var params = {id: row.id}
            Resource.get("$!{_contextPath_}/git_repository/query_release_api", params, function(resp){
                if(!resp.data.isConfApiPomLocation) {
                    Message.error("该仓库尚未配置pom.xml位置，请先编辑仓库")
                    return;
                }
                if(!resp.data.isRelease) {
                    Message.confirm("该分支pom.xml发布版本不是正式版本，当前为" + resp.data.mavenVersion + "，请修改为正式版本")
                    return;
                }
                Message.confirm("确定要发布api版本" + resp.data.mavenVersion +"吗?", function () {
                    Resource.post('$!{_contextPath_}/git_repository/release_api', params, function (resp) {
                        Message.success('发布成功')
                    })
                })
            })
        },

        autoFillTag: function (tag, tagBranches) {
            var that = this
            this.release.form.tag = tag.substring(8)
            if (tagBranches.length>0) {
                // 只能匹配release.allDevelops里有的
                this.release.form.develops = tagBranches.filter(a => that.release.allDevelops.includes(a))
            }
        },
        doRefreshRepo: function() {
            var that = this
            Resource.post('$!{_contextPath_}/git_repo_permission/do_refresh', that.permission.form,
                function(resp) {
                    Message.success('刷新成功')
                    that.permission.dialogVisible = false
                    that.getData(false)
                })
        },
        branchMoreValue: function (command,row,branchName) {
            return {
                command: command,
                row: row,
                branchName: branchName
            }
        },
        branchMore: function(command) {
            if (command.command == 'mergeMaster') {
                this.developMergeMaster(command.row, command.branchName)
            }
            if (command.command == 'mergeOtherBranch') {
                this.developMergeOtherBranch(command.row, command.branchName)
            }
            if(command.command == 'delete') {
                this.doDeleteBranch(command.row, command.branchName)
            }
            if(command.command == 'snapshotAPI') {
                this.doReleaseSnapshotApi(command.row, command.branchName)
            }
            if(command.command == 'copy') {
                this.handleAddBranch(command.row, command.branchName)
            }
            if(command.command == 'resetOneCommit') {
                this.doResetUndoOneCommit(command.row, command.branchName)
            }
        },
        doJumpBranch: function(row, name) {
            var url = row.url
            if (url.endsWith(".git")) {
                url = url.substring(0, url.length-4)
            }
            url = url.replace("github.com.cnpmjs.org", "github.com")
            window.open(url + "/tree/" + name, "_blank");
        },
        doLock: function(repoId, tag, oldLockStatus) {
            var that = this
            var isLock = oldLockStatus ? false : true
            Resource.post('$!{_contextPath_}/git_repository/lock_release', {
                id: repoId, tag: tag, isLock: isLock
            }, function(resp) { // refresh tag info
                Resource.get('$!{_contextPath_}/git_repository/get_release_info',
                        {id: repoId}, function (resp) {
                    if (repoId == that.release.form.id) {
                        that.release.allTags = resp.data.tagList;
                    }
                })
                that.getData(false)
            })
        },
        switchAutoMergeMaster: function(value, repoId, branchName) {
            Resource.post('$!{_contextPath_}/git_repository/switch_auto_merge_master', {
                id: repoId, branchName: branchName, enable: value
            }, function(resp) {
                // 不提示任何信息
            })
        }
    }
})
</script>
