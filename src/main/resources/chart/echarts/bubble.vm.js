var dom = document.getElementById('chart-container');
var myChart = echarts.init(dom, null, {
    renderer: 'canvas',
    useDirtyRect: false
});
var app = {};

var option;

option = {
    title: {
        text: '$TITLE',
        left: '5%',
        top: '3%'
    },
    legend: {
        right: '10%',
        top: '3%',
        data: $legend
    },
    grid: {
        left: '8%',
        top: '10%'
    },
    xAxis: {
        splitLine: {
            lineStyle: {
                type: 'dashed'
            }
        },
        scale: true,
        #if ($xMin != $null)
        min: $xMin,
        #end
        #if ($xMax != $null)
        max: $xMax,
        #end
    },
    yAxis: {
        splitLine: {
            lineStyle: {
                type: 'dashed'
            }
        },
        scale: true,
        #if ($yMin != $null)
        min: $yMin,
        #end
        #if ($yMax != $null)
        max: $yMax,
        #end
    },
    series: [
        #foreach ($item in $SERIES)
        {
            name: '$item.name',
            data: $item.data,
            type: 'scatter',
            symbolSize: function (data) {
                return Math.sqrt(data[2]) * $item.pointZoom;
            },
            label: {
                show: true,
                formatter: '{@[3]}',
                position: 'right',
                color: 'gray'
            },
            labelLayout: {
                hideOverlap: true // 自动隐藏重叠的label
            },
            emphasis: {
                focus: 'series',
                label: {
                    show: true,
                    formatter: function (param) {
                        return param.data[3];
                    },
                    position: 'right'
                }
            }
        },
        #end
    ]
};


if (option && typeof option === 'object') {
    myChart.setOption(option);
}

window.addEventListener('resize', myChart.resize);