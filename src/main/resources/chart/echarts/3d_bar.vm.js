var dom = document.getElementById("chart-container");
var myChart = echarts.init(dom, null, {
    renderer: "canvas",
    useDirtyRect: false,
});
var app = {};

var option;

// x轴用于时间日期
var x = $DATE_ARRAY; // ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'];
// y轴用于维度
var y = $DIM_ARRAY; // ['', ''];
// 这里[a,b,c] 中的a对应y轴的顺序，从0开始，b对应x轴的顺序，从0开始，c是数值
var data =  $DATA_ARRAY; // [[0, 0, 144576], [1, 0, 129894],];

option = {
    title: {
        text: '$TITLE',
        left: 'center'
    },
    tooltip: {},
    visualMap: {
        max: $MAX_VALUE,
        inRange: {
            color: [
                "#abd9e9",
                "#ffffbf",
                "#fee090",
                "#fdae61",
                "#f46d43",
                "#d73027",
                "#a50026",
            ],
        },
    },
    xAxis3D: {
        type: "category",
        data: x,
    },
    yAxis3D: {
        type: "category",
        data: y,
    },
    zAxis3D: {
        type: "value",
    },
    grid3D: {
        boxWidth: $BOX_WIDTH, // 场地的宽度
        boxDepth: $BOX_DEPTH, // 场地的深度
        light: {
            main: {
                intensity: 1.2,
            },
            ambient: {
                intensity: 0.3,
            },
        },
        viewControl: {
            distance: $DISTANCE, // 视角距离
            maxDistance: $MAX_DISTANCE,
            alpha: $HEIGHT_ANGLE // 调整一个比较合适的高度视角
        },
    },
    series: [
        {
            type: "bar3D",
            data: data.map(function (item) {
                return {
                    value: [item[1], item[0], item[2]],
                };
            }),
            shading: "color",
            label: {
                show: false,
                fontSize: 16,
                borderWidth: 1,
            },
            itemStyle: {
                opacity: 0.85, // 透明度
            },
            emphasis: {
                label: {
                    fontSize: 20,
                    color: "#900",
                },
                itemStyle: {
                    color: "#900",
                },
            },
            barSize: $BAR_SIZE, // 调整柱子的宽度
        },
    ],
};

if (option && typeof option === "object") {
    myChart.setOption(option);
}

window.addEventListener("resize", myChart.resize);
