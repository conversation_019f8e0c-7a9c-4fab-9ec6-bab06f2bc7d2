<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <title>#if($TITLE)$TITLE#else TradingVueJs Demo #end</title>
    <script src="https://unpkg.com/vue@2.6.12/dist/vue.min.js"></script>
    <script src="/js/trading-vue.min.js"></script>
    <script>
        Data = {
            "ohlcv": $OHLCV_DATA,
            "onchart": $ONCHART_DATA,
            "offchart": $OFFCHART_DATA
        }
    </script>
    <style>
        html,
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .night-mode {
            position: absolute;
            top: 10px;
            right: 80px;
            color: #888;
            font: 11px -apple-system,BlinkMacSystemFont,
            Se<PERSON><PERSON>,Roboto,Oxygen,Ubuntu,Can<PERSON>ell,
            <PERSON><PERSON>,Droid Sans,Helvetica Neue,
            sans-serif
        }
    </style>
</head>


<body>
<div id="app">
    <!-- index based设置为true可以让周末没有交易的时间不会出现gap -->
    <trading-vue :data="data" :width="this.width" :height="this.height"
                 :toolbar="true"
                 :index-based="true"
                 :chart-config="{TB_ICON_BRI: 1.25}"
                 :color-back="colors.colorBack"
                 :color-grid="colors.colorGrid"
                 :color-text="colors.colorText">
    </trading-vue>
    <span class="night-mode">
            <input type="checkbox" v-model="night">
            <label>NM</label>
        </span>
</div>
<script>
    app = new Vue({
        el: '#app',
        data: {
            data: new TradingVueJs.DataCube(Data),
            width: window.innerWidth,
            height: window.innerHeight,
            night: false
        },
        mounted() {
            window.addEventListener('resize', this.onResize)
            window.DataCube = this.data
        },
        methods: {
            onResize(event) {
                this.width = window.innerWidth
                this.height = window.innerHeight
            }
        },
        computed: {
            colors() {
                return this.night ? {} : {
                    colorBack: '#fff',
                    colorGrid: '#eee',
                    colorText: '#333'
                }
            },
        },
        beforeDestroy() {
            window.removeEventListener('resize', this.onResize)
        },
    })
</script>
</body>
