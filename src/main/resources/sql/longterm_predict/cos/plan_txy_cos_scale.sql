select DATE_FORMAT(STR_TO_DATE(data_date, '%Y%m%d'), '%Y-%m-%d') as date,
       (case when category_name_2 like '%内部%' then '内部' else '外部' end) as scope,
       sum(amount) as value
from end_to_end_provide.cloud_end_to_end_utilization
where cloud_product_name_1='COS'
  and cloud_product_id_1=4 /*腾讯云COS*/
  and category_name_1='售卖规模'
  and data_date>=20220701
group by data_date,category_name_2
order by data_date,category_name_2