<!-- ${table.allLowClassName}.vm -->

\#set($page_title='页面标题')

<style>
</style>

<div id="app" v-cloak>
<el-form :inline="true" @keyup.native.enter="getData">
#foreach($col in $columns)
#if($col.key)
  <el-form-item label="$!col.javaVarName">
    <el-input v-model="queryForm.$col.javaVarName" placeholder="仅示例，后台未实现"></el-input>
  </el-form-item>
#end
#end
  <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
  <el-form-item>
    <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
    <el-button @click="resetQuery">重置</el-button>
    <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
  </el-form-item>
</el-form>

<el-table :data="tableData" border stripe v-loading.body="tableLoading">
#foreach($col in $columns)
#if(!$table.useBaseDO || $table.useBaseDO && !($col.name=='deleted'))
#if($col.javaType=='Boolean')
    <el-table-column label="$!col.javaVarName">
      <template slot-scope="scope">
        <span v-text="scope.row.$col.javaVarName ? 'true' : 'false'"></span>
      </template>
    </el-table-column>
#else
    <el-table-column prop="$col.javaVarName" label="$!col.javaVarName"></el-table-column>
#end
#end
#end
    <el-table-column label="操作">
      <template slot-scope="scope">
        <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
      </template>
    </el-table-column>
</el-table>

<el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
     :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
</el-pagination>

<el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
    <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
#foreach($col in $columns)
#if(!$col.key)
#if(!$table.useBaseDO || $table.useBaseDO && !$col.isBaseColumn)
        <el-form-item label="$!col.javaVarName" prop="$col.javaVarName">
            <el-input v-model="addEditForm.$col.javaVarName" placeholder="$!col.comment"></el-input>
        </el-form-item>
#end
#end
#end
    </el-form>
    <div slot="footer">
        <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="doAddOrEdit">确定</el-button>
    </div>
</el-dialog>

</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 10}
var defaultAddForm = {}
var vm = new Vue({
    el: '#app',
    data: {
      queryForm: Utils.copy(defaultQueryForm),
      addEditForm: Utils.copy(defaultAddForm),
      rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
      total: 0, tableData: [], tableLoading: false,
      showDialog: false, dialogTitle: ''
    },
    created: function() {
      this.getData()
    },
    methods: {
      getData: function() {
        var that = this
        that.tableLoading = true
        Resource.get("${_contextPath_}/${table.allLowClassName}/get_page", this.queryForm, function(resp){
          that.tableData = resp.data.data
          that.total = resp.data.total
          that.tableLoading = false
        })
      },
      pageChange: function(page) {
        this.queryForm.page = page
        this.getData()
      },
      resetQuery: function() {
        this.queryForm = Utils.copy(defaultQueryForm)
      },
      handleDelete: function(row) {
        var that = this
        Message.confirm("确定要删除吗?", function(){
          Resource.post("${_contextPath_}/${table.allLowClassName}/delete", {#foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end${col.javaVarName}: row.${col.javaVarName}#end#end}, function(){
            that.showDialog = false
            Message.success("删除成功，列表已刷新")
            that.getData()
          })
        })
      },
      handleAddOrEdit: function(isAdd, row) {
        this.showDialog = true
        this.dialogTitle = isAdd ? '新增$!table.comment' : '编辑'
        Form.clearError(this, 'addEditForm')
        this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
      },
      doAddOrEdit: function() {
        var that = this
        var isEdit = #foreach($col in $columns)#if($col.key)#if($foreach.count>1) && #end this.addEditForm.${col.javaVarName}#end#end ? true : false
        Form.validate(this, 'addEditForm', function() {
          Resource.post("${_contextPath_}/${table.allLowClassName}/add_or_update", that.addEditForm, function(resp){
            Message.success(isEdit ? "修改成功" : "新增成功")
            isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
            that.getData()
          })
        })
      }
    }
})
</script>