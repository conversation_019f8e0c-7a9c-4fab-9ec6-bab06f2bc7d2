monitors:
  - name: 探活
    code: HEALTH
    monitorSql: select 1 as count
    intervalSecond: 10
    succLogRateSecs: 3600
    assertExpression: |
      rows[0].count.longValue() == 1

  - name: 检查DDL等待队列
    code: WAITING_DDL_QUEUE
    monitorSql: |
      SELECT count(*) as cnt FROM system.distributed_ddl_queue where status='Active' and query_create_time < now()-toIntervalSecond(60)
    intervalSecond: 600
    succLogRateSecs: 7200
    detailSql: |
      SELECT * FROM system.distributed_ddl_queue where status='Active' and query_create_time < now()-toIntervalSecond(60);
      select * from system.mutations where is_done=0;
    assertExpression: |
      rows[0].cnt.longValue() == 0