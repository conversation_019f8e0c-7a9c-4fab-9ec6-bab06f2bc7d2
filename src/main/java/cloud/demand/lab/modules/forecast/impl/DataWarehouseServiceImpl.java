package cloud.demand.lab.modules.forecast.impl;

import cloud.demand.lab.common.entity.NullNotEqual;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.utils.EnvUtils;
import cloud.demand.lab.common.utils.ListUtils2;
import cloud.demand.lab.common.utils.ObjectUtil;
import cloud.demand.lab.modules.forecast.DO.DwdCrpLongtailForecastItemDfDO;
import cloud.demand.lab.modules.forecast.DO.PplForecastInputDetailDO;
import cloud.demand.lab.modules.forecast.DO.PplForecastPredictResultWithTaskVO;
import cloud.demand.lab.modules.forecast.DO.PplForecastTaskInputDO;
import cloud.demand.lab.modules.forecast.DataWarehouseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.redis.Synchronized;
import io.vavr.collection.Stream;
import java.math.BigDecimal;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class DataWarehouseServiceImpl implements DataWarehouseService {

    @Resource
    DataWarehouseServiceImpl self;

    ExecutorService executorService = Executors.newFixedThreadPool(4);

    @Override
    @Synchronized
    public Map<String, Integer> genCkDWH() {
        List<PplForecastTaskInputDO> allTask = PplForecastTaskInputDO.db().getAll();

        List<String> categories = allTask.stream()
                .filter((o) -> o.getSerialInterval().equals("MONTH"))
                .map(PplForecastTaskInputDO::getCategory)
                .distinct()
                .collect(Collectors.toList());

        return categories.stream()
                .map(category -> CompletableFuture.supplyAsync(() -> {
                    int value = self.genCkDWH(category);
                    return new AbstractMap.SimpleEntry<>(category, value);
                }, executorService))
                .collect(Collectors.toList())
                .stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    @TaskLog
    @Synchronized(keyScript = "args[0]")
    public int genCkDWH(String categoryPrefix) {


        // 第一步，获取数据数据源
        OriginData result = getOriginData(categoryPrefix);
        if (result == null) {
            return 0;
        }
        log.info("** deal category: {} **", result.maxTask.getCategory());

        List<DwdCrpLongtailForecastItemDfDO> insertData;

        // 第二步，关联一些数据表， 默认6个月，否则就是周的，使用13周
        boolean isNotMonth = !result.maxTask.getSerialInterval().equals("MONTH");
        Stream<Integer> range = Stream.range(1, isNotMonth ? 14 : 7);

        insertData = ListUtils2.crossJoin(range, result.allReal,
                (a, b) -> {
                    DwdCrpLongtailForecastItemDfDO clone = a == 1 ? b : JSON.clone(b);
                    clone.setPredictIndex(a);
                    return clone;
                });

        // 关联预测数据
        insertData = ListUtils2.fullOuterJoin(
                insertData, JoinForecastKey::from,
                result.allPredict, JoinForecastKey::from,
                (key, real, pred) -> {
                    DwdCrpLongtailForecastItemDfDO tmp = real.orElseGet(pred::get);
                    if (real.isPresent() && pred.isPresent()) {
                        tmp.setPredictCoreNum(pred.get().getPredictCoreNum());
                    }
                    tmp.setMarkNumber((real.isPresent() ? 1 : 0) + (pred.isPresent() ? 10 : 0));
                    return tmp;
                });

        // 月的数据来设置 532 的值
        if (!isNotMonth) {
            // 关联预测数据，减去1期的数据
            insertData = ListUtils2.leftJoin(
                    insertData, JoinForecastKey::from,
                    result.allPredict, (o) -> {
                        JoinForecastKey key = JoinForecastKey.from(o);
                        key.setPredictIndex(o.getPredictIndex() + 1);
                        return key;
                    }, (key, left, right) -> {
                        right.ifPresent(i -> left.setPredictCoreNum1(i.getPredictCoreNum()));
                        right.ifPresent(i -> left.addMarkNumber(100));
                        return left;
                    });

            // 标记上 532 的数据
            insertData = ListUtils2.leftJoin(
                    insertData, JoinForecastKey::from,
                    result.allPredict, (o) -> {
                        JoinForecastKey key = JoinForecastKey.from(o);
                        key.setPredictIndex(o.getPredictIndex() + 2);
                        return key;
                    }, (key, left, right) -> {
                        right.ifPresent(i -> left.setPredictCoreNum2(i.getPredictCoreNum()));
                        right.ifPresent(i -> left.addMarkNumber(1000));
                        right.ifPresent(i -> {
                            if (left.getMarkNumber() != null && ((left.getMarkNumber() / 100) % 10) != 0) {
                                BigDecimal num532 = left.getPredictCoreNum().multiply(new BigDecimal("0.5"))
                                        .add(left.getPredictCoreNum1().multiply(new BigDecimal("0.3")))
                                        .add(left.getPredictCoreNum2().multiply(new BigDecimal("0.2")));
                                left.setPredictCoreNum532(num532);
                            }
                        });
                        return left;
                    });

        }
        ObjectUtil.setEmptyStringFieldsToNull(insertData);

        // delete
        DBHelper testInProdSwapDBHlper = DwdCrpLongtailForecastItemDfDO.dbTestInProdSwap().getDbHelper();
        String dropPartition = "ALTER TABLE dwd_crp_longtail_forecast_item_df_local ON CLUSTER default_cluster DROP PARTITION ?";
        String category = result.allTask.get(0).getCategory();
        testInProdSwapDBHlper.executeRaw(dropPartition, category);
        log.info("delete exist data");
        // insert
        int insertSize = DwdCrpLongtailForecastItemDfDO.dbTestInProdSwap().insertBatchWithoutReturnId(insertData);
        log.info("dwd_crp_longtail_forecast_item_df insert size: {}", insertSize);
        // sleep, 确保数据一样之后再replace
        int cnt = 0 ;
        while (cnt++ < 10) {
            DwdCrpLongtailForecastItemDfDO.dbTestInProdSwap().executeRaw("SELECT sleep(1)");
            long count = DwdCrpLongtailForecastItemDfDO.dbTestInProdSwap().getCount("where category=?", category);
            if (count == insertSize) {
                break;
            }
        }

        // replace
        boolean production = EnvUtils.isProduction();
        String dbName = production ? "std_crp" : "std_crp_forecast_test";
        String dbSwapName = production ? "std_crp_swap" : "std_crp_forecast_test_swap";
        String sqlTemp = "ALTER TABLE `%s`.%s on cluster default_cluster REPLACE PARTITION ? FROM `%s`.%s";
        String tableName = "`dwd_crp_longtail_forecast_item_df_local`";
        String replaceSql = String.format(sqlTemp, dbName, tableName,dbSwapName, tableName);
        DwdCrpLongtailForecastItemDfDO.dbTestInProdSwap().executeRaw(replaceSql,category);
        return insertSize;
    }

    private static OriginData getOriginData(String categoryPrefix) {
        // 先获取月的数据
        List<PplForecastTaskInputDO> allTask = PplForecastTaskInputDO.db()
                .getAll("where category like  concat('%',?,'%')  and is_enable=1", categoryPrefix);
        // 处理月的数据
        if (allTask.isEmpty() || !allTask.get(0).getSerialInterval().equals("MONTH")) {
            return null;
        }
        PplForecastTaskInputDO maxTask = allTask.stream()
                .filter(Objects::nonNull)
                .max(PplForecastTaskInputDO.orderByPredictMonthDesc)
                .orElseThrow(() -> BizException.makeThrow("task 未找到"));

        List<PplForecastInputDetailDO> allRealData = PplForecastInputDetailDO.db()
                .getAll("where task_id=?", maxTask.getTaskId());

        List<Long> allTaskIds = allTask.stream().map(PplForecastTaskInputDO::getTaskId).collect(Collectors.toList());
        List<PplForecastPredictResultWithTaskVO> allPredictMysql = PplForecastPredictResultWithTaskVO.db1()
                .getAll("where task_id in(?)", allTaskIds);

        // 转换为插入数据，这部分数据还有没有关联一些表
        List<DwdCrpLongtailForecastItemDfDO> allReal = allRealData.stream().map((o) -> {
            DwdCrpLongtailForecastItemDfDO target = DwdCrpLongtailForecastItemDfDO.transFromTask(maxTask);
            DwdCrpLongtailForecastItemDfDO.copyTo(o, target);
            // 设置关联的信息
            target.applyMapValue();
            return target;
        }).collect(Collectors.toList());

        List<DwdCrpLongtailForecastItemDfDO> allPredict = allPredictMysql.stream().map((o) -> {
            DwdCrpLongtailForecastItemDfDO target = DwdCrpLongtailForecastItemDfDO.transFromTask(maxTask);
            DwdCrpLongtailForecastItemDfDO.copyTo(o, target);
            // 设置关联的信息
            target.applyMapValue();
            return target;
        }).collect(Collectors.toList());
        return new OriginData(allTask, maxTask, allReal, allPredict);
    }

    @AllArgsConstructor
    private static class OriginData {

        public final List<PplForecastTaskInputDO> allTask;
        public final PplForecastTaskInputDO maxTask;
        public final List<DwdCrpLongtailForecastItemDfDO> allReal;
        public final List<DwdCrpLongtailForecastItemDfDO> allPredict;
    }

    @Data
    @ToString
    @Setter
    static class JoinForecastKey implements NullNotEqual {

        private Integer year;
        private Integer month;
        private String ginsFamily;
        private String regionName;
        private String seqType;
        private String category;
        private String sourceType;
        private Integer predictIndex;

        // 注意overWrite 了 key ！！！
        @SneakyThrows
        @Override
        @SuppressWarnings("EqualsWhichDoesntCheckParameterClass")
        public boolean equals(Object o) {
            return reflectiveEquals(o);
        }

        @SneakyThrows
        @Override
        public int hashCode() {
            return reflectiveHashCode();
        }

        public static JoinForecastKey from(PplForecastPredictResultWithTaskVO source) {
            JoinForecastKey joinForecastKey = new JoinForecastKey();
            joinForecastKey.setYear(source.getYear());
            joinForecastKey.setMonth(source.getMonth());
            joinForecastKey.setGinsFamily(source.getGinsFamily());
            joinForecastKey.setRegionName(source.getRegionName());
            joinForecastKey.setSeqType(source.getType());
            joinForecastKey.setCategory(source.getTask().getCategory());
            joinForecastKey.setSourceType(source.getTask().getSourceType());
            joinForecastKey.setPredictIndex(source.getPredictIndex());
            return joinForecastKey;
        }

        public static JoinForecastKey from(DwdCrpLongtailForecastItemDfDO source) {
            JoinForecastKey joinForecastKey = new JoinForecastKey();
            joinForecastKey.setYear(source.getYear());
            joinForecastKey.setMonth(source.getMonth());
            joinForecastKey.setGinsFamily(source.getGinsFamily());
            joinForecastKey.setRegionName(source.getRegionName());
            joinForecastKey.setSeqType(source.getSeqType());
            joinForecastKey.setCategory(source.getCategory());
            joinForecastKey.setSourceType(source.getSourceType());
            joinForecastKey.setPredictIndex(source.getPredictIndex());
            return joinForecastKey;
        }
    }


}
