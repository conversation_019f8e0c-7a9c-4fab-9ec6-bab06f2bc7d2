package cloud.demand.lab.modules.forecast.DO;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_forecast_predict_result")
@JsonIgnoreProperties(ignoreUnknown = true)
public class PplForecastPredictResultWithTaskVO extends PplForecastPredictResultDO{

    public static DBHelperWithClz<PplForecastPredictResultWithTaskVO> db1(){
        return DBHelperWithClz.create(DBList.demandDBHelper);
    }

    @RelatedColumn(localColumn = "task_id", remoteColumn = "task_id")
    private PplForecastTaskInputDO task;


}