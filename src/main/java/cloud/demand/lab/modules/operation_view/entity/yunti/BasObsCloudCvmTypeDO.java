package cloud.demand.lab.modules.operation_view.entity.yunti;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("bas_obs_cloud_cvm_type")
public class BasObsCloudCvmTypeDO {

    /** 规格ID<br/>Column: [CvmInstanceId] */
    @Column(value = "CvmInstanceId", isKey = true, isAutoIncrement = true)
    private Integer cvmInstanceId;

    /** 实例族<br/>Column: [CvmInstanceGroup] */
    @Column(value = "CvmInstanceGroup")
    private String cvmInstanceGroup;

    /** 实例类型<br/>Column: [CvmInstanceType] */
    @Column(value = "CvmInstanceType")
    private String cvmInstanceType;

    /** 实例类型简称<br/>Column: [CvmInstanceTypeCode] */
    @Column(value = "CvmInstanceTypeCode")
    private String cvmInstanceTypeCode;

    /** 实例规格<br/>Column: [CvmInstanceModel] */
    @Column(value = "CvmInstanceModel")
    private String cvmInstanceModel;

    /** Cpu核数(核)<br/>Column: [CpuAmount] */
    @Column(value = "CpuAmount")
    private Integer cpuAmount;

    /** 内存量(G)<br/>Column: [RamAmount] */
    @Column(value = "RamAmount")
    private Integer ramAmount;

}
