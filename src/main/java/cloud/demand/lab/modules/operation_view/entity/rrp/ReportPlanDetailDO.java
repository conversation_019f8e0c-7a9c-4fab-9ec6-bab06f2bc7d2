package cloud.demand.lab.modules.operation_view.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 *  PLAN明细表
 */
@Data
@ToString
@Table("report_plan_detail")
public class ReportPlanDetailDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isAutoIncrement = true, isKey = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 处理器类型，CPU/GPU<br/>Column: [compute_type] */
    @Column(value = "compute_type", insertValueScript = "''")
    private String computeType;

    /** 母机机型<br/>Column: [device_name] */
    @Column(value = "device_type", insertValueScript = "''")
    private String deviceName;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type", insertValueScript = "''")
    private String instanceType;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type", insertValueScript = "''")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform", insertValueScript = "''")
    private String cpuPlatform;

    /** 指标大类<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 指标子类<br/>Column: [sub_category] */
    @Column(value = "sub_category")
    private String subCategory;

    /** 指标代码<br/>Column: [indicator_code] */
    @Column(value = "indicator_code")
    private String indicatorCode;

    /** 指标名称<br/>Column: [indicator_name] */
    @Column(value = "indicator_name")
    private String indicatorName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name", insertValueScript = "''")
    private String zoneName;

    /** 地域名<br/>Column: [region_name] */
    @Column(value = "region_name", insertValueScript = "''")
    private String regionName;

    /** 地区名<br/>Column: [area_name] */
    @Column(value = "area_name", insertValueScript = "''")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title", insertValueScript = "''")
    private String customhouseTitle;

    /** 指标值<br/>Column: [indicator_value] */
    @Column(value = "cores", insertValueScript = "'0.000000'")
    private BigDecimal cores;

    /** 母机对应的逻辑核心数<br/>Column: [logic_cpu_core] */
    @Column(value = "logic_cpu_core", insertValueScript = "'0.000000'")
    private BigDecimal logicCpuCore;

    /** 母机台数<br/>Column: [num] */
    @Column(value = "num", insertValueScript = "'0.000000'")
    private BigDecimal num;

    /** 逻辑数 随着产品类型不同单位会变化<br/>Column: [logic_num] */
    @Column(value = "logic_num", insertValueScript = "'0.000000'")
    private BigDecimal logicNum;

    /** 逻辑数单位<br/>Column: [logic_unit] */
    @Column(value = "logic_unit", insertValueScript = "''")
    private String logicUnit;

    /** 弹性规模核心数<br/>Column: [buffer_cores] */
    @Column(value = "buffer_cores")
    private BigDecimal bufferCores;

}
