package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryReasonDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.OutsideHealthService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.config.DynamicProperty;

@Slf4j
@Service
public class OutsideHealthServiceImpl implements OutsideHealthService {
    @Autowired
    JsonrpcClient jsonrpcClient;
    @Override
    public Map<String, List<InventoryReasonDO>> listRangeReasonsParallelByInstanceTypeAndZoneName(String startDate,
            String endDate) {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // 连接超时：10秒
                .readTimeout(30, TimeUnit.SECONDS)   // 读取超时：10秒
                .build();
        DynamicProperty<String> crpUrl = DynamicProperty.create("crpUrl", "");
        String url = String.format("%s/cloud-demand-app/rpc/listRangeReasonsParallel?api_key=no",
                crpUrl.get());
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        body.put("params", params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),
                body.toJSONString());
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        Map<String, List<InventoryReasonDO>> result = new HashMap<>();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                result = getInventoryReason(responseBody);
                log.info("Response Code: " + response.code());
                log.info("Response Body: " + responseBody);
            } else {
                log.info("GET request not worked, Response Code: " + response.code());
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return result;
    }

    private Map<String, List<InventoryReasonDO>> getInventoryReason(String responseBody) {
        JSONObject response = JSON.parseObject(responseBody);
        JSONObject result = response.getJSONObject("result");
        Map<String, List<InventoryReasonDO>> all = new HashMap<>();
        if (result != null) {
            all = JSON.parseObject(result.toJSONString(), new TypeReference<Map<String, List<InventoryReasonDO>>>() {});
        }
        return all;
    }

    @Override
    public Boolean isZysyModule(String planProduct, String biz1, String biz2, String biz3) {
        OkHttpClient client = new OkHttpClient();
        DynamicProperty<String> crpUrl = DynamicProperty.create("crpUrl", "");
        String url = String.format("%s/cloud-demand-app/rpc/isZysyModule?api_key=no",
                crpUrl.get());
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("planProduct", planProduct);
        params.put("biz1", biz1);
        params.put("biz2", biz2);
        params.put("biz3", biz3);
        body.put("params", params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),
                body.toJSONString());
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        Boolean temp = null;
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null) {
                    temp = jsonObject.getBooleanValue("result");
                }
                log.info("Response Code: " + response.code());
                log.info("Response Body: " + responseBody);
            } else {
                log.info("GET request not worked, Response Code: " + response.code());
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return temp;
    }
}
