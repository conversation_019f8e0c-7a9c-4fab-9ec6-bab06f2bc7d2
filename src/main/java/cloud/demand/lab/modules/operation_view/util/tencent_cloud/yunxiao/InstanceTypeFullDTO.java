package cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao;

import lombok.Data;

/**
 * 云霄实例类型全量信息
 *
 * id	integer($int64)
 * createDate	string($date-time)
 * modifiedDate	string($date-time)
 * instanceFamily*	string
 * instanceFamilyName*	string
 * whitelistKey	string
 * generation	string
 * cpuType	string
 * bizType	string
 * bizTypeName	string
 * state	string
 * Enum:
 * [ PRINCIPAL, EOL, OTHER ]
 */
@Data
public class InstanceTypeFullDTO {
    private Long id;
    private String createDate;
    private String modifiedDate;
    private String instanceFamily;
    private String instanceFamilyName;
    private String whitelistKey;
    private String generation;
    private String cpuType;
    private String bizType;
    private String bizTypeName;
    private String state;
}
