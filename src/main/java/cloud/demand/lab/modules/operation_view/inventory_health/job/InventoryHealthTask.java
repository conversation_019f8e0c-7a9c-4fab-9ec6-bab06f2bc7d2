package cloud.demand.lab.modules.operation_view.inventory_health.job;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.service.ForecastViewService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthMckRestockService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthOverviewService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.SyncYunxiaoRubikGridService;
import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view.service.InstanceModelManualConfigService;
import cloud.demand.lab.modules.operation_view.operation_view.service.InventoryHealthGenService;
import cloud.demand.lab.modules.operation_view.operation_view.service.ManualConfigService;
import cloud.demand.lab.modules.operation_view.operation_view.service.ThresholdTransferService;
import cn.hutool.core.io.unit.DataUnit;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Calendar;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import task.run.exporter.anno.TaskRunLog;

@Slf4j
@Service
public class InventoryHealthTask {
    @Resource
    private ForecastViewService forecastViewService;
    @Resource
    private InventoryHealthGenService inventoryHealthGenService;
    @Resource
    private ThresholdTransferService thresholdTransferService;
    @Resource
    private ManualConfigService manualConfigService;

    @Resource
    private SyncYunxiaoRubikGridService syncYunxiaoRubikGridService;

    @Resource
    private InventoryHealthConfigService inventoryHealthConfigService;

    @Resource
    private InstanceModelManualConfigService instanceModelManualConfigService;

    @Resource
    private InventoryHealthMckRestockService mckRestockService;

    @Resource
    private InventoryHealthOverviewService inventoryHealthOverviewService;



    //@Scheduled(cron = "0 10 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genPplForecastHolidayWeekData() {
        forecastViewService.genForecastHolidayWeekData();
    }

    //@Scheduled(cron = "0 5 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genPurchaseFutureData() {
        forecastViewService.genPurchaseFutureData();
    }

    //@Scheduled(cron = "0 15 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genFutureForecastData() {
        forecastViewService.genFutureForecastData();
    }

    /**
     * 生成几种安全库存算法需要的中间表
     */
    //@Scheduled(cron = "0 12 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genSafetyInventoryDetail() {
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genSafetyInventoryDetail(statTime, Lang.list(), Lang.list(), null);
    }

//    @Scheduled(cron = "0 45 8 * * ?")
//    @Synchronized(waitLockMillisecond = 100)
//    public void snapshotInventoryHealthData(){
//        // 该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
//        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
//        snapshotInventoryHealthDfService.snapshotInventoryHealth(statTime);
//    }

    /**
     * 生成包月安全库存数据，该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
     * 同时会存储计算包月安全库存的中间数据，比如 13 周需求标准差、交付时间标准差等
     */
    //@Scheduled(cron = "0 45 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genMonthlySafeInventoryData() {
        // 该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genMonthlySafeInventoryData(statTime);
    }

    /**
     * 生成弹性备货配额，该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
     * 同时会存储计算弹性备货配额的中间数据，比如近一个月弹性规模均值、弹性系数等待
     */
    //@Scheduled(cron = "0 45 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genBufferInventoryData() {
        // 该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genBufferSafeInventoryData(statTime);
    }

    //@Scheduled(cron = "0 45 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genActualInventoryData() {
        // 该任务依赖上面几个任务执行完成，执行时间必须在上述任务完成之后
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genActualInventoryData(statTime);
    }

    /**
     * 基于实际库存数据，以及星云单据数据，生成供应汇总数据，改任务必须在实际库存数据生成完成后执行
     */
    //@Scheduled(cron = "0 55 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genSupplySummaryData() {
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genSupplySummaryData(statTime);
    }

    //@Scheduled(cron = "0 50 7 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotHeadZlkhbData() {
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.snapshotHeadZlkhbData(statTime);
    }

    //@Scheduled(cron = "0 30 7 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genDeliveryDaysData() {
        String statTime = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        inventoryHealthGenService.genDeliveryDaysData(statTime);
    }

    //@Scheduled(cron = "0 45 5 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotThresholdConfig() {
        thresholdTransferService.snapshotThresholdConfig();
    }

    //@Scheduled(cron = "0 15 5 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotManualConfig() {
        String yesterday = DateUtils.formatDate(DateUtils.yesterday());
        manualConfigService.snapshotManualConfig(yesterday);
    }

    //@Scheduled(cron = "0 1 0 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotMckRestockManualConfig() {
        String yesterday = DateUtils.formatDate(DateUtils.yesterday());
        mckRestockService.snapshotMckRestockManualConfig(yesterday);
    }

    /**
     * 同步安全库存实例规格均摊占比
     */
    //@Scheduled(cron = "0 10 5 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotInstanceModelManualConfig() {
        String yesterday = DateUtils.formatDate(DateUtils.yesterday());
        instanceModelManualConfigService.snapshotManualConfig(yesterday);
    }

    /**
     * 每天定时同步云霄预扣块
     */
    //@Scheduled(cron = "0 15 2 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void syncYunxiaoRubikGridData() {
        String yestoday = DateUtils.formatDate(DateUtils.yesterday());
        syncYunxiaoRubikGridService.syncYunxiaoRubikGridData(yestoday);
    }

    /**
     * 每天凌晨自动继承前一天配置数据
     */
    //@Scheduled(cron = "0 1 0 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void inheritThresholdConfig() {
        inventoryHealthConfigService.snapshotInventoryHealthConfig(DateUtils.formatDate(DateUtils.today()));
    }

    /**
     * 每 30 分钟同步云霄的机型和可用区信息
     */
    //@Scheduled(cron = "0 */30 * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void syncYunxiaoRubikGridData2() {
        // 云霄机型信息有问题暂时不同步
//        inventoryHealthConfigService.syncMainInstanceTypeConfig(false);
        inventoryHealthConfigService.syncMainZoneNameConfig(false);
    }

    /**
     * 生成 WN 预测底表
     */
    //@Scheduled(cron = "0 0 7 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genWeekNForecastData() {
        String yestoday = DateUtils.formatDate(DateUtils.yesterday());
        inventoryHealthGenService.genWeekNForecastData(yestoday);
    }

    /**
     * 生成 mck 周转库存底表
     */
    //@Scheduled(cron = "0 55 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genMckTurnoverInventoryData() {
        String yestoday = DateUtils.formatDate(DateUtils.yesterday());
        inventoryHealthGenService.genMckTurnoverInventoryData(yestoday);
    }

    /**
     * 生成 mck 预测周转库存底表
     */
    //@Scheduled(cron = "0 25 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genMckForecastTurnoverInventoryData() {
        String yestoday = DateUtils.formatDate(DateUtils.yesterday());
        inventoryHealthGenService.genMckForecastTurnoverInventoryData(yestoday);
    }

    /**
     * 生成 mck 预测安全库存底表
     */
    //@Scheduled(cron = "0 55 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genMckForecastSafeInventoryData() {
        String yestoday = DateUtils.formatDate(DateUtils.yesterday());
        inventoryHealthGenService.genMckForecastSafeInventoryData(yestoday);
    }

    /**
     * 生成EMR、ES、TC的售罄率数据表
     */
    //@Scheduled(cron = "0 10 0 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genServiceLevelSoldData() {
        LocalDate yesterday = DateUtils.yesterday();
        String statTime = DateUtils.formatDate(yesterday);
        long unix = DateUtils.parse(statTime).getTime() / 1000;
        inventoryHealthGenService.genServiceLevelSoldData(String.valueOf(unix), statTime);
    }

    /**
     * 生成EMR、ES、TC的售罄率数据表
     */
    //@Scheduled(cron = "0 15 0 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genServiceLevelApiSuccessData() {
        LocalDate yesterday = DateUtils.yesterday();
        String statTime = DateUtils.formatDate(yesterday);
        long unix = DateUtils.parse(statTime).getTime() / 1000;
        inventoryHealthGenService.genServiceLevelApiSuccessData(String.valueOf(unix), statTime);
    }

    @Scheduled(cron = "8 0 0 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genLeisureAndBusySoldOutData() {
        LocalDate yesterday = DateUtils.yesterday();
        String statTime = DateUtils.format(yesterday, "yyyyMMdd");
        inventoryHealthGenService.genLeisureAndBusySoldOutData(statTime);
    }

    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genInventoryHealthWeekData() {
        LocalDate yesterday = DateUtils.yesterday();
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
        ResPlanHolidayWeekDO week = bean.getHolidayWeekInfoByDate(DateUtils.formatDate(yesterday));
        inventoryHealthGenService.genInventoryHealthWeekData(week.getStart());
    }

    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genInventoryHealthMonthData() {
        LocalDate yesterday = DateUtils.yesterday();
        YearMonth yearMonth = YearMonth.of(yesterday.getYear(), yesterday.getMonthValue());
        inventoryHealthGenService.genInventoryHealthMonthData(DateUtils.formatDate(yearMonth.atDay(1)));
    }



    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genCDBServiceLevelData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genCDBServiceLevelData() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genCDBServiceLevelData(DateUtils.formatDate(yesterday));
    }

    /** 1：00 跑 CLB 的服务水平 */
    @Scheduled(cron = "0 0 1 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genClsLogServiceLevelDataForCLB'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genClsLogServiceLevelDataForCLB() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genClsLogServiceLevelData(ClsLogProductEnum.CLB,DateUtils.formatDate(yesterday));
    }

    /** 1:10 跑 EIP 的服务水平 */
    @Scheduled(cron = "0 10 1 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genClsLogServiceLevelDataForEIP'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genClsLogServiceLevelDataForEIP() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genClsLogServiceLevelData(ClsLogProductEnum.EIP,DateUtils.formatDate(yesterday));
    }

    /**
     * 6点跑CBS的服务水平
     */
    @Scheduled(cron = "0 0 6 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genCBSServiceLevelData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genCBSServiceLevelData() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genCBSServiceLevelData(DateUtils.formatDate(yesterday));
    }

    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void genEndToEndMonthData() {
        LocalDate yesterday = DateUtils.yesterday();
        YearMonth yearMonth = YearMonth.of(yesterday.getYear(), yesterday.getMonthValue());
        LocalDate localDate = yearMonth.atDay(1);
        String start = localDate.toString().replace("-","");
        String end = yesterday.toString().replace("-", "");
        inventoryHealthGenService.genEndToEndMonthData(yearMonth.toString(), start, end);
    }

    @Scheduled(cron = "0 0 9 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genCRSServiceLevelData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genCRSServiceLevelData() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genCRSServiceLevelData(DateUtils.formatDate(yesterday));
    }

    @Scheduled(cron = "0 0 1 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeStaticCvmType() {
        inventoryHealthGenService.synchronizeStaticCvmType();
    }

    @Scheduled(cron = "0 40 11 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'genTDSQLServiceLevelData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genTDSQLServiceLevelData() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.genTDSQLServiceLevelData(DateUtils.formatDate(yesterday));
    }


    @Scheduled(cron = "0 0 10 ? * 3")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "INVENTORY", nameScript = "'sendServiceLevelMail'", keyScript = "''")
    public void sendServiceLevelMail() {
        inventoryHealthOverviewService.sendInventoryHealthMail();
    }

}
