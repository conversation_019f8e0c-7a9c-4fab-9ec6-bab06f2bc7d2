package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
@Table("dws_demand_week_n_ppl_version_item")
public class DwsDemandWeekNPplVersionItemDO extends PplVersionItemDO implements Cloneable {

    // 下面是该条 PPL 对应的节假周信息，后续代码填充，一条数据可能会被平均到多个 ppl item 中
    @Column("stat_time")
    private LocalDate statTime;
    /**
     * holiday_year            Int32          default 0 comment '安全库存计算的节假年',
     *     holiday_month           Int32          default 0 comment '安全库存计算的节假月',
     *     holiday_week            Int32          default 0 comment '安全库存计算的节假周',
     *     holiday_week_start_date Date           default '1970-01-01' comment '节假周开始时间',
     *     holiday_week_end_date   Date           default '1970-01-01' comment '节假周结束时间',
     */
    @Column("holiday_year")
    private Integer holidayYear;
    @Column("holiday_month")
    private Integer holidayMonth;
    @Column("holiday_week")
    private Integer holidayWeek;

    @Column("holiday_week_start_date")
    private String weekStartDate;
    @Column("holiday_week_end_date")
    private String weekEndDate;
    @Column("average_total_core")
    private BigDecimal averageTotalCore;
    @Column("week_n")
    private int weekN;
    @Column("week_index")
    private int weekIndex;

    @Column("total_core")
    private BigDecimal totalCore;

    @Column("product")
    private String productType;

    public static DwsDemandWeekNPplVersionItemDO from(PplVersionItemDO itemDO) {
        DwsDemandWeekNPplVersionItemDO itemWeeklyDO = new DwsDemandWeekNPplVersionItemDO();
        itemWeeklyDO.setVersionCodeDate(itemDO.getVersionCodeDate());
        itemWeeklyDO.setBeginBuyDate(itemDO.getBeginBuyDate());
        itemWeeklyDO.setEndBuyDate(itemDO.getEndBuyDate());
        itemWeeklyDO.setCustomhouseTitle(itemDO.getCustomhouseTitle());
        itemWeeklyDO.setAreaName(itemDO.getAreaName());
        itemWeeklyDO.setRegionName(itemDO.getRegionName());
        itemWeeklyDO.setZoneName(itemDO.getZoneName());
        itemWeeklyDO.setInstanceType(itemDO.getInstanceType());
        itemWeeklyDO.setYear(itemDO.getYear());
        itemWeeklyDO.setMonth(itemDO.getMonth());
        itemWeeklyDO.setSource(itemDO.getSource());
        itemWeeklyDO.setTotalCore(itemDO.getTotalCore());
        return itemWeeklyDO;
    }

    public DwsDemandWeekNPplVersionItemDO clone() throws CloneNotSupportedException {
        return (DwsDemandWeekNPplVersionItemDO) super.clone();
    }
}
