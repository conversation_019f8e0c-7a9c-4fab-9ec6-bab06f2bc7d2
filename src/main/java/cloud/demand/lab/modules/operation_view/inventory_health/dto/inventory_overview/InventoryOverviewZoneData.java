package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class InventoryOverviewZoneData {

    private String product;

    private String zoneName;

    private String country;

    private String areaName;

    private String regionName;

    private BigDecimal actualInventory;

    private BigDecimal actualMem;

    private BigDecimal actualDisk;

    private BigDecimal apiSucTotal;

    private BigDecimal apiTotal;

    private BigDecimal soldOutTotal;

    private BigDecimal soldTotal;

    private BigDecimal soldScale;

    private BigDecimal totalScale;

    private Long sortNum;




}
