package cloud.demand.lab.modules.operation_view.inventory_health.dto.actual;

import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryServiceLevelReq {

    @NotNull
    private String statDate;

    /**
     * 实例类型
     */
    private List<String> instanceType;

    /**
     * 国家
     */
    private List<String> country;

    /**
     * 是否返回聚合数据
     */
    private Boolean isCumulative;

    /**
     * 售罄率类型
     */
    private  String soldType;



}
