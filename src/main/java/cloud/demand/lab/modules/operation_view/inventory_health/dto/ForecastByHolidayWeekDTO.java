package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPplForecastDetailDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

@Data
public class ForecastByHolidayWeekDTO {

    @Column("instance_type")
    private String instanceType;

    @Column("instance_model")
    private String instanceModel;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    /**
     * 净增核心数
     */
    @Column("total_core")
    private Integer totalCore;

    /**
     * 新增核心数
     */
    private Integer totalCoreNew;

    /**
     * 退回核心数
     */
    private Integer totalCoreReturn;

    @Column("begin_buy_date")
    private String beginBuyDate;

    @Column("end_buy_date")
    private String endBuyDate;

    @Column("industry_dept")
    private String industryDept;

    @Column("source")
    private String source;

    private Integer holidayYear;

    private Integer holidayMonth;

    private Integer holidayWeek;

    private String customhouseTitle;

    private String areaName;

    /** 模型预测的数据来源，内领和行业<br/>Column: [forecast_model_source_type] */
    @Column(value = "forecast_model_source_type")
    private String forecastModelSourceType;

    public static String getGroupK(ForecastByHolidayWeekDTO dto){
        return String.join("@",
                dto.getInstanceType(), dto.getIndustryDept(), dto.getZoneName(), dto.getRegionName(),
                dto.getHolidayYear().toString(), dto.getHolidayMonth().toString(),
                dto.getHolidayWeek().toString(), dto.getSource(), dto.getForecastModelSourceType());
    }

    public static ForecastByHolidayWeekDTO copy(ForecastByHolidayWeekDTO source) {
        ForecastByHolidayWeekDTO forecastByHolidayWeekDTO = new ForecastByHolidayWeekDTO();
        forecastByHolidayWeekDTO.setInstanceType(source.getInstanceType());
        forecastByHolidayWeekDTO.setInstanceModel(source.getInstanceModel());
        forecastByHolidayWeekDTO.setRegionName(source.getRegionName());
        forecastByHolidayWeekDTO.setZoneName(source.getZoneName());
        forecastByHolidayWeekDTO.setTotalCore(source.getTotalCore());
        forecastByHolidayWeekDTO.setBeginBuyDate(source.getBeginBuyDate());
        forecastByHolidayWeekDTO.setEndBuyDate(source.getEndBuyDate());
        forecastByHolidayWeekDTO.setHolidayYear(source.getHolidayYear());
        forecastByHolidayWeekDTO.setHolidayMonth(source.getHolidayMonth());
        forecastByHolidayWeekDTO.setHolidayWeek(source.getHolidayWeek());
        forecastByHolidayWeekDTO.setCustomhouseTitle(source.getCustomhouseTitle());
        forecastByHolidayWeekDTO.setAreaName(source.getAreaName());
        forecastByHolidayWeekDTO.setIndustryDept(source.getIndustryDept());
        forecastByHolidayWeekDTO.setSource(source.getSource());
        forecastByHolidayWeekDTO.setForecastModelSourceType(source.getForecastModelSourceType());
        return forecastByHolidayWeekDTO;
    }

    public static InventoryHealthPplForecastDetailDO transform(ForecastByHolidayWeekDTO source){
        InventoryHealthPplForecastDetailDO pplForecastWeekDetailDO = new InventoryHealthPplForecastDetailDO();
        pplForecastWeekDetailDO.setBeginBuyDate(DateUtils.toLocalDate(DateUtils.parse(source.getBeginBuyDate())));
        pplForecastWeekDetailDO.setEndBuyDate(DateUtils.toLocalDate(DateUtils.parse(source.getEndBuyDate())));
        pplForecastWeekDetailDO.setCustomhouseTitle(source.getCustomhouseTitle());
        pplForecastWeekDetailDO.setAreaName(source.getAreaName());
        pplForecastWeekDetailDO.setRegionName(source.getRegionName());
        pplForecastWeekDetailDO.setZoneName(source.getZoneName());
        pplForecastWeekDetailDO.setInstanceType(source.getInstanceType());
        pplForecastWeekDetailDO.setHolidayYear(source.getHolidayYear());
        pplForecastWeekDetailDO.setHolidayMonth(source.getHolidayMonth());
        pplForecastWeekDetailDO.setHolidayWeek(source.getHolidayWeek());
        pplForecastWeekDetailDO.setIndustryDept(source.getIndustryDept());
        pplForecastWeekDetailDO.setSource(source.getSource());
        pplForecastWeekDetailDO.setTotalCore(source.getTotalCore());
        pplForecastWeekDetailDO.setTotalCoreNew(source.getTotalCoreNew());
        pplForecastWeekDetailDO.setTotalCoreReturn(source.getTotalCoreReturn());
        pplForecastWeekDetailDO.setForecastModelSourceType(source.getForecastModelSourceType());
        return pplForecastWeekDetailDO;
    }
}

