package cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryMckRestockReq {
    /**
     * 计划日期
     */
    @NotNull
    private LocalDate date;

    /**
     * 需求日期
     */
    @NotNull
    private LocalDate demandDate;

    /** 库存类型：线上库存、线下搬迁、线下流转 */
    private List<String> lineType;

    /** 好差呆：好料、差料、呆料*/
    private List<String> materialType;

    /** 库存细项：大核库存、小核库存、大核预留等*/
    private List<String> invDetailType;

    /**实例类型*/
    private List<String> instanceType;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /** 汇总维度：可用区、实例类型 */
    private List<String> groupByDimension;

    /**
     * 机型族
     */
    private List<String> ginFamily;

    /** 安全库存转换：历史安全库存、预测安全库存 */
    private String switchSafeInventory;

    /** 是否包含全量在途数据，默认不勾选 */
    private Boolean isContainFullSupply;

    /** 需求量筛选，默认订单 */
    private String demandSelect;

    /** condition 缓存 */
    private WhereSQL condition = null;

    public WhereSQL genBasicCondition() {
        if (this.condition != null) {
            return this.condition;
        }

        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }

        WhereSQL cateCondition = SpringUtil.getBean(OperationViewService2Impl.class).genCategoryCondition(
                zoneCategory,
                instanceTypeCategory,
                false,
                date.toString(),
                customhouseTitle
        );

        this.condition = condition.and(cateCondition);
        return this.condition;
    }

    public WhereSQL genActualInvCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(materialType)) {
            condition.and("material_type in (?)", materialType);
        }
        if (ListUtils.isNotEmpty(lineType)) {
            condition.and("line_type in (?)", lineType);
        }
        if (ListUtils.isNotEmpty(invDetailType)) {
            condition.and("inv_detail_type in (?)", invDetailType);
        }

        return condition;
    }
}
