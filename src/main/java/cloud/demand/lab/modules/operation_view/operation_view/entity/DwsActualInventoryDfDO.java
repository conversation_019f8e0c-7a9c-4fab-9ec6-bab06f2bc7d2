package cloud.demand.lab.modules.operation_view.operation_view.entity;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.CommonConditionDTO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 包月安全库存，包含包月安全库存计算所需要的中间数据以及计算结果
 */
@Table("dws_actual_inventory_df")
@Data
public class DwsActualInventoryDfDO extends CommonConditionDTO {
    /**
     * 统计时间
     */
    @Column("stat_time")
    private String statTime;

    /** 产品类型 */
    @Column("product_type")
    private String productType;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 区域名 */
    @Column("area_name")
    private String areaName;

    /** 地域名 */
    @Column("region_name")
    private String regionName;

    /** 可用区名 */
    @Column("zone_name")
    private String zoneName;

    /**实例类型*/
    @Column("instance_type")
    private String instanceType;

    /**设备类型*/
    @Column("device_type")
    private String deviceType;

    /** 库存类型 */
    @Column("line_type")
    private String lineType;

    /** 物料类型：好呆差 */
    @Column("material_type")
    private String materialType;

    /** 物料类型：好呆差 */
    @Column("inv_detail_type")
    private String invDetailType;

    /**实际库存*/
    @Column("actual_inv")
    private BigDecimal actualInv;

    /**设备数量*/
    @Column("device_num")
    private BigDecimal deviceNum;
}
