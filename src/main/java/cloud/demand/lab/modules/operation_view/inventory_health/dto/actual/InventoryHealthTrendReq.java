package cloud.demand.lab.modules.operation_view.inventory_health.dto.actual;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.qcloud.cos.model.inventory.InventorySchedule;
import java.util.List;
import lombok.Data;


@Data
public class InventoryHealthTrendReq {

    /**
     * 开始时间
     */
    private String start;

    /**
     * 结束时间
     */
    private String end;

    /**
     * 时间维度：日、周、月，对于新库存健康接口必填
     *对于日，格式为xxxx-xx-xx
     * 对于周 格式为xxxxWxx
     * 对于月 格式为xxxx-xx
     */
    private String timeDimension;

    /**
     * 展开字段，比如可用区，地域，，区域等
     */
    private String expandField;

    /**
     * 实例类型
     */
    private List<String> instanceType;

    /**
     * 境内外
     */
    private List<String> customhouseTitle;

    /**
     * 国家
     */
    private List<String> country;
    /**
     * 区域
     */
    private List<String> areaName;
    /**
     * 地域
     */
    private List<String> regionName;
    /**
     * 可用区
     */
    private List<String> zoneName;

    /**
     * 库存类型：线上库存、线下搬迁、线下流转
     */
    private List<String> lineType;

    /**
     * 物料类型：好料、差料、呆料
     */
    private List<String> materialType;

    /**
     * 库存细类：大核库存、小核库存、大核预留、小核预留等
     */
    private List<String> invDetailType;

    /**
     * 是否组合机型
     */
    private Boolean isCombine;

    /**
     * 售罄率类型：全时段，闲时，忙时
     */
    private  String soldType;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /**
     * 机型族
     */
    private List<String> ginFamily;

    /**
     * 客户类型：
     *  [
     *    { label: '中长尾客户', value: 'MEDIUM_LONG_TAIL' },
     *    { label: '头部客户', value: 'LIST_REPORT' },
     *    { label: '全选', value: 'ALL' },
     *  ]
     */
    private String customerCustomGroup;

    /**
     * 客户预扣剔除，如果为 true，返回的库存数据剔除预扣，预扣数据不计入库存
     */
    private Boolean isIgnoreReserved;

    /**
     * 是否包含闲置预扣
     */
    private Boolean isIncludeReserved;

    /**
     * 取主力机型、主力可用区时对应的日期，如果为 null，默认为 statTime
     */
    private String categoryDate;

    public WhereSQL genCondition(){
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }
        return condition;
    }


    public InventoryHealthActualReq genActualReq(String date, InventoryHealthTrendReq req, String timeDimension) {
        InventoryHealthActualReq result = new InventoryHealthActualReq();
        result.setDate(date);
        result.setTimeDimension(timeDimension);
        result.setInstanceType(req.getInstanceType());
        result.setCustomhouseTitle(req.getCustomhouseTitle());
        result.setAreaName(req.getAreaName());
        result.setRegionName(req.getRegionName());
        result.setZoneName(req.getZoneName());
        result.setLineType(req.getLineType());
        result.setMaterialType(req.getMaterialType());
        result.setInvDetailType(req.getInvDetailType());
        result.setIsCombine(req.getIsCombine());
        result.setSoldType(req.getSoldType());
        result.setZoneCategory(req.getZoneCategory());
        result.setInstanceTypeCategory(req.getInstanceTypeCategory());
        result.setCustomerCustomGroup(req.getCustomerCustomGroup());
        result.setIsIgnoreReserved(req.getIsIgnoreReserved());
        result.setIsIncludeReserved(req.getIsIncludeReserved());
        result.setCategoryDate(req.getCategoryDate());

        return result;



    }

}
