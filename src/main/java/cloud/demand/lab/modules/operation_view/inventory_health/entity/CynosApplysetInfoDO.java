package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cynos_applyset_info")
public class CynosApplysetInfoDO {

    /** 自增主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** cpu核数(个)<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Integer cpu;

    /** mem大小(GB)<br/>Column: [mem] */
    @Column(value = "mem")
    private Integer mem;

    /** 实例类型<br/>Column: [xtype] */
    @Column(value = "xtype")
    private String xtype;

    /** 该规格下的实例个数<br/>Column: [count] */
    @Column(value = "count")
    private Integer count;

    /** 数据插入时间(年月日)<br/>Column: [createDate] */
    @Column(value = "createDate")
    private String createDate;

    /** 所在地域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 区域zone<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

}
