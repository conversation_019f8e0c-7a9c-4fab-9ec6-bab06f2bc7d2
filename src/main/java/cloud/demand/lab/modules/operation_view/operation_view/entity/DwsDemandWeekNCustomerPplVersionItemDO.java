package cloud.demand.lab.modules.operation_view.operation_view.entity;

import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString(callSuper = true)
@Table("dws_demand_week_n_customer_ppl_version_item")
public class DwsDemandWeekNCustomerPplVersionItemDO implements Cloneable {

    // 下面是该条 PPL 对应的节假周信息，后续代码填充，一条数据可能会被平均到多个 ppl item 中
    @Column("stat_time")
    private LocalDate statTime;
    /**
     * holiday_year            Int32          default 0 comment '安全库存计算的节假年',
     *     holiday_month           Int32          default 0 comment '安全库存计算的节假月',
     *     holiday_week            Int32          default 0 comment '安全库存计算的节假周',
     *     holiday_week_start_date Date           default '1970-01-01' comment '节假周开始时间',
     *     holiday_week_end_date   Date           default '1970-01-01' comment '节假周结束时间',
     */
    @Column("holiday_year")
    private Integer holidayYear;
    @Column("holiday_month")
    private Integer holidayMonth;
    @Column("holiday_week")
    private Integer holidayWeek;

    @Column("holiday_week_start_date")
    private String weekStartDate;
    @Column("holiday_week_end_date")
    private String weekEndDate;

    @Column("week_n")
    private Integer weekN;
    @Column("week_index")
    private Integer weekIndex;

    /** 国内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("industry_dept")
    private String industryDept;

    @Column("customer_short_name")
    private String customerShortName;

    @Column("uin")
    private String uin;

    /** 分摊ppl预测核数 */
    @Column("average_total_core")
    private BigDecimal averageTotalCore;

    /** ppl预测核数 */
    @Column("total_core")
    private BigDecimal totalCore;

    /** 周峰外部计费内部服务 */
    @Column("logic_num")
    private BigDecimal logicNum;

    /** 周峰内外都是计费 */
    @Column("bill_num")
    private BigDecimal billNum;

    /** 周峰内外都是服务 */
    @Column("service_num")
    private BigDecimal serviceNum;

    @Column("product")
    private String product;

    public static DwsDemandWeekNCustomerPplVersionItemDO transform(CustomerSafetyInventoryHistoryPeakDTO v) {
        DwsDemandWeekNCustomerPplVersionItemDO ret = new DwsDemandWeekNCustomerPplVersionItemDO();
        ret.setHolidayYear(v.getYear());
        ret.setHolidayWeek(v.getWeek());
        ret.setCustomhouseTitle(v.getCustomhouseTitle());
        ret.setIndustryDept(v.getIndustryDept());
        ret.setCustomerShortName(v.getCustomerShortName());
        ret.setUin(v.getUin());
        ret.setTotalCore(BigDecimal.ZERO);
        ret.setAverageTotalCore(BigDecimal.ZERO);
        ret.setLogicNum(v.getLogicNum());
        ret.setBillNum(v.getBillNum());
        ret.setServiceNum(v.getServiceNum());
        ret.setProduct(v.getProduct());
        return ret;

    }

    public String getKey(){
        return StringUtils.joinWith("@", holidayYear, holidayWeek,customhouseTitle, industryDept, customerShortName, uin);
    }

    public String getWaveKey(){
        return StringUtils.joinWith("@", customhouseTitle, customerShortName, uin);
    }

    public DwsDemandWeekNCustomerPplVersionItemDO copy(){
        DwsDemandWeekNCustomerPplVersionItemDO ret = new DwsDemandWeekNCustomerPplVersionItemDO();
        ret.setStatTime(this.getStatTime());
        ret.setHolidayYear(this.getHolidayYear());
        ret.setHolidayMonth(this.getHolidayMonth());
        ret.setHolidayWeek(this.getHolidayWeek());
        ret.setWeekStartDate(this.getWeekStartDate());
        ret.setWeekEndDate(this.getWeekEndDate());
        ret.setWeekN(this.getWeekN());
        ret.setWeekIndex(this.getWeekIndex());
        ret.setCustomhouseTitle(this.getCustomhouseTitle());
        ret.setIndustryDept(this.getIndustryDept());
        ret.setCustomerShortName(this.getCustomerShortName());
        ret.setUin(this.getUin());
        ret.setAverageTotalCore(this.getAverageTotalCore());
        ret.setTotalCore(this.getTotalCore());
        ret.setLogicNum(this.getLogicNum());
        ret.setBillNum(this.getBillNum());
        ret.setServiceNum(this.getServiceNum());
        ret.setProduct(this.getProduct());
        return ret;
    }

    public static DwsDemandWeekNCustomerPplVersionItemDO from(CustomerPplVersionItemDO itemDO, HolidayWeekInfoDTO holidayWeekInfoDTO) {

        DwsDemandWeekNCustomerPplVersionItemDO ret = new DwsDemandWeekNCustomerPplVersionItemDO();
        // 暂时支持 CVM
        ret.setProduct(ProductTypeEnum.CVM.getCode());
        ret.setCustomhouseTitle(itemDO.getCustomhouseTitle());
        ret.setIndustryDept(itemDO.getIndustryDept());
        ret.setCustomerShortName(itemDO.getCustomerShortName());
        ret.setUin(itemDO.getCustomerUin());
        ret.setTotalCore(ObjectUtils.defaultIfNull(itemDO.getTotalCore(),BigDecimal.ZERO));
        // 默认不分摊
        ret.setAverageTotalCore(ret.getTotalCore());

        ret.setHolidayYear(holidayWeekInfoDTO.getYear());
        ret.setHolidayMonth(holidayWeekInfoDTO.getMonth());
        ret.setHolidayWeek(holidayWeekInfoDTO.getWeek());
        ret.setWeekStartDate(holidayWeekInfoDTO.getStartDate());
        ret.setWeekEndDate(holidayWeekInfoDTO.getEndDate());
        ret.setWeekIndex(holidayWeekInfoDTO.getWeekNFromNow());
        return ret;

    }

    public DwsDemandWeekNCustomerPplVersionItemDO clone() throws CloneNotSupportedException {
        return (DwsDemandWeekNCustomerPplVersionItemDO) super.clone();
    }
}
