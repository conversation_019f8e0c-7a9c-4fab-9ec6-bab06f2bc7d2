package cloud.demand.lab.modules.operation_view.inventory_health.dto.future;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthFutureForecastDetailDO;
import java.util.List;
import lombok.Data;

/**
 * 查询未来库存概览的返回体
 */
@Data
public class InventoryHealthFutureResp {

    private List<Item> data;

    //  水位系数
    private double waterLevelFactor;

    @Data
    public static class Item {

        private String customhouseTitle;

        private String areaName;

        private String regionName;

        private String zoneName;

        private String instanceType;

        private Integer holidayYear;

        private Integer holidayWeek;

        /**周index，0表示第0周，1表示第1周*/
        private Integer weekIndex;

        private String startDate;

        private String endDate;

        /**实际库存(核)，只有第0周有数据*/
        private Integer actualInventoryCore;

        /**模拟库存(核)，只有第1周起才有数据*/
        private Integer simulateInventoryCore;

        /**未来采购到货量，核，第1周起有数据*/
        private Integer futurePurchaseCore;

        /**需求预测核心数，第1周起有数据*/
        private Integer demandForecastNew;
        /**退回预测核心数，第1周起有数据*/
        private Integer demandForecastReturn;
        /**净增预测核心数，第1周起有数据*/
        private Integer demandForecastTotal;

        /**安全库存核心数，第1周起有数据*/
        private Integer safetyInventoryCore;

        /**周转库存核心数，第1周起有数据 */
        private Integer turnoverInventoryCore;

        public static Item from(InventoryHealthFutureForecastDetailDO d) {
            Item item = new Item();
            item.setCustomhouseTitle(d.getCustomhouseTitle());
            item.setAreaName(d.getAreaName());
            item.setRegionName(d.getRegionName());
            item.setZoneName(d.getZoneName());
            item.setInstanceType(d.getInstanceType());
            item.setHolidayYear(d.getHolidayYear());
            item.setHolidayWeek(d.getHolidayWeek());
            item.setWeekIndex(d.getWeekIndex());
            item.setActualInventoryCore(d.getActualInventory());
            item.setSimulateInventoryCore(d.getSimulateInventory());
            item.setFuturePurchaseCore(d.getFuturePurchase());
            item.setDemandForecastNew(d.getDemandForecastNew());
            item.setDemandForecastReturn(d.getDemandForcastReturn());
            item.setDemandForecastTotal(d.getDemandForecastTotal());
            item.setSafetyInventoryCore(d.getSafetyInventory());
            item.setTurnoverInventoryCore(d.getTurnoverInventory());
            return item;
        }
    }

}
