package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ads_mck_forecast_summary_df")
public class AdsMckForecastSummaryDfDO {

    /**
     * 数据日期<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /**
     * 大客户/预约单/中长尾
     */
    @Column(value = "demand_source")
    private String demandSource;

    @Column(value = "product")
    private String product;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "area_name")
    private String areaName;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "submit_date")
    private LocalDate submitDate;

    @Column(value = "submit_year")
    private Integer submitYear;

    @Column(value = "submit_week")
    private Integer submitWeek;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "gpu_type")
    private String gpuType;

    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    @Column(value = "begin_buy_year")
    private Integer beginBuyYear;

    @Column(value = "begin_buy_week")
    private Integer beginBuyWeek;

    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    @Column(value = "instance_num")
    private Integer instanceNum;

    @Column(value = "total_core")
    private Integer totalCore;

    @Column(value = "compare")
    private String compare;

    //预测单id
    @Column(value = "ppl_id")
    private String pplId;

    // 预约单填充字段

    @Column(value = "yunxiao_order_id")
    private String yunxiaoOrderId;

    @Column(value = "yunxiao_order_status")
    private String yunxiaoOrderStatus;

    // 大客户填充字段
    @Column(value = "status_name")
    private String statusName;

    @Column(value = "week_gap")
    private Integer weekGap;

    @Column(value = "zone_name_original")
    private String zoneNameOriginal;

    @Column(value = "version_code")
    private String versionCode;

    // 中长尾填充字段
    @Column(value = "taskId")
    private Integer taskId;

    // 模型训练期间开始
    @Column(value = "input_date_begin")
    private LocalDate inputDateBegin;

    // 模型训练期间结束
    @Column(value = "input_date_end")
    private LocalDate inputDateEnd;

    @Column(value = "gpu_num")
    private Integer gpuNum;

    @Column(value = "total_gpu_num")
    private Integer totalGpuNum;

    /**
     * 国内国外
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "win_rate")
    private BigDecimal winRate;

    @Column(value = "project_name")
    private String projectName;

    // 订单新加字段。
    @Column(value = "order_number")
    private String orderNumber;

    @Column(value = "order_number_id")
    private String orderNumberId;

    @Column(value = "order_node_code")
    private String orderNodeCode;

    @Column(value = "order_source")
    private String orderSource;

    @Column(value = "is_spike")
    private Integer isSpike;

    @Column(value = "is_head")
    private Integer isHead;
}
