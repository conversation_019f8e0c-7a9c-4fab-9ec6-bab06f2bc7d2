package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualTrendExportData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCbsServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CBSHealthActualService;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class CBSHealthActualServiceImpl implements CBSHealthActualService {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    DBHelper demandDBHelper;

    @Override
    public List<CBSHealthActualData> queryCBSHealthActualTrendReport(CBSHealthActualReq req) {

        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }
        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        //获取数据
        List<DwsCbsServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfDO.class,
                condition.getSQL(), condition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList, o -> o.getZoneName(),
                o -> o);
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCbsServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        List<CBSHealthActualData> result = new ArrayList<>();
        for (Entry<String, List<DwsCbsServiceLevelDataDfDO>> entry : mapList.entrySet()) {
            CBSHealthActualData data = new CBSHealthActualData();
            DwsCbsServiceLevelDataDfDO temp = entry.getValue().get(0);
            data.setStatTime(dateMap.get(temp.getStatTime()));
            data.setZoneName(temp.getZoneName());
            data.setRegionName(temp.getRegionName());
            data.setAreaName(temp.getAreaName());
            data.setCustomhouseTitle(temp.getCustomhouseTitle());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(data.getZoneName());
            if (configItem != null) {
                data.setZoneCategory(configItem.getTypeName());
            }else {
                data.setZoneCategory("未分类");
            }
            if (StringUtils.isNotBlank(req.getVolumeType())) {
                data.setVolumeType(temp.getVolumeType());
            }
            data.setFailedCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailedCount));
            data.setSuccessCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessCount));
            data.setSuccessDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessDiskSize));
            data.setFailedDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailDiskSize));
            BigDecimal totalSize = data.getFailedDiskSize().add(data.getSuccessDiskSize());
            if (totalSize.compareTo(BigDecimal.ZERO) > 0) {
                data.setServiceLevel(data.getSuccessDiskSize()
                        .divide(totalSize, 4, RoundingMode.HALF_UP));
            }
            result.add(data);
        }
        return result;
    }

    @Override
    public DownloadBean exportCBSHealthActualTrendReport(CBSHealthActualReq req) {
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }
        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        //获取数据
        List<DwsCbsServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfDO.class,
                condition.getSQL(), condition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList,
                InventoryHealthMainZoneNameConfigDO::getZoneName,
                o -> o);
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCbsServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName(), o.getAppId(),
                        o.getIndustryDept(), o.getCustomerGroup()), o -> o);
        List<CBSHealthActualTrendExportData> result = new ArrayList<>();
        for (Entry<String, List<DwsCbsServiceLevelDataDfDO>> entry : mapList.entrySet()) {
            CBSHealthActualTrendExportData data = new CBSHealthActualTrendExportData();
            DwsCbsServiceLevelDataDfDO temp = entry.getValue().get(0);
            data.setStatTime(dateMap.get(temp.getStatTime()));
            data.setZoneName(temp.getZoneName());
            data.setRegionName(temp.getRegionName());
            data.setAreaName(temp.getAreaName());
            data.setCustomhouseTitle(temp.getCustomhouseTitle());
            data.setAppId(temp.getAppId());
            data.setCustomerShortName(temp.getCustomerShortName());
            data.setIndustryDept(temp.getIndustryDept());
            data.setCustomerGroup(temp.getCustomerGroup());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(data.getZoneName());
            if (configItem != null) {
                data.setZoneCategory(configItem.getTypeName());
            }else {
                data.setZoneCategory("未分类");
            }
            if (StringUtils.isNotBlank(req.getVolumeType())) {
                data.setVolumeType(temp.getVolumeType());
            }
            data.setFailedCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailedCount).intValue());
            data.setSuccessCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessCount).intValue());
            data.setSuccessDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessDiskSize).intValue());
            data.setFailedDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailDiskSize).intValue());
            result.add(data);
        }
        result.sort(Comparator.comparing(CBSHealthActualTrendExportData::getStatTime)
                .thenComparing(CBSHealthActualTrendExportData::getZoneName)
                .thenComparing(CBSHealthActualTrendExportData::getAppId));
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/cbs_health_actual_trend.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", result), writeSheet).finish();
        String fileName = "CBS服务水平趋势数据明细" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    public Map<String, String> getDayMap(String startDate, String endDate) {
        Map<String, String> result = new HashMap<>();
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            result.put(DateUtils.formatDate(start), DateUtils.formatDate(start));
            start = start.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }
}
