package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

/**
 * 安全库存 MCK 预测包月安全库存结果表
 */
@Table("dws_inventory_health_mck_forecast_monthly_safe_df")
@Data
public class DwsInventoryHealthMckForecastMonthlySafeDfDO {
    @Column("stat_time")
    private LocalDate statTime;
    @Column("product_type")
    private String productType;

    @Column("instance_type")
    private String instanceType;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("area_name")
    private String areaName;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;
    @Column("holiday_year")
    private Integer holidayYear;
    @Column("holiday_month")
    private Integer holidayMonth;
    @Column("holiday_week")
    private Integer holidayWeek;

    @Column("holiday_week_start_date")
    private LocalDate holidayWeekStartDate;
    @Column("holiday_week_end_date")
    private LocalDate holidayWeekEndDate;

    @Column("week_index")
    private Integer weekIndex;

    /**
     * 预测安全库存预估=周转周数M*预测周转库存
     */
    @Column("monthly_safe_inv")
    private BigDecimal monthlySafeInv;

    /**
     * 预测安全库存预估=周转周数M*预测周转库存 - 不考虑供应波动
     */
    @Column("no_delivery_monthly_safe_inv")
    private BigDecimal noDeliveryMonthlySafeInv;

    /**
     * 安全库存的周转周数M=13周安全库存均值/13周实际周峰净增均值
     */
    @Column("turnover_week_num")
    private BigDecimal turnoverWeekNum;

    /**
     * 安全库存的周转周数M=13周安全库存均值/13周实际周峰净增均值 - 不考虑供应波动
     */
    @Column("no_delivery_turnover_week_num")
    private BigDecimal noDeliveryTurnoverWeekNum;

    /**
     * 预测周转库存
     */
    @Column("turnover_inv")
    private BigDecimal turnoverInv;

    /**
     * 13周安全库存均值
     */
    @Column("safe_inv_avg_13")
    private BigDecimal safeInvAvg13;

    /**
     * 13周安全库存均值 - 不考虑供应波动
     */
    @Column("no_delivery_safe_inv_avg_13")
    private BigDecimal noDeliverySafeInvAvg13;

    /**
     * 13周实际周峰净增均值
     */
    @Column("week_peak_avg_13")
    private BigDecimal weekPeakAvg13;

    public static DwsInventoryHealthMckForecastMonthlySafeDfDO from(DwsInventoryHealthMckForecastTurnoverDfDO turnover) {
        DwsInventoryHealthMckForecastMonthlySafeDfDO forecastMonthlySafeDfDO = new DwsInventoryHealthMckForecastMonthlySafeDfDO();
        forecastMonthlySafeDfDO.setStatTime(turnover.getStatTime());
        forecastMonthlySafeDfDO.setProductType(turnover.getProductType());
        forecastMonthlySafeDfDO.setCustomhouseTitle(turnover.getCustomhouseTitle());
        forecastMonthlySafeDfDO.setAreaName(turnover.getAreaName());
        forecastMonthlySafeDfDO.setRegionName(turnover.getRegionName());
        forecastMonthlySafeDfDO.setZoneName(turnover.getZoneName());
        forecastMonthlySafeDfDO.setInstanceType(turnover.getInstanceType());
        forecastMonthlySafeDfDO.setHolidayYear(turnover.getHolidayYear());
        forecastMonthlySafeDfDO.setHolidayMonth(turnover.getHolidayMonth());
        forecastMonthlySafeDfDO.setHolidayWeek(turnover.getHolidayWeek());
        forecastMonthlySafeDfDO.setHolidayWeekStartDate(turnover.getHolidayWeekStartDate());
        forecastMonthlySafeDfDO.setHolidayWeekEndDate(turnover.getHolidayWeekEndDate());
        forecastMonthlySafeDfDO.setWeekIndex(turnover.getWeekIndex());

        return forecastMonthlySafeDfDO;
    }

    public static String toGroupKey(DwsInventoryHealthMckForecastMonthlySafeDfDO item) {
        return StringTools.join("@", item.getProductType(), item.getCustomhouseTitle(), item.getAreaName(), item.getRegionName(), item.getZoneName(), item.getInstanceType());
    }
}

