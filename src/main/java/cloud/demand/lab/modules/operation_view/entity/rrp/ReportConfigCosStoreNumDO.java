package cloud.demand.lab.modules.operation_view.entity.rrp;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * 物理机cos到tb数的映射
 */
@Data
@ToString
@Table("report_config_cos_store_num")
public class ReportConfigCosStoreNumDO extends BaseDO {

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 折算后的tb数<br/>Column: [store_num] */
    @Column(value = "store_num")
    private BigDecimal storeNum;

    /** 规划产品<br/>Column: [plan_product] */
    @Column(value = "plan_product")
    private String planProduct;

}
