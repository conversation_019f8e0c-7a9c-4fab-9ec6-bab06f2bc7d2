package cloud.demand.lab.modules.operation_view.operation_view.model;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 安全库存阈值设置的请求体
 * By机型-可用区
 */
@Data
public class SafetyInvManualReq {
    // 日期
    @NotBlank
    private String date;

    @Valid
    List<Item> data;

    @Data
    @Accessors(chain = true)
    public static class Item {

        //  可用区
        @NotBlank(message = "可用区不能为空")
        private String zoneName;

        //  实例类型
        @NotBlank(message = "实例类型不能为空")
        private String instanceType;

        //  安全库存人工配置
        @NotNull(message = "安全库存人工配置不能为空")
        private Integer value;
    }

}
