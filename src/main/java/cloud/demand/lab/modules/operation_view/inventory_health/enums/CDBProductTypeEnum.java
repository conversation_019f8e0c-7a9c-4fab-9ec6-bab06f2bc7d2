package cloud.demand.lab.modules.operation_view.inventory_health.enums;

import lombok.Getter;

@Getter
public enum CDBProductTypeEnum {

    ONE("0", "本地盘"),
    TWO("1", "集群版"),
    THREE("2", "单节点");

    String code;
    String name;

    CDBProductTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public CDBProductTypeEnum getNameByCode(String code) {
        for (CDBProductTypeEnum value : CDBProductTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
