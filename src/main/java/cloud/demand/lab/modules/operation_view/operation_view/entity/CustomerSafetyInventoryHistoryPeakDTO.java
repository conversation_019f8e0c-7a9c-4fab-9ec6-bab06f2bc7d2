package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/** 按客户维度规模周峰 */
@Data
public class CustomerSafetyInventoryHistoryPeakDTO {
    @Column("year")
    private Integer year;

    @Column("month")
    private Integer month;

    @Column("week")
    private Integer week;

    @Column("product")
    private String product;

    /** 国内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("industry_dept")
    private String industryDept;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    @Column("uin")
    private String uin;

    /** 外部计费+内部服务 */
    @Column("logic_num")
    private BigDecimal logicNum;

    /** 内外都取计费 */
    @Column(value = "bill_num")
    private BigDecimal billNum;

    /** 内外都取服务 */
    @Column(value = "service_num")
    private BigDecimal serviceNum;

    public String getKey(){
        return StringUtils.joinWith("@",year,week,customhouseTitle,industryDept,customerShortName,uin);
    }

    public CustomerSafetyInventoryHistoryPeakDTO copy(){
        CustomerSafetyInventoryHistoryPeakDTO ret = new CustomerSafetyInventoryHistoryPeakDTO();
        ret.setYear(this.getYear());
        ret.setMonth(this.getMonth());
        ret.setWeek(this.getWeek());
        ret.setProduct(this.getProduct());
        ret.setCustomhouseTitle(this.getCustomhouseTitle());
        ret.setIndustryDept(this.getIndustryDept());
        ret.setCustomerShortName(this.getCustomerShortName());
        ret.setUin(this.getUin());
        ret.setLogicNum(this.getLogicNum());
        ret.setBillNum(this.getBillNum());
        ret.setServiceNum(this.getServiceNum());
        return ret;

    }
}
