package cloud.demand.lab.modules.operation_view.entity.web.common;

import cloud.demand.lab.common.utils.ORMUtils;
import gramel.JsonrpcException;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class Page {

    @NotNull(message = "分页起点不能为空")
    private int start = 0;
    @NotNull(message = "分页大小不能为空")
    private int size = 10;

    public int getPageIndex() {
        return ORMUtils.toPageIndex(start, size);
    }

    public void checkPage() {
        if (start <= 0) {
            throw new JsonrpcException(20000, "page start must be greater than 0", null);
        }
        if (size <= 0) {
            throw new JsonrpcException(20000, "page size must be greater than 0", null);
        }
    }
}
