package cloud.demand.lab.modules.operation_view.inventory_health.dto.actual;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.BigDecimal2ScaleSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class InventoryHealthTrendResp {
    private List<Item> data;

    /**
     * 返回所有机型组合
     */
    private List<String> combineList;

    //  计算过程中出现的报错信息
    private String errorMsg;

    @Data
    public static class Item {

        /**
         * 日期
         */
        private String date;

        /**
         * 实例类型
         */
        private String instanceType;

        /**
         * 展开字段
         */
        private String extendField;

        /**
         * 是否展示预扣
         */
        private Boolean isContainPreDeduct;


        /**
         * 实际库存核心数
         */
        private Integer actualInventoryCore;


        /**
         * 用户预扣核心数
         */
        private Integer preDeductInventoryCore;

        /**
         * 安全库存核心数 = 包年包月 + 弹性用量
         */
        private Integer safetyInventoryCore;

        /**
         * 包年包月 = z(90%) * D * MAPE * sqrt(LT/t)
         */
        private Integer prePaidSafetyInventoryCore;

        /**
         * 弹性用量 = X%(服务水平）*L(弹性量/天)
         */
        private BigDecimal bufferSafetyInventoryCore;

        /**
         * 实际库存/安全库存
         */
        private BigDecimal healthRatio;

        // 以下是参与计算的值

        /**
         * 交付SLA
         */
        private Integer sla;

        /**
         * 服务水平
         */
        private BigDecimal serviceLevel;

        /**
         * 服务水平系数
         */
        private BigDecimal serviceLevelFactor;

        /**
         * 需求预测量(核心数)
         */
        private Integer forecastDemandCore;

        /**
         * 需求预测量(核心数)-外部行业
         */
        private Integer forecastDemandCoreOuter;

        /**
         * 需求预测量(核心数)-内部领用
         */
        private Integer forecastDemandCoreInner;

        /**
         * 需求标准差
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal standardDiff;

        /**
         * 需求平均值
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal demandAvg;

        /**for未来预测 中长尾客户包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal mediumLongTailSafetyInv;

        /**for未来预测 头部-战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headZlkhbSafetyInv;

        /**for未来预测 头部-非战略包月安全库存 */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal headNotZlkhbSafetyInv;

        /**
         * 需求预测准确率
         */
        private BigDecimal forecastDemandAccuracyRate;

        /**
         * 需求预测开始时间
         */
        private String forecastDemandStartDate;

        /**
         * 需求预测结束时间
         */
        private String forecastDemandEndDate;

        /**
         * 弹性服务水平
         */
        private BigDecimal bufferServiceLevel;

        /**
         * 弹性服务水平系数
         */
        private BigDecimal bufferServiveLevelFactor;

        /**
         * 弹性ROI
         */
        private BigDecimal bufferRoi;

        /**
         * 弹性利用率
         */
        private BigDecimal bufferRate;

        /**
         * 弹性用量平均核心数
         */
        private Integer bufferAverageCore;

        /**
         * 弹性用量开始时间
         */
        private String bufferAverageStartDate;

        /**
         * 弹性用量结束时间
         */
        private String bufferAverageEndDate;

        /**
         * 计算出安全库存核心数的表达式
         */
        private String safetyInventoryCoreExpression;

        /**
         * 云上真实服务水平
         */
        private BigDecimal actualServiceLevel;

        /**
         * 云上真实服务水平
         */
        private BigDecimal actualServiceLevelWeight;

        private List<DwsCloudServerLevelDO> actualSla;

        /**
         * 针对组合机型，陈列每个机型详情
         */
        private List<InventoryHealthActualResp.Item> details;


//        /**
//         * 从参数日期开始，近一个月的原因分析
//         */
//        private List<InventoryHealthActualResp.ReasonItem> reasons;

        /**
         * 预扣核心数
         */
        private Integer reservedCores;

        /**
         * 13周平均交付时长
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryAvg;

        /**
         * 13周交付标准差
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal deliveryStandardDiff;

        /**
         * 安全库存人工调整
         */
        private BigDecimal safeInvManualConfig;

        /**
         * 周转库存
         */
        private BigDecimal turnoverInv;

        /**
         * 周转库存 - 当周执行量周峰
         */
        private BigDecimal turnoverWeekPeakCore;
        /**
         * 周转库存 - 当周预扣均值
         */
        private BigDecimal turnoverWeekReservedAvgCore;

        /**
         * 预测周转库存
         */
        private BigDecimal forecastTurnoverInv;

        /**
         * 预测周转库存 - 过去12周周峰均值
         */
        private BigDecimal forecastTurnoverWeekPeakAvg12Core;
        /**
         * 预测周转库存 - 最新版本对当周的预测
         */
        private BigDecimal forecastTurnoverWeekDemandCore;

    }

}
