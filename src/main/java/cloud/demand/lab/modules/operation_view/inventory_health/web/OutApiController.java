package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.QueryServiceLevelReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleOutReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.CBSDeliveryDataReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryDisassembleService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthActualV2Service;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/api")
public class OutApiController {

    @Resource
    private InventoryHealthActualV2Service inventoryHealthActualV2Service;

    @Resource
    private InventoryDisassembleService inventoryDisassembleService;

    @RequestMapping
     public Object queryServiceLevelData(@JsonrpcParam QueryServiceLevelReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if (req.getIsCumulative() == null) {
            req.setIsCumulative(false);
        }
        if (req.getIsCumulative()) {
            return inventoryHealthActualV2Service.queryServiceLevelSummaryData(req);
        }
        return inventoryHealthActualV2Service.queryServiceLevelData(req);

    }


    @RequestMapping
    public Object queryInventoryDisassembleData(@JsonrpcParam InventoryDisassembleOutReq req) {
         if(req == null) {
             throw new WrongWebParameterException("请求参数不能为空");
         }
        List<String> types = Arrays.asList("zoneName", "regionName", "areaName", "customhouseTitle");
         if (StringUtils.isBlank(req.getType()) || !types.contains(req.getType())) {
             throw new WrongWebParameterException("type参数异常");
         }
         return inventoryDisassembleService.queryInventoryDisassembleOut(req);
    }

    @RequestMapping
    public Object queryCBSDeliveryData(@JsonrpcParam CBSDeliveryDataReq req) {
        if(req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        LocalDate localDate = DateUtils.parseLocalDate(req.getStatTime());
        if (localDate == null) {
            throw new WrongWebParameterException("日期格式错误");
        }
        Integer spanNum = req.getSpanNum();
        if (spanNum == null || spanNum >= 0) {
            throw new WrongWebParameterException("spanNum设置有误");
        }
        return inventoryHealthActualV2Service.getAllCBSDeliveryData(req.getStatTime(), req.getSpanNum());
    }



}
