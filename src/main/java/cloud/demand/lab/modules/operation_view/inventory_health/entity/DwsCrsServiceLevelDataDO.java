package cloud.demand.lab.modules.operation_view.inventory_health.entity;


import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dws_crs_service_level_data")
public class DwsCrsServiceLevelDataDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 线上最大装机内存量<br/>Column: [max_sell_mem] */
    @Column(value = "max_sell_mem")
    private Integer maxSellMem;

    /** 线上剩余内存量<br/>Column: [rest_mem] */
    @Column(value = "rest_mem")
    private Integer restMem;

    /** 线上最小内存量<br/>Column: [min_rest_mem] */
    @Column(value = "min_rest_mem")
    private Integer minRestMem;

    /** 线上物理机设备数量<br/>Column: [machine_cnt] */
    @Column(value = "machine_cnt")
    private Integer machineCnt;

    /** 发货成功数量<br/>Column: [suc_cnt] */
    @Column(value = "suc_cnt")
    private Integer sucCnt;

    /** 发货总量<br/>Column: [total_cnt] */
    @Column(value = "total_cnt")
    private Integer totalCnt;

}
