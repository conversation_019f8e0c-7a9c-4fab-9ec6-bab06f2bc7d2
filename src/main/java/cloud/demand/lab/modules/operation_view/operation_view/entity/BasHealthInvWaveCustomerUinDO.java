package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * 安全库存客户波动的大客户
 */
@Data
@ToString
@Table("bas_health_inv_wave_customer_uin")
public class BasHealthInvWaveCustomerUinDO implements IVersionBasTableDO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户uin<br/>Column: [uin] */
    @Column(value = "uin")
    private String uin;

    /** 版本号：用于区分不同版本记录<br/>Column: [version] */
    @Column(value = "version")
    private Long version;

    /** 是否为默认版本，1：是，0：否<br/>Column: [default_flag] */
    @Column(value = "default_flag")
    private String defaultFlag;

    /** 创建的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;
}
