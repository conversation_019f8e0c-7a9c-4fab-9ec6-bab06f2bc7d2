package cloud.demand.lab.modules.operation_view.operation_view.model;

import cloud.demand.lab.modules.operation_view.enums.DefaultFlagEnum;
import cloud.demand.lab.modules.operation_view.operation_view.entity.BasHealthInvWaveCustomerUinDO;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class BasHealthInvWaveCustomerUinExcel {

    /** 客户uin<br/>Column: [uin] */
    @ExcelProperty(value = "客户uin",index = 0)
    private String uin;

    /** 客户简称<br/>Column: [customer_short_name] */
    @ExcelProperty(value = "客户简称",index = 1)
    private String customerShortName;


    public static BasHealthInvWaveCustomerUinDO transform(BasHealthInvWaveCustomerUinExcel o) {
        BasHealthInvWaveCustomerUinDO basHealthInvWaveCustomerUinDO = new BasHealthInvWaveCustomerUinDO();
        basHealthInvWaveCustomerUinDO.setCustomerShortName(o.getCustomerShortName());
        basHealthInvWaveCustomerUinDO.setUin(o.getUin());
        basHealthInvWaveCustomerUinDO.setDefaultFlag(DefaultFlagEnum.YES.getCode());
        return basHealthInvWaveCustomerUinDO;

    }
}
