package cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeeklyScaleDfDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import org.nutz.lang.Strings;

/**
 * 安全库存-未来预测模型的头部-非战略客户部计算DTO
 */
@Data
public class SafetyInventoryFutureOtherDTO {

    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 赢率 */
    @Column(value = "win_rate")
    private BigDecimal winRate;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "logic_num")
    private BigDecimal logicNum;

    private String product;

    private Integer year;

    private Integer month;

    //  当前月份的节假周数
    private Integer weekCount;

    private String customhouseTitle;

    private String areaName;

    //  基于赢率算出的需求-月度
    private BigDecimal result;

    //  基于赢率算出的月度需求根据节假周当月周数均分的周需求
    private BigDecimal weekResult;

    /**
     * 获取分组k
     */
    public String getKey(){
        return Strings.join("@", year.toString(), month.toString(), regionName, zoneName, instanceType, product);
    }

    public String getYearMonth(){
        return Strings.join("@", year.toString(), month.toString());
    }

    public static DwsInventoryHealthWeeklyScaleDfDO transform(SafetyInventoryFutureOtherDTO source, HolidayWeekInfoDTO dto){
        DwsInventoryHealthWeeklyScaleDfDO dwsInventoryHealthWeeklyScaleDfDO = new DwsInventoryHealthWeeklyScaleDfDO();
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayYear(dto.getYear());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayMonth(dto.getMonth());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeek(dto.getWeek());
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeekStartDate(LocalDate.parse(dto.getStartDate()));
        dwsInventoryHealthWeeklyScaleDfDO.setHolidayWeekEndDate(LocalDate.parse(dto.getEndDate()));
        dwsInventoryHealthWeeklyScaleDfDO.setWeekIndex(dto.getWeekNFromNow());
        dwsInventoryHealthWeeklyScaleDfDO.setProductType(source.getProduct());
        dwsInventoryHealthWeeklyScaleDfDO.setInstanceType(source.getInstanceType());
        dwsInventoryHealthWeeklyScaleDfDO.setCustomhouseTitle(source.getCustomhouseTitle());
        dwsInventoryHealthWeeklyScaleDfDO.setAreaName(source.getAreaName());
        dwsInventoryHealthWeeklyScaleDfDO.setRegionName(source.getRegionName());
        dwsInventoryHealthWeeklyScaleDfDO.setZoneName(source.getZoneName());
        return dwsInventoryHealthWeeklyScaleDfDO;
    }


}
