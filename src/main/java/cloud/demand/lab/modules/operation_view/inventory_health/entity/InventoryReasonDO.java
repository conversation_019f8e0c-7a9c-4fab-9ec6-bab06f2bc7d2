package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;

@Data
@Table("inventory_health_reason")
public class InventoryReasonDO extends BaseDO {
    @Column(value = "reason_type")
    private String reasonType; // '原因分类：客户预扣、客户弹性购买、客户包月购买、库存线下调度、其他原因'
    @Column(value = "reason_detail")
    private String reasonDetail; // '原因描述'
    @Column(value = "date")
    private Date date; // 日期格式为 'YYYY-MM-DD'
    @Column(value = "instance_type")
    private String instanceType; // '实例类型'
    @Column(value = "zone_name")
    private String zoneName; // '可用区'
}
