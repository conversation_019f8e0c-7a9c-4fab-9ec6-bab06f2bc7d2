package cloud.demand.lab.modules.operation_view.operation_view.model;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;

@Data
@ToString
public class OperationViewReq2 {

    /** 选择日期 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /** 客户类型，传code进来
     * @see cloud.demand.lab.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum
     */
    private String customerCustomGroup;

    /** 库存类型：线上库存、线下搬迁、线下流转 */
    private List<String> lineType;

    /** 好差呆：好料、差料、呆料*/
    private List<String> materialType;

    /** 库存细项：大核库存、小核库存、大核预留等*/
    private List<String> invDetailType;

    /**
     * 剔除uin列表，后端收到之后要进行从小到大排序，并用;隔开
     */
    private List<String> excludeUinList;

    /**实例类型*/
    private List<String> instanceType;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /** 是否需要未来13周的安全库存Map，只有true才处理，按需返回
     *  目前仅会在库存预测的接口中置为true
     */
    private Boolean isForecast;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /**
     * 是否组合机型
     * 为了适配库存健康组合机型的优先级改动
     */
    private Boolean isCombine;

    /**
     * 取主力机型、主力可用区时对应的日期，如果为 null，默认为 statTime
     */
    private String categoryDate;

    public String getCategoryDate() {
        if (Strings.isBlank(categoryDate)) {
            return DateUtils.formatDate(date);
        }
        return categoryDate;
    }
    /**
     * 处理待剔除的uin列表，首先转换为List<Integer>，对其正向排序后以;为分隔符拼接
     */
    public String handleExcludeUinList(){
        List<Long> result = Lang.list();
        for (String uin : excludeUinList) {
            //  去掉两边空格
            uin = uin.trim();
            Long each = NumberUtils.parseLong(uin);
            if (each != null) {
                result.add(each);
            }else {
                throw new WrongWebParameterException("请输入正确的uin!");
            }
        }
        ListUtils.sortAscNullLast(result, o -> o);
        return Strings.join(";", result);
    }

    /**
     * 暴露给外部用
     * 处理待剔除的uin列表，首先转换为List<Integer>，对其正向排序后以;为分隔符拼接
     */
    public static String handleExcludeUinList(List<String> uinsList){
        List<Long> result = Lang.list();
        for (String uin : uinsList) {
            //  去掉两边空格
            uin = uin.trim();
            Long each = NumberUtils.parseLong(uin);
            if (each != null) {
                result.add(each);
            }
        }
        ListUtils.sortAscNullLast(result, o -> o);
        return Strings.join(";", result);
    }


    public WhereSQL genCondition(){
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }
        return condition;
    }

    public static OperationViewReq2 fromInstanceModelReq(OperationViewInstanceModelReq req) {
        OperationViewReq2 result = new OperationViewReq2();
        result.setDate(req.getDate());
        result.setCustomerCustomGroup(req.getCustomerCustomGroup());
        result.setInstanceType(req.getInstanceType());
        result.setInstanceTypeCategory(req.getInstanceTypeCategory());
        result.setZoneName(req.getZoneName());
        result.setZoneCategory(req.getZoneCategory());
        result.setCustomhouseTitle(req.getCustomhouseTitle());
        result.setAreaName(req.getAreaName());
        result.setRegionName(req.getRegionName());
        return result;
    }
}
