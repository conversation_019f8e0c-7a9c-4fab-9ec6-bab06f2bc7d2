package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPplForecastDetailDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class InventoryHealthPplForecastDetailVO extends InventoryHealthPplForecastDetailDO {

    @Column(value = "sumTotalCore", computed = "sum(total_core)")
    private Integer sumTotalCore;

    @Column(value = "sumTotalOuterCore", computed = "sum(case when forecast_model_source_type = 'INDUSTRY' then total_core else 0 end)")
    private Integer sumTotalOuterCore;

    @Column(value = "sumTotalInnerCore", computed = "sum(case when forecast_model_source_type = 'INNER' then total_core else 0 end)")
    private Integer sumTotalInnerCore;

    // 以下是程序后处理存放的数据

    private Integer sla;

    private Integer safetyInventoryCore;

    private Integer prePaidDemand;

    /**服务水平*/
    private BigDecimal serviceLevel;

    /**服务水平系数*/
    private BigDecimal serviceLevelFactor;

    /**需求预测准确率*/
    private BigDecimal forecastDemandAccuracyRate;

    /**需求预测开始时间*/
    private String forecastDemandStartDate;

    /**需求预测结束时间*/
    private String forecastDemandEndDate;

    /**计算公式，用于排查问题*/
    private String expression;

}
