package cloud.demand.lab.modules.operation_view.inventory_health.web;


import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthOverviewService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/inventory-overview")
public class InventoryHealthOverviewController {

    @Resource
    InventoryHealthOverviewService inventoryHealthOverviewService;


    @RequestMapping
    public Object queryInventoryOverviewReport(@JsonrpcParam InventoryOverviewReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return inventoryHealthOverviewService.queryInventoryOverviewReport(req);
    }

    @RequestMapping
    public Object queryInventoryOverviewTrendReport(@JsonrpcParam InventoryOverviewTrendReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return inventoryHealthOverviewService.queryInventoryOverviewTrendReport(req);
    }

    @RequestMapping
    public Object queryZoneConfig() {
        return inventoryHealthOverviewService.getAllInventoryHealthZoneConfig();
    }

    @RequestMapping
    public Object queryRateConfig() {
        return inventoryHealthOverviewService.getAllInventoryHealthConfig();
    }
}
