package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_turnover;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class InventoryTurnoverResp {

    private List<Item> data;

    @Data
    public static class Item {

        /**
         * 统计时间
         */
        private String statisticTime;

        /**
         * 境内外
         */
        private String customhouseTitle;

        /**
         * 国家
         */
        private String countryName;

        /**
         * 机型类型
         */
        private String instanceCategory;

        /**
         * 可用区类型
         */
        private String zoneCategory;

        /**
         * 地域
         */
        private String regionName;

        /**
         * 可用区
         */
        private String zoneName;

        /**
         * 实例类型
         */
        private String instanceType;

        /**
         * 售卖流水累加
         */
        private BigDecimal sellRevenue;

        /**
         * 退回核数
         */
        private BigDecimal returnCores;

        /**
         * 转出核数
         */
        private BigDecimal outCores;

        /**
         * 平均售卖流水
         */
        private BigDecimal averageSellRevenue;

        /**
         * 平均库存
         */
        private BigDecimal averageInventory;

        /**
         * ito
         */
        private BigDecimal ito;

        /**
         * 安全库存
         */
        private BigDecimal safeInventory;
    }

}
