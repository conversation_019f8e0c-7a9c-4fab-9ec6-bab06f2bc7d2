package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("inventory_health_future_forecast_detail")
public class InventoryHealthFutureForecastDetailDO extends BaseDO {

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 腾讯云境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 腾讯云地域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 腾讯云区域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 腾讯云可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 节假年<br/>Column: [holiday_year] */
    @Column(value = "holiday_year")
    private Integer holidayYear;

    /** 节假月<br/>Column: [holiday_month] */
    @Column(value = "holiday_month")
    private Integer holidayMonth;

    /** 节假周<br/>Column: [holiday_week] */
    @Column(value = "holiday_week")
    private Integer holidayWeek;

    /** 节假周基于当前是第几周，从0开始<br/>Column: [week_index] */
    @Column(value = "week_index")
    private Integer weekIndex;

    /** 实际库存<br/>Column: [actual_inventory] */
    @Column(value = "actual_inventory")
    private Integer actualInventory;

    /** 安全库存<br/>Column: [safety_inventory] */
    @Column(value = "safety_inventory")
    private Integer safetyInventory;

    /** 模拟库存<br/>Column: [simulate_inventory] */
    @Column(value = "simulate_inventory")
    private Integer simulateInventory;

    /** 采购到货<br/>Column: [future_purchase] */
    @Column(value = "future_purchase")
    private Integer futurePurchase;

    /** 搬迁到货<br/>Column: [future_moving] */
    @Column(value = "future_moving")
    private Integer futureMoving;

    /** 需求预测-新增<br/>Column: [demand_forecast_new] */
    @Column(value = "demand_forecast_new")
    private Integer demandForecastNew;

    /** 需求预测-退回<br/>Column: [demand_forcast_return] */
    @Column(value = "demand_forecast_return")
    private Integer demandForcastReturn;

    /** 需求预测-净增<br/>Column: [demand_forecast_total] */
    @Column(value = "demand_forecast_total")
    private Integer demandForecastTotal;

    /** 周转库存<br/>Column: [turnover_inventory] */
    @Column(value = "turnover_inventory")
    private Integer turnoverInventory;

}
