package cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OperationActionDetailDO {
    /**
     * 设备类型
     */
    @Column("device_type")
    private String deviceType;
    /**
     * 行业部门
     */
    @Column("xy_industry")
    private String industryDept;
    /**
     * 区域
     */
    @Column("quota_campus_name")
    private String campus;
    /**
     * 客户名称
     */
    @Column("xy_customer_name")
    private String customName;
    /**
     * cloud提单时间
     */
    @Column("quota_create_time")
    private String createTime;
    /**
     * Q单号
     */
    @Column("quota_id")
    private String quotaId;
    /**
     * 预计交付时间
     */
    @Column("sla_date_expect")
    private String expectDeliveryTime;
    /**
     * 期望交付时间
     */
    @Column("quota_use_time")
    private String useTime;
    /**
     * 需求核心数
     */
    @Column("demand_core_num")
    private BigDecimal demandCoreNum;
    /**
     * 未到货核心数
     */
    @Column("no_arrival_core_num")
    private BigDecimal noArrivalCoreNum;

}
