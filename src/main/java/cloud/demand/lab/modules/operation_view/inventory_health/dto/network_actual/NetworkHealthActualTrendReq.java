package cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class NetworkHealthActualTrendReq {

    private String dateType;

    private String start;

    private String end;

    private List<String> customhouseTitle;

    private List<String> regionName;

    private List<String> country;

    private List<String> areaName;

    private List<String> product;


    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(areaName)) {
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(country)) {
            condition.and("country_name in (?)", country);
        }
        if (ListUtils.isNotEmpty(product)) {
            condition.and("network_product in (?)", product);
        }
        condition.and("stat_time between ? and ?", start, end);
        return condition;
    }

}
