package cloud.demand.lab.modules.operation_view.operation_view.model;

import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsDemandWeekNCustomerWaveDO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class DwsDemandWeekNCustomerWaveExcel {
    // 下面是该条 PPL 对应的节假周信息，后续代码填充，一条数据可能会被平均到多个 ppl item 中
    @ExcelProperty(value = "切片时间",index = 0)
    private String statTime;

    @ExcelProperty(value = "产品",index = 1)
    private String product;
    /**
     * holiday_year            Int32          default 0 comment '安全库存计算的节假年',
     *     holiday_month           Int32          default 0 comment '安全库存计算的节假月',
     *     holiday_week            Int32          default 0 comment '安全库存计算的节假周',
     *     holiday_week_start_date Date           default '1970-01-01' comment '节假周开始时间',
     *     holiday_week_end_date   Date           default '1970-01-01' comment '节假周结束时间',
     */
    @ExcelProperty(value = "起始年",index = 2)
    private Integer startHolidayYear;
    @ExcelProperty(value = "起始月",index = 3)
    private Integer startHolidayMonth;
    @ExcelProperty(value = "起始周",index = 4)
    private Integer startHolidayWeek;

    @ExcelProperty(value = "结束年",index = 5)
    private Integer endHolidayYear;
    @ExcelProperty(value = "结束月",index = 6)
    private Integer endHolidayMonth;
    @ExcelProperty(value = "结束周",index = 7)
    private Integer endHolidayWeek;

    @ExcelProperty(value = "提前预测周",index = 8)
    private Integer weekN;

    @ExcelProperty(value = "境内外",index = 9)
    private String customhouseTitle;

    @ExcelProperty(value = "客户简称",index = 10)
    private String customerShortName;

    @ExcelProperty(value = "客户uin",index = 11)
    private String uin;


    /** 均摊后核心数-13周均值 */
    @ExcelProperty(value = "客户W5需求（13周均值）",index = 12)
    private BigDecimal averageTotalAvg13Core;

    /** 周峰外部计费内部服务-13周均值 */
    @ExcelProperty(value = "客户执行规模（13周均值）",index = 13)
    private BigDecimal logicAvg13Num;


    /** 客户波动（均摊后核心数-周峰外部计费内部服务）-13周平方总和后开方 */
    @ExcelProperty(value = "客户波动量（sqrt(sum((客户W5需求-客户执行规模)^2))）",index = 14)
    private BigDecimal customerWaveLogicSqrtCore;


    public static DwsDemandWeekNCustomerWaveExcel transform(DwsDemandWeekNCustomerWaveDO object) {
        DwsDemandWeekNCustomerWaveExcel ret = new DwsDemandWeekNCustomerWaveExcel();
        ret.setStatTime(DateUtils.format(object.getStatTime()));
        ret.setProduct(object.getProduct());
        ret.setStartHolidayYear(object.getStartHolidayYear());
        ret.setStartHolidayMonth(object.getStartHolidayMonth());
        ret.setStartHolidayWeek(object.getStartHolidayWeek());
        ret.setEndHolidayYear(object.getEndHolidayYear());
        ret.setEndHolidayMonth(object.getEndHolidayMonth());
        ret.setEndHolidayWeek(object.getEndHolidayWeek());
        ret.setWeekN(object.getWeekN());
        ret.setCustomhouseTitle(object.getCustomhouseTitle());
        ret.setCustomerShortName(object.getCustomerShortName());
        ret.setUin(object.getUin());
        ret.setAverageTotalAvg13Core(object.getAverageTotalAvg13Core());
        ret.setLogicAvg13Num(object.getLogicAvg13Num());
        ret.setCustomerWaveLogicSqrtCore(object.getCustomerWaveLogicSqrtCore());
        return ret;
    }
}
