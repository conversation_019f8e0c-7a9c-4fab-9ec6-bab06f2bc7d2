package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Map;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString
@Table("dws_cbs_service_level_data_df")
public class DwsCbsServiceLevelDataDfDO {

    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    @Column(value = "appId")
    private String appId;

    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户类型<br/>Column: [customer_group] */
    @Column(value = "customer_group")
    private String customerGroup;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "pay_mode")
    private String payMode;

    @Column(value = "pool")
    private String pool;

    /** 成功数<br/>Column: [success_count] */
    @Column(value = "success_count")
    private Integer successCount;

    @Column(value = "failed_count")
    private Integer failedCount;

    /** 成功磁盘大小<br/>Column: [success_disk_size] */
    @Column(value = "success_disk_size")
    private Integer successDiskSize;

    /** 失败磁盘大小<br/>Column: [fail_disk_size] */
    @Column(value = "fail_disk_size")
    private Integer failDiskSize;

    public static DwsCbsServiceLevelDataDfDO genBasicData(CbsServiceLevelProcessedRawDataDO origin,
            Map<String, StaticZoneDO> zoneMap, Map<String, String> zoneToCountry, Map<String, String> appidMap) {
        DwsCbsServiceLevelDataDfDO data = new DwsCbsServiceLevelDataDfDO();
        data.setStatTime(DateUtils.formatDate(origin.getOperationDate()));
        data.setZoneName(origin.getCbsZoneName());
        data.setAppId(String.valueOf(origin.getAppid()));
        data.setIndustryDept(origin.getOrganizationName());
        data.setCustomerGroup(origin.getCustomerGroup());
        data.setVolumeType(origin.getVolumeType());
        data.setPayMode(origin.getPayMode());
        data.setPool(origin.getPool());
        data.setSuccessCount(origin.getSuccessCount());
        data.setFailedCount(origin.getFailedCount());
        data.setSuccessDiskSize(origin.getSuccessDiskSize());
        data.setFailDiskSize(origin.getFailDiskSize());
        StaticZoneDO staticZoneDO = zoneMap.get(data.getZoneName());
        if (staticZoneDO != null) {
            data.setRegionName(staticZoneDO.getRegionName());
            data.setAreaName(staticZoneDO.getAreaName());
            data.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
        }else {
            data.setRegionName("(空值)");
            data.setAreaName("(空值)");
            data.setCustomhouseTitle("(空值)");
        }
        String customerName = appidMap.get(data.getAppId());
        data.setCustomerShortName(StringUtils.isBlank(customerName) ? "(空值)" : customerName);
        String country = zoneToCountry.get(data.getZoneName());
        data.setCountry(StringUtils.isNotBlank(country) ? country : "(空值)");
        return data;
    }
}
