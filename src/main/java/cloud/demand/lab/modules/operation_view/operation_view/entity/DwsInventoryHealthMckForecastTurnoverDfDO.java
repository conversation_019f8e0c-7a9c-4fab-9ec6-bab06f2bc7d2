package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

/**
 * 安全库存 MCK 周转库存算法计算结果
 */
@Table("dws_inventory_health_mck_forecast_turnover_df")
@Data
public class DwsInventoryHealthMckForecastTurnoverDfDO {
    @Column("stat_time")
    private LocalDate statTime;
    @Column("instance_type")
    private String instanceType;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("area_name")
    private String areaName;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;
    @Column("holiday_year")
    private Integer holidayYear;
    @Column("holiday_month")
    private Integer holidayMonth;
    @Column("holiday_week")
    private Integer holidayWeek;

    @Column("holiday_week_start_date")
    private LocalDate holidayWeekStartDate;
    @Column("holiday_week_end_date")
    private LocalDate holidayWeekEndDate;

    @Column("week_index")
    private Integer weekIndex;

    /**
     * 预测周转库存=（T0周过去12周周峰均值*12+最新版本的本周预测净增值）/13
     */
    @Column("turnover_inv")
    private BigDecimal turnoverInv;

    /**
     * T0周过去12周周峰均值
     */
    @Column("week_peak_avg12_core")
    private BigDecimal weekPeakCore;
    /**
     * WN 预测净增值，均摊之后
     */
    @Column("avg_forecast_core")
    private BigDecimal avgForecastCore;

    @Column("total_forecast_core")
    private BigDecimal totalForecastCore;

    @Column("product_type")
    private String productType;
}
