package cloud.demand.lab.modules.operation_view.inventory_health.dto.future;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPplForecastDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPurchaseFutureDetailDO;
import java.util.List;
import lombok.Data;

@Data
public class InventoryHealthFutureViewResp {

    private List<Item> data;

    @Data
    public static class Item {

        private Integer holidayYear;

        private Integer holidayWeek;

        /**周index，0表示第0周，1表示第1周*/
        private Integer weekIndex;

        /** 行业部门 */
        private String industryDept;

        /**净增预测核心数，第1周起有数据*/
        private Integer demandForecastTotalCore;

        /** 未来采购核心数 */
        private Integer purchaseCore;

        public static Item from(InventoryHealthPurchaseFutureDetailDO d) {
            InventoryHealthFutureViewResp.Item item = new InventoryHealthFutureViewResp.Item();
            item.setHolidayYear(d.getPromiseDeliveryHolidayYear());
            item.setHolidayWeek(d.getPromiseDeliveryHolidayWeek());
            item.setIndustryDept(d.getIndustryDept());
            item.setPurchaseCore(d.getCoreNum());
            return item;
        }

        public static Item from(InventoryHealthPplForecastDetailDO d) {
            InventoryHealthFutureViewResp.Item item = new InventoryHealthFutureViewResp.Item();
            item.setHolidayYear(d.getHolidayYear());
            item.setHolidayWeek(d.getHolidayWeek());
            item.setIndustryDept(d.getIndustryDept());
            item.setDemandForecastTotalCore(d.getTotalCore());
            return item;
        }

        public static String getGroupK(Item item){
            return String.join("@",
                    item.getHolidayYear().toString(), item.getHolidayWeek().toString(),
                    item.getWeekIndex().toString(), item.getIndustryDept());
        }

        public static Item copy(Item source){
            Item item = new Item();
            item.setHolidayYear(source.getHolidayYear());
            item.setHolidayWeek(source.getHolidayWeek());
            item.setWeekIndex(source.getWeekIndex());
            item.setIndustryDept(source.getIndustryDept());
            return item;
        }
    }

}
