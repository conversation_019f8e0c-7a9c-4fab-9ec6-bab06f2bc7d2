package cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class CommonRestockForecastDataItemDO extends CommonRestockDataItemDO {
    @Column("demand_source")
    private String demandSource;
    @Column("demand_type")
    private String demandType;
    @Column("compare")
    private String compare;
    @Column("is_spike")
    private Integer isSpike;
    @Column("is_head")
    private Integer isHead;
    @Column("customer_short_name")
    private String customerShortName;
    @Column("region_name")
    private String regionName;
}
