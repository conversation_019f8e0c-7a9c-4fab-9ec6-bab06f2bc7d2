package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Table("dws_inventory_health_weekly_delivery_df")
@Data
public class DwsInventoryHealthWeeklyDeliveryDfDO {
    @Column(value = "stat_time")
    private String statTime;

    @Column(value = "holiday_year")
    private Integer holidayYear;

    @Column(value = "holiday_month")
    private Integer holidayMonth;

    @Column(value = "holiday_week")
    private Integer holidayWeek;

    @Column(value = "holiday_week_start_date")
    private String holidayWeekStartDate;

    @Column(value = "holiday_week_end_date")
    private String holidayWeekEndDate;

    @Column(value = "week_index")
    private Integer weekIndex;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "device_type")
    private String deviceType;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "area_name")
    private String areaName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "campus")
    private String campus;

    @Column(value = "delivery_status")
    private String deliveryStatus;

    @Column(value = "quota_use_time")
    private LocalDateTime quotaUseTime;

    @Column(value = "erp_actual_date")
    private LocalDateTime erpActualDate;

    @Column(value = "xy_create_time")
    private LocalDateTime xyCreateTime;

    @Column(value = "quota_create_time")
    private LocalDateTime quotaCreateTime;

    @Column(value = "cloud_delivery_time")
    private LocalDateTime cloudDeliveryTime;

    @Column(value = "xy_approval_days")
    private BigDecimal xyApprovalDays;

    @Column(value = "delivery_days")
    private BigDecimal deliveryDays;

    @Column(value = "sla")
    private BigDecimal sla;

    @Column(value = "num")
    private Integer num;

    public String toKey() {
        return StringTools.join("@", "CVM", customhouseTitle, areaName, regionName, zoneName, instanceType);
    }
}
