package cloud.demand.lab.modules.operation_view.entity.p2p;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("server_parts_extended_info")
public class ServerPartsExtendedInfoDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 版本<br/>Column: [device_version] */
    @Column(value = "device_version")
    private String deviceVersion;

    /** 处理器型号缩写<br/>Column: [cpu_abbr] */
    @Column(value = "cpu_abbr")
    private String cpuAbbr;

    /** 处理器厂家<br/>Column: [cpu_vender] */
    @Column(value = "cpu_vender")
    private String cpuVender;

    /** 处理器系列<br/>Column: [cpu_series] */
    @Column(value = "cpu_series")
    private String cpuSeries;

    /** 单处理器核心数量<br/>Column: [cpu_core] */
    @Column(value = "cpu_core")
    private Integer cpuCore;

    /** 处理器个数<br/>Column: [cpu_number] */
    @Column(value = "cpu_number")
    private Integer cpuNumber;

    /** 处理器物理核数<br/>Column: [cpu_physics_core] */
    @Column(value = "cpu_physics_core")
    private Integer cpuPhysicsCore;

    /** 处理器逻辑核数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** 内存缩写<br/>Column: [memory_abbr] */
    @Column(value = "memory_abbr")
    private String memoryAbbr;

    /** 单内存容量(GB)<br/>Column: [memory_volume] */
    @Column(value = "memory_volume")
    private Integer memoryVolume;

    /** 内存条数<br/>Column: [memory_number] */
    @Column(value = "memory_number")
    private Integer memoryNumber;

    /** 总内存容量<br/>Column: [memory_total_vol] */
    @Column(value = "memory_total_vol")
    private Integer memoryTotalVol;

    /** 硬盘缩写<br/>Column: [disk_abbr] */
    @Column(value = "disk_abbr")
    private String diskAbbr;

    /** 硬盘2缩写<br/>Column: [disk2_abbr] */
    @Column(value = "disk2_abbr")
    private String disk2Abbr;

    /** 硬盘规格(GB)<br/>Column: [disk_volume] */
    @Column(value = "disk_volume")
    private Integer diskVolume;

    /** 硬盘数量<br/>Column: [disk_number] */
    @Column(value = "disk_number")
    private Integer diskNumber;

    /** 硬盘总容量<br/>Column: [disk_total_vol] */
    @Column(value = "disk_total_vol")
    private Integer diskTotalVol;

    /** 硬盘2规格(GB)<br/>Column: [disk2_volume] */
    @Column(value = "disk2_volume")
    private Integer disk2Volume;

    /** 硬盘2数量<br/>Column: [disk2_number] */
    @Column(value = "disk2_number")
    private Integer disk2Number;

    /** 硬盘2总容量<br/>Column: [disk2_total_vol] */
    @Column(value = "disk2_total_vol")
    private Integer disk2TotalVol;

    /** SSD缩写<br/>Column: [ssd_abbr] */
    @Column(value = "ssd_abbr")
    private String ssdAbbr;

    /** SSD2缩写<br/>Column: [ssd2_abbr] */
    @Column(value = "ssd2_abbr")
    private String ssd2Abbr;

    /** SSD规格<br/>Column: [ssd_type] */
    @Column(value = "ssd_type")
    private Integer ssdType;

    /** SSD数量<br/>Column: [ssd_number] */
    @Column(value = "ssd_number")
    private Integer ssdNumber;

    /** SSD总容量<br/>Column: [ssd_total_vol] */
    @Column(value = "ssd_total_vol")
    private Integer ssdTotalVol;

    /** SSD2规格<br/>Column: [ssd2_type] */
    @Column(value = "ssd2_type")
    private Integer ssd2Type;

    /** SSD2数量<br/>Column: [ssd2_number] */
    @Column(value = "ssd2_number")
    private Integer ssd2Number;

    /** SSD2总容量<br/>Column: [ssd2_total_vol] */
    @Column(value = "ssd2_total_vol")
    private Integer ssd2TotalVol;

    /** GPU缩写<br/>Column: [gpu_abbr] */
    @Column(value = "gpu_abbr")
    private String gpuAbbr;

    /** GPU型号<br/>Column: [gpu_model] */
    @Column(value = "gpu_model")
    private String gpuModel;

    /** GPU数量<br/>Column: [gpu_number] */
    @Column(value = "gpu_number")
    private Integer gpuNumber;

    /** 是否默认版本{1:是,0:否}<br/>Column: [default_flag] */
    @Column(value = "default_flag")
    private Integer defaultFlag;

}
