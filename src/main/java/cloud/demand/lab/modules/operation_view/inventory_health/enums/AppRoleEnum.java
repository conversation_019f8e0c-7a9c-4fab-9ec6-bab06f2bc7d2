package cloud.demand.lab.modules.operation_view.inventory_health.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AppRoleEnum {

    NORMAL_SALES("正常售卖"),
    EKS("EKS"),
    EMR("EMR"),
    LH("LH"),
    OTHER("其他"),
    ;
    private final String name;

    public static List<AppRoleEnum> getWithoutOther() {
        return Arrays.stream(AppRoleEnum.values()).filter(e -> e != OTHER).collect(Collectors.toList());
    }
}
