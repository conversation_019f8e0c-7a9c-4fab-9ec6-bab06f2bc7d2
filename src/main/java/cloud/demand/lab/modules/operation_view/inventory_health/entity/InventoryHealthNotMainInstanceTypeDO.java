package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 库存健康-在售非主力机型(实例类型)配置表
 */
@Data
@ToString
@Table("inventory_health_not_main_instance_type")
public class InventoryHealthNotMainInstanceTypeDO extends BaseDO {

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

}
