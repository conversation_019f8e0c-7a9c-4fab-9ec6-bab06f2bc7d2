package cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryManualConfigDetailReq {
    @NotNull
    private String date;
    @NotNull
    private String field;
    @NotNull
    private Integer year;
    @NotNull
    private Integer week;

    private Integer weekIndex;

    private List<String> instanceType;

    private List<String> zoneName;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 园区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /**
     * 机型族
     */
    private List<String> ginFamily;

    /**
     * 是否获取累计数据
     */
    private Boolean isCumulative;

    /** condition 缓存 */
    private WhereSQL condition = null;

    public WhereSQL genBasicCondition() {
        if (this.condition != null) {
            return this.condition;
        }

        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }

        WhereSQL cateCondition = SpringUtil.getBean(OperationViewService2Impl.class).genCategoryCondition(
                zoneCategory,
                instanceTypeCategory,
                false,
                date.toString(),
                customhouseTitle
        );

        this.condition = condition.and(cateCondition);
        return this.condition;
    }


}
