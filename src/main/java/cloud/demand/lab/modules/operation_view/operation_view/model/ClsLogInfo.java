package cloud.demand.lab.modules.operation_view.operation_view.model;

import lombok.Data;

@Data
public class ClsLogInfo {
    /** 动作 */
    private String action;
    /** 地域，例子：ap-nanjing */
    private String region;
    /** 资源不足错误（CLB） */
    private Integer insufficientResourceFailed;
    /** 内部错误（CLB） */
    private Integer internalFailed;
    /** 内部错误（EIP） */
    private Integer internalError;
    /** 资源不足错误（EIP） */
    private Integer resourceInsufficient;
    /** 总数 */
    private Integer total;
}
