package cloud.demand.lab.modules.operation_view.inventory_health.entity;


import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("inventory_health_main_instance_type_config")
@DotExcelEntity
public class InventoryHealthMainInstanceTypeConfigDO extends BaseDO {
    @Column(value = "region_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE, excelColumnName = "境内外")
    private String regionType;

    @Column(value = "instance_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE, excelColumnName = "实例类型")
    private String instanceType;

    @Column(value = "type1")
    private String type1;

    @Column(value = "type1_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE, excelColumnName = "一级类型")
    private String type1Name;

    @Column(value = "type2")
    private String type2;

    @Column(value = "type2_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE, excelColumnName = "二级类型")
    private String type2Name;

    @Column(value = "date")
    private Date date;

    @Column(value = "product")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE, excelColumnName = "产品")
    private String product;

    public static InventoryHealthMainInstanceTypeConfigDO copy(InventoryHealthMainInstanceTypeConfigDO thisDO) {
        InventoryHealthMainInstanceTypeConfigDO inventoryHealthMainInstanceTypeConfigDO = new InventoryHealthMainInstanceTypeConfigDO();
        inventoryHealthMainInstanceTypeConfigDO.setRegionType(thisDO.getRegionType());
        inventoryHealthMainInstanceTypeConfigDO.setInstanceType(thisDO.getInstanceType());
        inventoryHealthMainInstanceTypeConfigDO.setType1(thisDO.getType1());
        inventoryHealthMainInstanceTypeConfigDO.setType1Name(thisDO.getType1Name());
        inventoryHealthMainInstanceTypeConfigDO.setType2(thisDO.getType2());
        inventoryHealthMainInstanceTypeConfigDO.setType2Name(thisDO.getType2Name());
        inventoryHealthMainInstanceTypeConfigDO.setDate(thisDO.getDate());
        inventoryHealthMainInstanceTypeConfigDO.setProduct(thisDO.getProduct());
        return inventoryHealthMainInstanceTypeConfigDO;
    }
}