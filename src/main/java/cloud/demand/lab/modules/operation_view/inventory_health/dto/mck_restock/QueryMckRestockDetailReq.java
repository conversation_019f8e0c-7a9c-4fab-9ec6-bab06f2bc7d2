package cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock;

import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QueryMckRestockDetailReq extends QueryMckRestockReq {
    @NotNull
    private Integer year;
    @NotNull
    private Integer week;
    @NotNull
    private Integer weekIndex;

    private String weekStartDate;

    private String weekEndDate;

    private Boolean isCumulative;
}
