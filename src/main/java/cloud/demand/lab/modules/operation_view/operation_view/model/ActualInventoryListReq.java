package cloud.demand.lab.modules.operation_view.operation_view.model;

import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ActualInventoryListReq {
    String statTime;
    List<String> lineType;
    List<String> materialType;
    List<String> zoneCategory;
    List<String> instanceTypeCategory;
    List<String> invDetailType;
    List<String> customhouseTitle;
    Boolean isCombine;
    /**
     * 取主力机型、主力可用区时对应的日期，如果为 null，默认为 statTime
     */
    String categoryDate;


    public ActualInventoryListReq getNewReq(ActualInventoryListReq req) {
        ActualInventoryListReq newReq = new ActualInventoryListReq();
        newReq.setLineType(req.getLineType());
        newReq.setMaterialType(req.getMaterialType());
        newReq.setZoneCategory(req.getZoneCategory());
        newReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        newReq.setInvDetailType(req.getInvDetailType());
        newReq.setIsCombine(req.getIsCombine());
        newReq.setCategoryDate(req.getCategoryDate());
        return newReq;
    }
}
