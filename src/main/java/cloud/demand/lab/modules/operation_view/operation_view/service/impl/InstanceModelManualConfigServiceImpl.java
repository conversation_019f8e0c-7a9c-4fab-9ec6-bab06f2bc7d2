package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthInstanceModelManualConfigDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.InstanceModelManualConfigReq;
import cloud.demand.lab.modules.operation_view.operation_view.service.InstanceModelManualConfigService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InstanceModelManualConfigServiceImpl implements InstanceModelManualConfigService {

    @Resource
    private DBHelper demandDBHelper;


    @Override
    public Result setManualValue(InstanceModelManualConfigReq req) {
        WhereSQL cond = req.getCondition();
        InventoryHealthInstanceModelManualConfigDO existConfigDo = demandDBHelper.getOne(InventoryHealthInstanceModelManualConfigDO.class, cond.getSQL(), cond.getParams());

        if (existConfigDo == null) {
            InventoryHealthInstanceModelManualConfigDO configDO = InventoryHealthInstanceModelManualConfigDO.fromReq(req);
            demandDBHelper.insert(configDO);
        } else {
            existConfigDo.setValue(req.getValue());
            demandDBHelper.update(existConfigDo);
        }

        return Result.success("success");
    }

    @Override
    public void snapshotManualConfig(String date) {
        List<InventoryHealthInstanceModelManualConfigDO> current = demandDBHelper.getAll(InventoryHealthInstanceModelManualConfigDO.class,
                "where date = ?", date);

        // 确保幂等性
        if (current.size() > 0){
            demandDBHelper.delete(InventoryHealthInstanceModelManualConfigDO.class, "where date = ?", date);
        }

        Date yesterday = DateUtils.addTime(DateUtils.parse(date), Calendar.DATE, -1);
        List<InventoryHealthInstanceModelManualConfigDO> all = demandDBHelper.getAll(InventoryHealthInstanceModelManualConfigDO.class,
                "where date = ?", DateUtils.formatDate(yesterday));
        // 把前一天的继承下来
        if (all.size() > 0) {
            List<InventoryHealthInstanceModelManualConfigDO> result = Lang.list();
            ListUtils.forEach(all, o -> {
                InventoryHealthInstanceModelManualConfigDO one = InventoryHealthInstanceModelManualConfigDO.transform(o);
                one.setDate(DateUtils.parseLocalDate(date));
                result.add(one);
            });
            demandDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    @Override
    public Map<String, BigDecimal> querySnapshotManual(String statTime) {
        List<InventoryHealthInstanceModelManualConfigDO> manualList =
                demandDBHelper.getAll(InventoryHealthInstanceModelManualConfigDO.class, "where date = ?", statTime);
        return ListUtils.toMap(manualList, o -> o.toKey(),
                InventoryHealthInstanceModelManualConfigDO::getValue);
    }
}
