package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("inventory_health_overview_zone_config")
public class InventoryHealthOverviewZoneConfigDO {

    /** 编号<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 排序序号<br/>Column: [sort_num] */
    @Column(value = "sort_num")
    private Long sortNum;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域别称<br/>Column: [other_name] */
    @Column(value = "other_name")
    private String otherName;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;
}
