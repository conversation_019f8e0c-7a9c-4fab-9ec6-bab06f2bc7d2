package cloud.demand.lab.modules.operation_view.entity.rrp;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * CMongo物理机机型内存映射信息
 */

@Data
@ToString
@Table("report_config_cmongo_mem")
public class ReportConfigCmongoMemDO extends BaseDO {

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 物理容量GB<br/>Column: [physical_mem] */
    @Column(value = "physical_mem")
    private BigDecimal physicalMem;

    /** 最大可用容量GB<br/>Column: [max_can_use_mem] */
    @Column(value = "max_can_use_mem")
    private BigDecimal maxCanUseMem;

}
