package cloud.demand.lab.modules.operation_view.operation_view.service.impl;

import cloud.demand.lab.modules.operation_view.entity.web.common.StreamDownloadBean;
import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.utils.CkDBUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.ObjUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.BasHealthInvWaveCustomerUinDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.CustomerPplVersionItemDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.CustomerSafetyInventoryHistoryPeakDTO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsDemandWeekNCustomerPplVersionItemDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsDemandWeekNCustomerWaveDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.BasHealthInvWaveCustomerUinExcel;
import cloud.demand.lab.modules.operation_view.operation_view.model.DwsDemandWeekNCustomerWaveExcel;
import cloud.demand.lab.modules.operation_view.operation_view.service.InventoryHealthCustomerGenService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.operation_view.util.SopDateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

@Slf4j
@Service
public class InventoryHealthCustomerGenServiceImpl implements InventoryHealthCustomerGenService {

    /**
     * 库存健康字典
     */
    @Resource
    private InventoryHealthDictService inventoryHealthDictService;

    /**
     * ck库-std_crp
     */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /**
     * ck库存-cloud_demand
     */
    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    /** 导入大客户uin */
    @Transactional(transactionManager = "demandTransactionManager")
    public List<ErrorMessage> uploadHeadUin(MultipartFile file){
        if (file == null){
            throw new ITException("导入大客户uin文件不能为空");
        }
        List<BasHealthInvWaveCustomerUinDO> saveData = parseFile(file);
        if (ListUtils.isEmpty(saveData)){
            throw new ITException("大客户名单客户集合不能为空");
        }
        List<ErrorMessage> ret = new ArrayList<>();
        for (int i = 0; i < saveData.size(); i++) {
            BasHealthInvWaveCustomerUinDO saveDatum = saveData.get(i);
            if (!SoeCommonUtils.isNotBlank(saveDatum.getUin())){
                ret.add(new ErrorMessage(i+1,1,"客户uin","客户uin不能为空值"));
            }
            if (!SoeCommonUtils.isNotBlank(saveDatum.getCustomerShortName())){
                ret.add(new ErrorMessage(i+1,2,"客户简称","客户简称不能为空值"));
            }
        }
        if (ListUtils.isNotEmpty(ret)){
            return ret;
        }

        // 设置版本
        long version = SoeCommonUtils.getNextVersion(demandDBHelper, BasHealthInvWaveCustomerUinDO.class);

        for (BasHealthInvWaveCustomerUinDO saveDatum : saveData) {
            saveDatum.setVersion(version);
        }

        // 将所有的是否为默认版本都设为否
        demandDBHelper.updateAll(BasHealthInvWaveCustomerUinDO.class,"default_flag = '0'","where default_flag = '1'");

        // 插入数据
        demandDBHelper.insert(saveData);
        return ret;
    }

    private List<BasHealthInvWaveCustomerUinDO> parseFile(MultipartFile file){
        List<BasHealthInvWaveCustomerUinDO> ret = new ArrayList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), BasHealthInvWaveCustomerUinExcel.class,
                    new AnalysisEventListener<BasHealthInvWaveCustomerUinExcel>() {
                        @Override
                        public void invoke(BasHealthInvWaveCustomerUinExcel o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            ret.add(BasHealthInvWaveCustomerUinExcel.transform(o));
                        }
                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(1).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return ret;
    }

    @TaskLog(taskName = "InventoryHealthCustomerGenService.genWeekNForecastData")
    @Override
    public void genWeekNForecastData(LocalDate statTime) {
        if (statTime == null) {
            statTime = LocalDate.now();
        }
        // step1：参数准备（过去 13 周）
        log.info(String.format("安全库存客户预测&周峰底表【%s】：start", statTime));
        // 固定 5 周，相当于5周前对当前周的预测
        int week = 5;
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(statTime, -13);

        List<DwsDemandWeekNCustomerPplVersionItemDO> result = new ArrayList<>();

        // step2：循环 n-13 周，取 n-13-w 预测
        log.info(String.format("安全库存客户预测&周峰底表【%s】：循环 过期13 周 获取 w-5 预测数据", statTime));
        for (HolidayWeekInfoDTO holidayWeekInfoDTO : holidayWeekInfoDTOS) {
            // 过去 w 周
            List<HolidayWeekInfoDTO> lastNWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(LocalDate.parse(holidayWeekInfoDTO.getStartDate()), -week);
            // 找到最小的开始时间（w-5周）
            HolidayWeekInfoDTO weekInfoDTO = lastNWeekInfo.get(0);

            WhereSQL condition = new WhereSQL();

            // 剔除弹性需求
            condition.and("demand_type <> 'ELASTIC'");

            // 版本日期在 w-5 周
            condition.and("version_code_date between ? and ?", weekInfoDTO.getStartDate(), weekInfoDTO.getEndDate());
            // 只看CVM
            condition.and("version_group_product = 'CVM&CBS'");
            // 云运管干预后的所有 PPL
            condition.and("source in ('IMPORT', 'COMD_INTERVENE', 'FORECAST', 'APPLY_AUTO_FILL', 'SYNC_YUNXIAO')");
            condition.and("is_comd = 0");

            // 购买日期区间需要和当周的时间有交集（即 i-w周 对 i周 的预测，i 为 n-13 遍历下标）
            WhereSQL buyDateCondition = new WhereSQL();
            buyDateCondition.or("begin_buy_date between ? and ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
            buyDateCondition.or("end_buy_date between ? and ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
            buyDateCondition.or("begin_buy_date < ? and end_buy_date > ?", holidayWeekInfoDTO.getStartDate(), holidayWeekInfoDTO.getEndDate());
            condition.and(buyDateCondition);

            condition.addGroupBy("version_code_date", "begin_buy_date", "end_buy_date", "customhouse_title" ,"industry_dept", "customer_short_name", "customer_uin", "source", "year", "month");

            List<CustomerPplVersionItemDO> pplVersionItemDOS = ckcldStdCrpDBHelper.getAll(CustomerPplVersionItemDO.class, condition.getSQL(), condition.getParams());

            // 3. 处理跨周的场景
            //  3.1 对于大客户提报需求，将预测平均到所跨的周。从预测的开始结束购买时间得到所跨的周数
            //  3.2 对于中长尾需求，中长尾是按月预测，所以这里要平均到 4 或者 5 周，基于当月有多少个节假周
            result.addAll(handlePplData(statTime, holidayWeekInfoDTO, pplVersionItemDOS));
        }
        // step4：生成周峰数据
        List<CustomerSafetyInventoryHistoryPeakDTO> peakData = getHistoryWeekPeakData(statTime);
        // step5：合并
        log.info(String.format("安全库存客户预测&周峰底表【%s】：ppl需求集合size：【%s】,规模周峰集合size：【%s】", statTime,result.size(),peakData.size()));
        result = mergeData(result, peakData, week, statTime, holidayWeekInfoDTOS);
        // step6：清空ck当前分区
        log.info(String.format("安全库存客户预测&周峰底表【%s】：clean", statTime));
        CkDBUtils.doDelete(ckcldDBHelper, DateUtils.format(statTime), DwsDemandWeekNCustomerPplVersionItemDO.class, "cloud_demand");
        // step7：写入ck
        log.info(String.format("安全库存客户预测&周峰底表【%s】：insert size：【%s】", statTime, result.size()));
        log.info(String.format("安全库存客户预测&周峰底表【%s】：first row：【%s】", statTime, ListUtils.isEmpty(result) ? "空集合" : result.get(0)));
        ckcldDBHelper.insertBatchWithoutReturnId(result);
    }

    @TaskLog(taskName = "InventoryHealthCustomerGenService.genWeekNCustomerWaveData")
    @Override
    public void genWeekNCustomerWaveData(LocalDate statTime) {
        if (statTime == null) {
            statTime = LocalDate.now();
        }

        // step1：获取ppl需求&周峰切片数据
        log.info(String.format("安全库存客户波动底表【%s】: start", statTime));
        List<DwsDemandWeekNCustomerPplVersionItemDO> demandPeakData = ckcldDBHelper.getAll(DwsDemandWeekNCustomerPplVersionItemDO.class, "where stat_time = ?", statTime);
        if (ListUtils.isEmpty(demandPeakData)) {
            throw new ITException(String.format("ppl需求&周峰切片数据为空，无法生成安全库存客户波动底表，切片时间：【%s】", statTime));
        }
        // step2：计算客户波动
        log.info(String.format("安全库存客户波动底表【%s】: 计算客户波动", statTime));
        List<DwsDemandWeekNCustomerWaveDO> saveData = computeWaveData(demandPeakData);
        // step3：清空ck当前分区
        log.info(String.format("安全库存客户波动底表【%s】: 清空ck当前分区", statTime));
        CkDBUtils.doDelete(ckcldDBHelper, DateUtils.format(statTime), DwsDemandWeekNCustomerWaveDO.class, "cloud_demand");
        // step4：写入ck
        log.info(String.format("安全库存客户波动底表【%s】：insert size：【%s】", statTime, saveData.size()));
        log.info(String.format("安全库存客户波动底表【%s】：first row：【%s】", statTime, ListUtils.isEmpty(saveData) ? "空集合" : saveData.get(0)));
        ckcldDBHelper.insertBatchWithoutReturnId(saveData);
    }

    /** 计算波动数据 */
    private List<DwsDemandWeekNCustomerWaveDO> computeWaveData(List<DwsDemandWeekNCustomerPplVersionItemDO> demandPeakData) {
        // step1：获取大客户名单，不在大客户名单策略表里面的均改成中长尾
        List<BasHealthInvWaveCustomerUinDO> uinDOList = demandDBHelper.getAll(BasHealthInvWaveCustomerUinDO.class, "where default_flag = '1'");
        if (ListUtils.isEmpty(uinDOList)){
            throw new ITException("大名单客户为空，请联系 smithyyi 配置");
        }

        log.info(String.format("安全库存客户波动底表，大客户名单集合：版本：【%s】，size：【%s】", uinDOList.get(0).getVersion(),uinDOList.size()));

        Map<String,BasHealthInvWaveCustomerUinDO> uinMap = ListUtils.toMap(uinDOList,BasHealthInvWaveCustomerUinDO::getUin,item->item);

        // step2：清洗客户，不在大名单客户里面的清洗为中长尾
        int hitCount = 0;
        for (DwsDemandWeekNCustomerPplVersionItemDO peakDatum : demandPeakData) {
            String uin = peakDatum.getUin();
            BasHealthInvWaveCustomerUinDO uinDO = uinMap.get(uin);
            if (uinDO == null){
                peakDatum.setUin("0");
                peakDatum.setCustomerShortName("中长尾");
                peakDatum.setIndustryDept("中长尾");
            }else {
                peakDatum.setCustomerShortName(uinDO.getCustomerShortName());
                hitCount ++;
            }
        }

        log.info(String.format("安全库存客户波动底表，清洗客户（不在大客户名单配置表【bas_health_inv_wave_customer_uin】的清洗为中长尾），命中数：【%s】，集合总数：【%s】", hitCount, demandPeakData.size()));

        List<DwsDemandWeekNCustomerWaveDO> ret = new ArrayList<>();
        Map<String, List<DwsDemandWeekNCustomerPplVersionItemDO>> customerWaveListMap = ListUtils.groupBy(demandPeakData, DwsDemandWeekNCustomerPplVersionItemDO::getWaveKey);

        customerWaveListMap.forEach((k, v) -> {
            DwsDemandWeekNCustomerWaveDO retItem = new DwsDemandWeekNCustomerWaveDO();
            DwsDemandWeekNCustomerPplVersionItemDO first = v.get(0);
            // 在group by内
            retItem.setCustomhouseTitle(first.getCustomhouseTitle());
            retItem.setUin(first.getUin());
            retItem.setCustomerShortName(first.getCustomerShortName());
            // 固定不变
            retItem.setProduct(first.getProduct());
            retItem.setWeekN(first.getWeekN());
            retItem.setStatTime(first.getStatTime());

            Map<String, BigDecimal> averageTotalMap = new HashMap<>(); // key：13周，value：均摊后核心数
            Map<String, BigDecimal> logicNumMap = new HashMap<>(); // key：13周，value：周峰外部计费内部服务
            int startHolidayYear = Integer.MAX_VALUE;
            int startHolidayMonth = Integer.MAX_VALUE;
            int startHolidayWeek = Integer.MAX_VALUE;
            int endHolidayYear = 0;
            int endHolidayMonth = 0;
            int endHolidayWeek = 0;
            String startYearWeek = null;
            String endYearWeek = null;
            for (DwsDemandWeekNCustomerPplVersionItemDO item : v) {
                // 最大最小年周
                Integer holidayYear = item.getHolidayYear();
                Integer holidayMonth = item.getHolidayMonth();
                Integer holidayWeek = item.getHolidayWeek();

                String yearWeek = SoeCommonUtils.getYearWeek(holidayYear,holidayWeek);
                if (startYearWeek == null || StringUtils.compare(startYearWeek,yearWeek) > 0){
                    startYearWeek = yearWeek;
                    startHolidayYear = holidayYear;
                    startHolidayMonth = holidayMonth;
                    startHolidayWeek = holidayWeek;
                }
                if (endYearWeek == null || StringUtils.compare(endYearWeek, yearWeek) < 0){
                    endYearWeek = yearWeek;
                    endHolidayYear = holidayYear;
                    endHolidayMonth = holidayMonth;
                    endHolidayWeek = holidayWeek;
                }

                // 统计13周 ppl需求和逻辑周峰（周峰外部计费内部服务）
                String key = StringUtils.joinWith("@", holidayYear, SopDateUtils.fixNumber(holidayWeek));
                BigDecimal oldAverageTotal = averageTotalMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal oldLogicNum = logicNumMap.getOrDefault(key, BigDecimal.ZERO);
                averageTotalMap.put(key, oldAverageTotal.add(item.getAverageTotalCore()));
                logicNumMap.put(key, oldLogicNum.add(item.getLogicNum()));
            }
            BigDecimal averageTotalAvg13Core = avg(averageTotalMap.values()); // 均摊后核心数-13周均值
            BigDecimal logicAvg13Num = avg(logicNumMap.values()); // 周峰外部计费内部服务-13周均值
            BigDecimal customerWaveLogicCore = sumDiff(averageTotalMap, logicNumMap); // 客户波动（均摊后核心数-周峰外部计费内部服务）-13周总和
            BigDecimal customerWaveLogicSqrtCore = absSumDiffSqrt(averageTotalMap, logicNumMap); // 客户波动（均摊后核心数-周峰外部计费内部服务）-13周平方总和后开方

            // 起始结束年月周
            retItem.setStartHolidayYear(startHolidayYear);
            retItem.setStartHolidayMonth(startHolidayMonth);
            retItem.setStartHolidayWeek(startHolidayWeek);
            retItem.setEndHolidayYear(endHolidayYear);
            retItem.setEndHolidayMonth(endHolidayMonth);
            retItem.setEndHolidayWeek(endHolidayWeek);
            // 波动
            retItem.setAverageTotalAvg13Core(averageTotalAvg13Core);
            retItem.setLogicAvg13Num(logicAvg13Num);
            retItem.setCustomerWaveLogicCore(customerWaveLogicCore);
            retItem.setCustomerWaveLogicSqrtCore(customerWaveLogicSqrtCore);
            ret.add(retItem);
        });

        return ret;
    }

    /** (集合A元素 - 集合B元素)平方总和的开方 */
    private BigDecimal absSumDiffSqrt(Map<String, BigDecimal> averageTotalMap, Map<String, BigDecimal> logicNumMap) {
        BigDecimal total = BigDecimal.ZERO;
        Map<String,BigDecimal> tempLogicNumMap = new HashMap<>(logicNumMap);
        if (ListUtils.isNotEmpty(averageTotalMap)){
            for (Map.Entry<String, BigDecimal> entry : averageTotalMap.entrySet()) {
                String k = entry.getKey();
                BigDecimal v = entry.getValue();
                BigDecimal remove = tempLogicNumMap.remove(k);
                if (remove!=null){
                    v = v.subtract(remove);
                }
                // 平方后累加到total
                total = total.add(BigDecimal.valueOf(Math.pow(v.doubleValue(),2)));
            }
        }
        if (ListUtils.isNotEmpty(tempLogicNumMap)){
            for (BigDecimal v : tempLogicNumMap.values()) {
                total = total.add(BigDecimal.valueOf(Math.pow(v.doubleValue(),2)));
            }
        }
        if (total.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        // 结果开方
        return BigDecimal.valueOf(Math.sqrt(total.doubleValue()));
    }

    /** 集合A元素 - 集合B元素 的总和 */
    private BigDecimal sumDiff(Map<String, BigDecimal> averageTotalMap, Map<String, BigDecimal> logicNumMap) {
        BigDecimal ret = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(averageTotalMap)) {
            for (BigDecimal value : averageTotalMap.values()) {
                if (value != null) {
                    ret = ret.add(value);
                }
            }
        }
        if (ListUtils.isNotEmpty(logicNumMap)) {
            for (BigDecimal value : logicNumMap.values()) {
                if (value != null) {
                    ret = ret.subtract(value);
                }
            }
        }
        return ret;
    }


    /** 求集合内的均值 */
    public BigDecimal avg(Collection<BigDecimal> values) {
        if (ListUtils.isEmpty(values)) {
            return BigDecimal.ZERO;
        }
        BigDecimal bigDecimal = values.stream().map(item -> ObjectUtils.defaultIfNull(item, BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        return bigDecimal.divide(BigDecimal.valueOf(values.size()), 6, RoundingMode.HALF_UP);
    }


    /**
     * 合并ppl需求和周峰
     *
     * @param result              ppl需求
     * @param peakData            周峰
     * @param week                多少周前对当前周的预测
     * @param statTime            切片时间
     * @param holidayWeekInfoDTOS 节假日
     * @return 合并后的结果集
     */
    private List<DwsDemandWeekNCustomerPplVersionItemDO> mergeData(List<DwsDemandWeekNCustomerPplVersionItemDO> result,
            List<CustomerSafetyInventoryHistoryPeakDTO> peakData,
            int week,
            LocalDate statTime,
            List<HolidayWeekInfoDTO> holidayWeekInfoDTOS) {
        Map<String, HolidayWeekInfoDTO> yearWeek = ListUtils.toMap(holidayWeekInfoDTOS,
                item -> StringUtils.join(item.getYear(), SopDateUtils.fixNumber(item.getWeek())),
                item -> item);

        // step1：聚合ppl预测
        Map<String, List<DwsDemandWeekNCustomerPplVersionItemDO>> pplCustomerYMListMap = ListUtils.groupBy(result, DwsDemandWeekNCustomerPplVersionItemDO::getKey);

        Map<String, DwsDemandWeekNCustomerPplVersionItemDO> pplCustomerYMMap = new HashMap<>();
        pplCustomerYMListMap.forEach((k, ls) -> {
            DwsDemandWeekNCustomerPplVersionItemDO value = ls.get(0).copy();
            BigDecimal totalCore = BigDecimal.ZERO;
            BigDecimal averageTotalCore = BigDecimal.ZERO;
            for (DwsDemandWeekNCustomerPplVersionItemDO l : ls) {
                totalCore = totalCore.add(l.getTotalCore());
                averageTotalCore = averageTotalCore.add(l.getAverageTotalCore());
            }
            value.setTotalCore(totalCore);
            value.setAverageTotalCore(averageTotalCore);
            pplCustomerYMMap.put(k, value);
        });

        // step2：聚合规模周峰
        Map<String, List<CustomerSafetyInventoryHistoryPeakDTO>> peakCustomerYMListMap = ListUtils.groupBy(peakData, CustomerSafetyInventoryHistoryPeakDTO::getKey);

        Map<String, CustomerSafetyInventoryHistoryPeakDTO> peakCustomerYMMap = new HashMap<>();
        peakCustomerYMListMap.forEach((k, ls) -> {
            CustomerSafetyInventoryHistoryPeakDTO value = ls.get(0).copy();
            BigDecimal logicNum = BigDecimal.ZERO;
            BigDecimal billNum = BigDecimal.ZERO;
            BigDecimal serverNum = BigDecimal.ZERO;
            for (CustomerSafetyInventoryHistoryPeakDTO l : ls) {
                logicNum = logicNum.add(ObjectUtils.defaultIfNull(l.getLogicNum(), BigDecimal.ZERO));
                billNum = billNum.add(ObjectUtils.defaultIfNull(l.getBillNum(), BigDecimal.ZERO));
                serverNum = serverNum.add(ObjectUtils.defaultIfNull(l.getServiceNum(), BigDecimal.ZERO));
            }
            peakCustomerYMMap.put(k, value);
        });

        List<DwsDemandWeekNCustomerPplVersionItemDO> ret = new ArrayList<>();

        // step3：merge ppl需求+售卖周峰
        pplCustomerYMMap.forEach((k, v) -> {
            CustomerSafetyInventoryHistoryPeakDTO remove = peakCustomerYMMap.remove(k);
            BigDecimal logicNum = BigDecimal.ZERO;
            BigDecimal billNum = BigDecimal.ZERO;
            BigDecimal serverNum = BigDecimal.ZERO;
            if (remove != null) {
                logicNum = remove.getLogicNum();
                billNum = remove.getBillNum();
                serverNum = remove.getServiceNum();
            }
            v.setLogicNum(logicNum);
            v.setBillNum(billNum);
            v.setServiceNum(serverNum);
            ret.add(v);
        });

        if (ListUtils.isNotEmpty(peakCustomerYMMap)) {
            peakCustomerYMMap.forEach((k, v) -> {
                DwsDemandWeekNCustomerPplVersionItemDO retItem = DwsDemandWeekNCustomerPplVersionItemDO.transform(v);
                ret.add(retItem);
            });
        }
        // 清洗返回结果
        ret.forEach(item -> {
            item.setStatTime(statTime);
            HolidayWeekInfoDTO holidayWeekInfoDTO = yearWeek.get(StringUtils.join(item.getHolidayYear(), SopDateUtils.fixNumber(item.getHolidayWeek())));
            if (holidayWeekInfoDTO == null) {
                throw new ITException("节假日获取失败");
            }
            item.setHolidayMonth(holidayWeekInfoDTO.getMonth());
            item.setWeekN(week);
            item.setWeekIndex(holidayWeekInfoDTO.getWeekNFromNow());
            item.setWeekStartDate(holidayWeekInfoDTO.getStartDate());
            item.setWeekEndDate(holidayWeekInfoDTO.getEndDate());
            // 暂时只支持CVM
            item.setProduct(ProductTypeEnum.CVM.getCode());
        });
        return ret;
    }

    private List<DwsDemandWeekNCustomerPplVersionItemDO> handlePplData(LocalDate statTime,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            List<CustomerPplVersionItemDO> pplVersionItemDOS) {
        List<DwsDemandWeekNCustomerPplVersionItemDO> result = ListUtils.newList();
        if (ListUtils.isEmpty(pplVersionItemDOS)) {
            return result;
        }

        // step1：参数准备（全部周信息）
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();

        // step2：遍历ppl数据，头部按购买区间内周数进行分摊，中长尾是按月内周数分摊
        for (CustomerPplVersionItemDO itemDO : pplVersionItemDOS) {
            // 均摊到每一周
            DwsDemandWeekNCustomerPplVersionItemDO resultItem = DwsDemandWeekNCustomerPplVersionItemDO.from(itemDO, holidayWeekInfoDTO);
            result.add(resultItem);
            resultItem.setStatTime(statTime);

            // 拿到开始购买时间和结束购买时间
            LocalDate beginBuyDate = LocalDate.parse(itemDO.getBeginBuyDate());
            LocalDate endBuyDate = LocalDate.parse(itemDO.getEndBuyDate());

            // 按周均摊
            if (itemDO.getSource().equals("FORECAST")) {
                // PPL 预测年月的周数量
                long weekNum = all.stream().filter(item -> item.getYear().equals(itemDO.getYear()) && item.getMonth().equals(itemDO.getMonth())).count();
                // 按照节假周的数量均摊到周
                resultItem.setAverageTotalCore(resultItem.getTotalCore().divide(BigDecimal.valueOf(weekNum), 6, RoundingMode.HALF_UP));
            } else {
                // 找到所有 beginBuyDate 和 endBuyDate 横跨的周信息
                List<ResPlanHolidayWeekDO> dtos = Lang.list();

                for (ResPlanHolidayWeekDO wdo : all) {
                    LocalDate wdoEndDate = LocalDate.parse(wdo.getEnd());
                    LocalDate wdoStartDate = LocalDate.parse(wdo.getStart());

                    if (
                            (!beginBuyDate.isBefore(wdoStartDate) && !beginBuyDate.isAfter(wdoEndDate)) ||
                                    (!endBuyDate.isBefore(wdoStartDate) && !endBuyDate.isAfter(wdoEndDate)) ||
                                    (beginBuyDate.isBefore(wdoStartDate) && endBuyDate.isAfter(wdoEndDate))
                    ) {
                        dtos.add(wdo);
                    }
                }

                if (dtos.size() > 0) {
                    // 直接按照所跨的周均摊。这里只拿了当周的，下一周的均摊会在下一个版本处理，如果下一个版本没有继承这条预测，那么下周将不会有这一条的均摊
                    resultItem.setAverageTotalCore(resultItem.getTotalCore().divide(BigDecimal.valueOf(dtos.size()), 6, RoundingMode.HALF_UP));
                }
            }
        }
        return result;
    }

    @Override
    public List<CustomerSafetyInventoryHistoryPeakDTO> getHistoryWeekPeakData(LocalDate statTime) {
        // step1：获取过去 13 周
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(statTime, -13);
        HolidayWeekInfoDTO startWeek = holidayWeekInfoDTOS.get(0);
        HolidayWeekInfoDTO endWeek = holidayWeekInfoDTOS.get(holidayWeekInfoDTOS.size() - 1);

        // 起始时间，n-13周的首天
        String startDay = startWeek.getStartDate();
        // 结束时间，n周的结束时间
        String endDay = endWeek.getEndDate();

        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/history_week_peak_data_with_customer.sql");

        Map<String, Object> params = new HashMap<>();
        params.put("start", startDay);
        params.put("end", endDay);

        return ckcldStdCrpDBHelper.getRaw(CustomerSafetyInventoryHistoryPeakDTO.class, sql, params);
    }

    @Override
    public ResponseEntity<InputStreamResource> exportCustomerWave(LocalDate statTime) {
        List<DwsDemandWeekNCustomerWaveDO> all = ckcldDBHelper.getAll(DwsDemandWeekNCustomerWaveDO.class, "where stat_time = ?", statTime);

        List<DwsDemandWeekNCustomerWaveExcel> excelData = ListUtils.transform(all, DwsDemandWeekNCustomerWaveExcel::transform);

        ByteArrayInputStream in;

        try(ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out, DwsDemandWeekNCustomerWaveExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("数据")
                    .doWrite(excelData);
            in = new ByteArrayInputStream(out.toByteArray());
        }catch (Exception e){
            log.error(e.getMessage());
            throw new ITException("导出安全库存客户波动失败");
        }

        String filename = "安全库存客户波动-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

}