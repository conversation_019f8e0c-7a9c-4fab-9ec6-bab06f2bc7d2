package cloud.demand.lab.modules.operation_view.inventory_health.dto.trend_graph;

import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 需求交付集市详情导出
 */
@DotExcelEntity
@Table("demandMarket")
@Data
public class PurchaseDetailDTO {
    @Column(value = "quota_id")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "单号")
    private String quotaId;

    @Column(value = "quota_device_class")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "设备类型")
    private String quotaDeviceClass;

    /**
     * 实例类型，根据设备类型从映射表中关联出来
     */
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "实例类型")
    private String instanceType;

    /**
     * 地域信息，根据 campus 映射关联出来
     */
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "境内外")
    private String regionType;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "区域")
    private String areaName;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "地域")
    private String regionName;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "可用区")
    private String zoneName;

    @Column(value = "campus")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "Campus")
    private String campus;

    /**
     * 一行数据就是一台的交付，直接 count 即可
     */
    @Column(value = "num", computed = "count(1)")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "台数")
    private Integer num;

    @Column(value = "core", computed = "sum(cpu_logic_core)")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "逻辑核心数")
    private Integer core;

    @Column(value = "xy_industry")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "行业")
    private String industry;

    @Column(value = "xy_customer_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "客户名称")
    private String customerName;

    @Column(value = "quota_use_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "期望交付时间")
    private String quotaUseTime;

    @Column(value = "erp_actual_date")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "实际交付时间")
    private String erpActualDate;

    @Column(value = "cloud_delivery_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS, excelColumnName = "产品提货时间")
    private String cloudDeliveryTime;
}

