package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataPermissionReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataQueryInstanceReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.EKSDataHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.BigDataTypeEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.BigDataHealthActualService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.time.LocalDate;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/bigData-inventory-health")
public class BigDataActualController {

    @Resource
    BigDataHealthActualService bigDataHealthActualService;

    @RequestMapping
    public Object queryBDHealthActualReport(@JsonrpcParam BigDataHealthActualReq req){
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }

        if (StringTools.isBlank(req.getStatTime())) {
            throw new WrongWebParameterException("日期存在空值");
        }

        return bigDataHealthActualService.queryBigDataHealthActualReport(req);
    }


    @RequestMapping
    public Object getAllBigDataType() {
        return BigDataTypeEnum.getAllBigDataType();
    }

    @RequestMapping
    public Object getBigDataInstance(@JsonrpcParam BigDataQueryInstanceReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        if (StringTools.isBlank(req.getProductType())) {
            throw new WrongWebParameterException("产品为空");
        }
        String productType = BigDataTypeEnum.getNameByCode(req.getProductType());
        return bigDataHealthActualService.queryBigDataInstance(productType);

    }

    @RequestMapping
    public Object getBigDataProductPermission(@JsonrpcParam BigDataPermissionReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        if (StringTools.isBlank(req.getUser())) {
            throw new WrongWebParameterException("user为空");
        }
        return bigDataHealthActualService.getBigDataProductPermission(req.getUser());
     }


     @RequestMapping
    public Object queryEKSDataHealthTrendReport(@JsonrpcParam EKSDataHealthActualReq req) {
         if (req == null) {
             throw new WrongWebParameterException("请求参数为null");
         }
         return bigDataHealthActualService.queryEKSDataHealthTrendReport(req);
     }


    @RequestMapping
    public Object queryBigDataHealthTrendReport(@JsonrpcParam BigDataHealthTrendReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return bigDataHealthActualService.queryBigDataHealthTrendReport(req);
    }
}
