package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble;

import javax.print.DocFlavor.STRING;
import lombok.Data;

@Data
public class NotPreDeductInventoryData {

    private String statTime;

    private String zoneName;

    private String instanceType;

    private String regionName;

    private String areaName;

    private String customhouseTitle;

    private String zoneCategory;

    private String instanceCategory;

    private String industryDept;

    private String warZone;

    private String appId;

    private String orderNumber;

    private String orderNodeCodeName;

    private String customerShortName;

    private String uin;

    private String beginBuyDate;

    private Integer notPreTurnover;

    private Integer notPreActual;

    private Integer totalCores;

    private Integer preActual;

    private Integer awaitForInventory;


}
