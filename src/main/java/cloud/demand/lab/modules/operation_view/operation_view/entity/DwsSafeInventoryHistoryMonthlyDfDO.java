package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 包月安全库存，包含包月安全库存计算所需要的中间数据以及计算结果
 */
@Table("dws_safe_inventory_history_monthly_df")
@Data
public class DwsSafeInventoryHistoryMonthlyDfDO {
    /**
     * 统计时间
     */
    @Column("stat_time")
    private String statTime;

    /** 产品类型 */
    @Column("product_type")
    private String productType;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 区域名 */
    @Column("area_name")
    private String areaName;

    /** 地域名 */
    @Column("region_name")
    private String regionName;

    /** 可用区名 */
    @Column("zone_name")
    private String zoneName;

    /**实例类型*/
    @Column("instance_type")
    private String instanceType;

    /** 客户类型：名单客户、宝贝客户、中长尾客户等 */
    @Column("customer_custom_group")
    private String customerCustomGroup;

    /**
     * 安全库存算法
     */
    @Column("algorithm")
    private String algorithm;

    /**包月安全库存*/
    @Column("monthly_safety_inv")
    private BigDecimal monthlySafetyInv;

    /**包月安全库存：不考虑供应波动影响 */
    @Column("no_delivery_monthly_safety_inv")
    private BigDecimal noDeliveryMonthlySafetyInv;

    /**
     * 需求标准差
     */
    @Column("standard_diff")
    private BigDecimal standardDiff;

    /**
     * 需求平均值
     */
    @Column("demand_avg")
    private BigDecimal demandAvg;

    /**
     * 13周交付周期标准差，算法考虑供应时才设置
     */
    @Column("delivery_standard_diff")
    private BigDecimal deliveryStandardDiff;

    /**
     * 13周交付周期平均值，算法考虑供应时才设置
     */
    @Column("delivery_avg")
    private BigDecimal deliveryAvg;

    /**
     * 交付SLA
     */
    @Column("sla")
    private BigDecimal sla;

    /**
     * 服务水平
     */
    @Column("service_level")
    private BigDecimal serviceLevel;

    /**
     * 服务水平系数
     */
    @Column("service_level_factor")
    private BigDecimal serviceLevelFactor;

}
