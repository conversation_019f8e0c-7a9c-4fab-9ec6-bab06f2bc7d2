package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.utils.AlarmRobotUtil;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.AreaGradientLineRenderer;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryHealthOverviewRateConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryHealthOverviewZoneConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewWithDateData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewZoneData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryWeekMailData;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ClsLogServiceLevelDiDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCbsServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCdbServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCrsServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsEndToEndZoneDeviceModelMonthDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsLeisureAndBusySoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsTdsqlServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthOverviewService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.temporal.IsoFields;
import java.util.Base64;
import java.util.Date;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.labels.ItemLabelAnchor;
import org.jfree.chart.labels.ItemLabelPosition;
import org.jfree.chart.labels.StandardCategoryItemLabelGenerator;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.renderer.category.LineAndShapeRenderer;
import org.jfree.chart.ui.TextAnchor;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.ITException;


@Service
public class InventoryHealthOverviewServiceImpl implements InventoryHealthOverviewService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper planReportDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DictService dictService;

    @Resource
    private CvmPlanService cvmPlanService;

    @Resource
    private OutsideViewOldService outsideViewOldService;

    @Resource
    DBHelper ckstdcrpDBHelper;

    private final ExecutorService threadPool = Executors.newFixedThreadPool(4);

    private static DynamicProperty<String> mailUsers = DynamicProperty.create("service.level.mail.user", "");

    private String toPercentageString(BigDecimal value) {
        if (value == null) {
            return "0.0%";
        }
        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        percentFormat.setMinimumFractionDigits(1); // 最少1位小数
        percentFormat.setMaximumFractionDigits(1); // 最多1位小数
        return percentFormat.format(value);
    }


    @Override
    public List<InventoryOverviewData> queryInventoryOverviewReport(InventoryOverviewReq req) {
        if(StringUtils.isBlank(req.getDimension())) {
            req.setDimension("区域别称");
        }
        List<InventoryOverviewData> result = new ArrayList<>();
        String startDate = null;
        String endDate = null;
        switch (req.getTimeDimension()) {
            case "day":
                startDate = req.getStatDate();
                endDate = req.getStatDate();
                break;
            case "month":
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                YearMonth yearMonth = YearMonth.parse(req.getStatDate(), formatter);
                LocalDate start = yearMonth.atDay(1);
                startDate = DateUtils.formatDate(start);
                LocalDate end = yearMonth.atEndOfMonth().isAfter(DateUtils.yesterday()) ?
                        DateUtils.yesterday() : yearMonth.atEndOfMonth();
                endDate = DateUtils.formatDate(end);
                break;
            case "week":
                LocalDate localDate = DateUtils.parseLocalDate(req.getStatDate()).plusDays(6);
                startDate = req.getStatDate();
                endDate = localDate.isAfter(DateUtils.yesterday()) ?
                        DateUtils.yesterday().toString() : localDate.toString();
                break;
        }
        String configDate;
        if (req.getTimeDimension().equals("week")) {
            LocalDate start = DateUtils.parseLocalDate(startDate);
            LocalDate end = DateUtils.parseLocalDate(endDate);
            if (start.getYear() != end.getYear()) {
                configDate = endDate;
            }else {
                configDate = startDate;
            }
        }else {
            configDate = startDate;
        }
        List<InventoryOverviewZoneData> tempList = new ArrayList<>();
        //1.cvm相关数据获取
        List<InventoryOverviewZoneData> cvmData = queryCvmInventoryOverviewReport(startDate, endDate, req);
        if (ListUtils.isNotEmpty(cvmData)) {
            tempList.addAll(cvmData);
        }
        //2.数据库相关数据获取
        List<InventoryOverviewZoneData> CDBData = queryCDBInventoryOverviewReport(startDate, endDate, req);
        if (ListUtils.isNotEmpty(CDBData)) {
            tempList.addAll(CDBData);
        }
        //3.EKS相关数据获取
        List<InventoryOverviewZoneData> EKSData = queryEKSInventoryOverviewReport(startDate, endDate, req);
        if (ListUtils.isNotEmpty(EKSData)) {
            tempList.addAll(EKSData);
        }

        //4.大数据相关数据获取,数据范围暂定EMR
        List<String> bigDataProduct = Collections.singletonList("EMR");
        List<InventoryOverviewZoneData> EMRData = queryBigDataInventoryOverviewReport(startDate, endDate, bigDataProduct, req);
        if (ListUtils.isNotEmpty(EMRData)) {
            tempList.addAll(EMRData);
        }

        //5.CBS相关数据获取
        List<InventoryOverviewZoneData> CBSData = queryCBSInventoryOverviewReport(startDate, endDate, req);
        if (ListUtils.isNotEmpty(CBSData)) {
            tempList.addAll(CBSData);
        }


        List<StaticZoneDO> zoneInfo = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zoneInfo, StaticZoneDO::getZoneName, o -> o);
        Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> ret = new HashMap<>();
        for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
            String zone = entry.getKey();
            String region = entry.getValue();
            SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
            if (regionDO != null) {
                ret.put(zone, regionDO.getCountryName());
            }
        }
        for (InventoryOverviewZoneData item : tempList) {
            StaticZoneDO staticZoneDO = zoneMap.get(item.getZoneName());
            String country = ret.get(item.getZoneName());
            if (staticZoneDO != null) {
                item.setRegionName(staticZoneDO.getRegionName());
                item.setAreaName(staticZoneDO.getAreaName());
            }
            item.setCountry(country);
        }

        //针对国家进行筛选
        if (ListUtils.isNotEmpty(req.getCountry())) {
            tempList = tempList.stream().filter(o -> req.getCountry().contains(o.getCountry())).collect(Collectors.toList());
        }

        //6.网络相关数据获取
        if (!req.getDimension().equals("可用区") && ListUtils.isNotEmpty(tempList) && ListUtils.isEmpty(req.getZoneName())) {
            List<InventoryOverviewZoneData> netWorkData = queryNetworkInventoryOverviewReport(startDate, endDate, req);
            if (ListUtils.isNotEmpty(netWorkData)) {
                List<String> region = tempList.stream().map(InventoryOverviewZoneData::getRegionName).distinct()
                        .collect(Collectors.toList());
                netWorkData = netWorkData.stream().
                        filter(o -> region.contains(o.getRegionName())).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(netWorkData)) {
                    tempList.addAll(netWorkData);
                }
            }
        }
        //首先获取地理配置
        List<InventoryHealthOverviewZoneConfigDO> zoneConfig = getAllInventoryHealthZoneConfig();
        //获取服务水平和端到端利用率配置
        List<InventoryHealthOverviewRateConfigDO> rateConfig = getAllInventoryHealthConfig();

        Map<String, InventoryHealthOverviewZoneConfigDO> zoneConfigMap = ListUtils.toMap(zoneConfig,
                o -> String.valueOf(o.getId()), o -> o);
        Map<String, List<InventoryOverviewZoneData>> tempMap = new HashMap<>();
        for (InventoryOverviewZoneData item : tempList) {
            InventoryHealthOverviewZoneConfigDO zoneConfigByZoneInfo = getZoneConfigByZoneInfo(zoneConfig, item);
            if (zoneConfigByZoneInfo != null) {
                item.setSortNum(zoneConfigByZoneInfo.getSortNum());
                String key = req.getDimension().equals("可用区") ?
                        String.join("@", item.getProduct(), item.getZoneName(), String.valueOf(item.getSortNum()))
                        : String.join("@", item.getProduct(), String.valueOf(item.getSortNum()));
                List<InventoryOverviewZoneData> inventoryOverviewZoneData = tempMap.getOrDefault(key, new ArrayList<>());
                inventoryOverviewZoneData.add(item);
                tempMap.put(key, inventoryOverviewZoneData);
            }
        }
        for (Entry<String, List<InventoryOverviewZoneData>> entry : tempMap.entrySet()) {
            InventoryOverviewData inventoryOverviewData = new InventoryOverviewData();
            List<InventoryOverviewZoneData> value = entry.getValue();
            inventoryOverviewData.setProduct(value.get(0).getProduct());
            inventoryOverviewData.setSortNum(value.get(0).getSortNum());
            InventoryHealthOverviewZoneConfigDO config = zoneConfigMap.get(
                    String.valueOf(inventoryOverviewData.getSortNum()));
            inventoryOverviewData.setZoneOtherName(config.getOtherName());
            inventoryOverviewData.setRegionName(value.get(0).getRegionName());
            if (StringUtils.isNotBlank(config.getAreaName())) {
                inventoryOverviewData.setAreaName(value.get(0).getAreaName());
            }
            if (StringUtils.isNotBlank(config.getCountry()) || value.get(0).getCountry().equals("中国内地")) {
                inventoryOverviewData.setCountry(value.get(0).getCountry());
            }
            if (req.getDimension().equals("可用区")) {
                inventoryOverviewData.setZoneName(value.get(0).getZoneName());
            }
            inventoryOverviewData.setCustomhouseTitle(config.getCustomhouseTitle());
            inventoryOverviewData.setApiTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getApiTotal));
            inventoryOverviewData.setApiSucTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getApiSucTotal));
            inventoryOverviewData.setSoldTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getSoldTotal));
            inventoryOverviewData.setSoldOutTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getSoldOutTotal));
            if (inventoryOverviewData.getProduct().equals("CVM")) {
                inventoryOverviewData.setActualInventory(NumberUtils.sum(value,
                        InventoryOverviewZoneData::getActualInventory));
                inventoryOverviewData.setSoldOutTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getSoldOutTotal));
                inventoryOverviewData.setSoldTotal(NumberUtils.sum(value, InventoryOverviewZoneData::getSoldTotal));
                inventoryOverviewData.setSoldScale(NumberUtils.sum(value, InventoryOverviewZoneData::getSoldScale));
                inventoryOverviewData.setTotalScale(NumberUtils.sum(value, InventoryOverviewZoneData::getTotalScale));
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal soldTotal = inventoryOverviewData.getSoldTotal();
                BigDecimal soldOut = inventoryOverviewData.getSoldOutTotal();
                BigDecimal apiTotal = inventoryOverviewData.getApiTotal();
                BigDecimal apiSuc = inventoryOverviewData.getApiSucTotal();
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                inventoryOverviewData.setServiceLevel(slap);
                BigDecimal soldScale = inventoryOverviewData.getSoldScale();
                BigDecimal totalScale = inventoryOverviewData.getTotalScale();
                if (totalScale.intValue() > 0) {
                    inventoryOverviewData.setEndToEndRate(soldScale.divide(totalScale, 4, RoundingMode.HALF_UP));
                }
                InventoryHealthOverviewRateConfigDO service = getRateConfig(rateConfig, inventoryOverviewData,
                        configDate, "服务水平");
                InventoryHealthOverviewRateConfigDO endToEnd = getRateConfig(rateConfig, inventoryOverviewData,
                        configDate, "端到端利用率");
                List<InventoryHealthOverviewRateConfigDO> globalRate = rateConfig.stream()
                        .filter(o -> o.getCustomhouseTitle().equals("全球")).collect(Collectors.toList());
                InventoryHealthOverviewRateConfigDO globalService = getGlobalRateConfig(globalRate, inventoryOverviewData.getProduct(),
                        configDate, "服务水平");
                InventoryHealthOverviewRateConfigDO globalEndToEnd = getGlobalRateConfig(globalRate,
                        inventoryOverviewData.getProduct(), configDate, "端到端利用率");
                if (service != null) {
                    inventoryOverviewData.setServiceLevelTarget(service.getTargetValue());
                }
                if (endToEnd != null) {
                    inventoryOverviewData.setEndToEndRateTarget(endToEnd.getTargetValue());
                }

                if (globalService != null) {
                    inventoryOverviewData.setGlobalServiceLevelTarget(globalService.getTargetValue());
                }
                if (globalEndToEnd != null) {
                    inventoryOverviewData.setGlobalEndToEndRateTarget(globalEndToEnd.getTargetValue());
                }

            }else if (inventoryOverviewData.getProduct().contains("数据库")) {
                inventoryOverviewData.setActualMem(NumberUtils.sum(value, InventoryOverviewZoneData::getActualMem));
                inventoryOverviewData.setActualDisk(NumberUtils.sum(value, InventoryOverviewZoneData::getActualDisk));
                BigDecimal apiTotal = inventoryOverviewData.getApiTotal();
                BigDecimal apiSucTotal = inventoryOverviewData.getApiSucTotal();
                if (inventoryOverviewData.getProduct().contains("数据库-TDSQL-C")) {
                    BigDecimal soldTotal = inventoryOverviewData.getSoldTotal();
                    BigDecimal soldOutTotal = inventoryOverviewData.getSoldOutTotal();
                    BigDecimal slap = BigDecimal.valueOf(0.7);
                    BigDecimal soldRt = BigDecimal.ZERO;
                    if (apiTotal.intValue() > 0) {
                        slap = apiSucTotal.divide(apiTotal, 4,
                                RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.7));
                    }
                    if (soldTotal.intValue() > 0) {
                        soldRt = soldOutTotal.divide(soldTotal, 4, RoundingMode.HALF_UP);
                    }
                    slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                    inventoryOverviewData.setServiceLevel(slap);
                }else {
                    if (apiSucTotal.intValue() > 0) {
                        inventoryOverviewData.setServiceLevel(apiSucTotal.divide(apiTotal, 4, RoundingMode.HALF_UP));
                    }
                }
                InventoryHealthOverviewRateConfigDO service = getRateConfig(rateConfig, inventoryOverviewData,
                        configDate, "服务水平");
                List<InventoryHealthOverviewRateConfigDO> globalRate = rateConfig.stream()
                        .filter(o -> o.getCustomhouseTitle().equals("全球")).collect(Collectors.toList());
                InventoryHealthOverviewRateConfigDO globalService = getGlobalRateConfig(globalRate, inventoryOverviewData.getProduct(),
                        configDate, "服务水平");
                if (service != null) {
                    inventoryOverviewData.setServiceLevelTarget(service.getTargetValue());
                }
                if (globalService != null) {
                    inventoryOverviewData.setGlobalServiceLevelTarget(globalService.getTargetValue());
                }
            }else {
                BigDecimal apiTotal = inventoryOverviewData.getApiTotal();
                BigDecimal apiSucTotal = inventoryOverviewData.getApiSucTotal();
                if (apiSucTotal.intValue() > 0) {
                    inventoryOverviewData.setServiceLevel(apiSucTotal.divide(apiTotal, 4, RoundingMode.HALF_UP));
                }
                InventoryHealthOverviewRateConfigDO service = getRateConfig(rateConfig, inventoryOverviewData,
                        configDate, "服务水平");
                List<InventoryHealthOverviewRateConfigDO> globalRate = rateConfig.stream()
                        .filter(o -> o.getCustomhouseTitle().equals("全球")).collect(Collectors.toList());
                InventoryHealthOverviewRateConfigDO globalService = getGlobalRateConfig(globalRate, inventoryOverviewData.getProduct(),
                        configDate, "服务水平");
                if (service != null) {
                    inventoryOverviewData.setServiceLevelTarget(service.getTargetValue());
                }
                if (globalService != null) {
                    inventoryOverviewData.setGlobalServiceLevelTarget(globalService.getTargetValue());
                }
            }
            result.add(inventoryOverviewData);
        }

        result.sort((o1, o2) -> {
            if (o1.getProduct().equals(o2.getProduct())) {
                return o1.getSortNum().compareTo(o2.getSortNum());
            }
            return o1.getProduct().compareTo(o2.getProduct());
        });
        return result;
    }

    @Override
    public List<InventoryOverviewTrendData> queryInventoryOverviewTrendReport(InventoryOverviewTrendReq req) {
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getTimeDimension()) {
            case "day":
                dateMap = getDayMap(req.getStartDate(), req.getEndDate());
                break;
            case "month":
                dateMap = getMonthMap(req.getStartDate(), req.getEndDate());
                break;
            case "week":
                dateMap = getWeekMap(req.getStartDate(), req.getEndDate());
                break;
        }
        if (DateUtils.parseLocalDate(req.getEndDate()).isAfter(DateUtils.yesterday())) {
            req.setEndDate(DateUtils.yesterday().toString());
        }
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        //获取地理配置
        List<InventoryHealthOverviewZoneConfigDO> zoneConfig = getAllInventoryHealthZoneConfig();
        List<StaticZoneDO> allZoneInfos = dictService.getAllZoneInfos();
        List<SoeRegionNameCountryDO> countries = demandDBHelper.getAll(SoeRegionNameCountryDO.class);
        Set<String> region = new HashSet<>();
        for (InventoryHealthOverviewZoneConfigDO config : zoneConfig) {
            if (StringUtils.isNotBlank(config.getRegionName())) {
                List<String> collect = allZoneInfos.stream()
                        .map(StaticZoneDO::getRegionName).filter(regionName -> regionName.equals(config.getRegionName()))
                        .collect(
                                Collectors.toList());
                if (ListUtils.isNotEmpty(collect)) {
                    region.add(collect.get(0));
                }
                continue;
            }
            if (StringUtils.isNotBlank(config.getAreaName())) {
                List<StaticZoneDO> collect = allZoneInfos.stream()
                        .filter(o -> o.getAreaName().equals(config.getAreaName())).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(collect)) {
                    region.add(collect.get(0).getRegionName());
                }
                continue;
            }
            if (StringUtils.isNotBlank(config.getCountry())) {
                List<SoeRegionNameCountryDO> collect = countries.stream()
                        .filter(o -> o.getCountryName().equals(config.getCountry())).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(collect)) {
                    region.add(collect.get(0).getRegionName());
                }
            }
         }
        //1.获取CVM数据
        Map<String, String> finalDateMap = dateMap;
        Future<List<InventoryOverviewTrendData>> cvmFuture = threadPool.submit(() -> queryCvmInventoryOverviewTrendReport(
                req.getStartDate(), req.getEndDate(), req, finalDateMap, region));

        //4.大数据相关数据获取,数据范围暂定EMR
        List<String> bigDataProduct = Collections.singletonList("EMR");
        Future<List<InventoryOverviewTrendData>> bigFuture = threadPool.submit(
                () -> queryBigDataInventoryOverviewTrendReport(
                        req.getStartDate(), req.getEndDate(), bigDataProduct, req, finalDateMap, region));
        //3.EKS相关数据获取
        Future<List<InventoryOverviewTrendData>> EKSFuture = threadPool.submit(() -> queryEKSInventoryOverviewTrendReport(
                req.getStartDate(), req.getEndDate(), req, finalDateMap, region));
        //CBS相关数据获取
        Future<List<InventoryOverviewTrendData>> CBSFuture = threadPool.submit(() -> queryCBSInventoryOverviewTrendReport(
                req.getStartDate(), req.getEndDate(), req, finalDateMap, region));
        //2.数据库相关数据获取
        List<InventoryOverviewTrendData> CDBList = queryCDBInventoryOverviewTrendReport(
                req.getStartDate(), req.getEndDate(), req, dateMap, region);
        if (ListUtils.isNotEmpty(CDBList)) {
            result.addAll(CDBList);
        }
        try {
            List<InventoryOverviewTrendData> inventoryList = cvmFuture.get();
            List<InventoryOverviewTrendData> bigDataList = bigFuture.get();
            List<InventoryOverviewTrendData> EKSList = EKSFuture.get();
            List<InventoryOverviewTrendData> CBSList = CBSFuture.get();
            if (ListUtils.isNotEmpty(bigDataList)) {
                result.addAll(bigDataList);
            }
            if (ListUtils.isNotEmpty(inventoryList)) {
                result.addAll(inventoryList);
            }
            if (ListUtils.isNotEmpty(EKSList)) {
                result.addAll(EKSList);
            }
            if (ListUtils.isNotEmpty(CBSList)) {
                result.addAll(CBSList);
            }
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        Map<String, List<InventoryOverviewTrendData>> mapList = ListUtils.toMapList(result,
                InventoryOverviewTrendData::getStatTime, o -> o);
        Map<String, List<String>> regionMap = new HashMap<>();
        for (Entry<String, List<InventoryOverviewTrendData>> entry : mapList.entrySet()) {
            Set<String> tempRegion = new HashSet<>();
            for (InventoryOverviewTrendData data : entry.getValue()) {
                if (ListUtils.isNotEmpty(data.getRegionName())) {
                    tempRegion.addAll(data.getRegionName());
                }
            }
            regionMap.put(entry.getKey(), new ArrayList<>(tempRegion));
        }
        //5.网络的相关数据获取
        if (ListUtils.isNotEmpty(result) && ListUtils.isEmpty(req.getZoneName())) {
            List<InventoryOverviewTrendData> networkList = queryNetworkInventoryOverviewTrendReport(
                    req.getStartDate(), req.getEndDate(), req, dateMap, regionMap);
            if (ListUtils.isNotEmpty(networkList)) {
                result.addAll(networkList);
            }
        }
        Set<String> regions = new HashSet<>();
        for (InventoryOverviewTrendData item : result) {
            if (ListUtils.isNotEmpty(item.getRegionName())) {
                regions.addAll(item.getRegionName());
            }
        }
        //获取境内外信息
        Map<String, String> customhouseTitleMap = ListUtils.toMap(countries, SoeRegionNameCountryDO::getRegionName,
                SoeRegionNameCountryDO::getCustomhouseTitle);
        List<String> customhouseTitles = regions.stream().map(customhouseTitleMap::get).distinct().collect(Collectors.toList());
        //获取服务水平和端到端利用率配置
        List<InventoryHealthOverviewRateConfigDO> rateConfig = getAllInventoryHealthConfig();
        for (InventoryOverviewTrendData item : result) {
            String startDate = item.getStatTime();
            if (req.getTimeDimension().equals("month")) {
                YearMonth parse = YearMonth.parse(item.getStatTime());
                startDate = parse.atDay(1).toString();
            }else if (req.getTimeDimension().equals("week")) {
                List<String> days = new ArrayList<>();
                for (Entry<String, String> entry : dateMap.entrySet()) {
                    if (entry.getValue().equals(startDate)) {
                        days.add(entry.getKey());
                    }
                }
                days.sort(Comparator.naturalOrder());
                LocalDate start = DateUtils.parseLocalDate(days.get(0));
                LocalDate end = DateUtils.parseLocalDate(days.get(days.size() - 1));
                if (start.getYear() != end.getYear()) {
                    startDate = end.toString();
                }else {
                    startDate = days.get(0);
                }
            }
            List<InventoryHealthOverviewRateConfigDO> globalRate = new ArrayList<>();
            if (ListUtils.isNotEmpty(customhouseTitles)) {
                if (customhouseTitles.contains("境内") && customhouseTitles.contains("境外")) {
                    globalRate = rateConfig.stream()
                            .filter(o -> o.getCustomhouseTitle().equals("全球")).collect(Collectors.toList());
                }else if (customhouseTitles.contains("境内")) {
                    globalRate = rateConfig.stream()
                            .filter(o -> o.getCustomhouseTitle().equals("境内")).collect(Collectors.toList());
                }else if (customhouseTitles.contains("境外")) {
                    globalRate = rateConfig.stream()
                            .filter(o -> o.getCustomhouseTitle().equals("境外")).collect(Collectors.toList());
                }
            }
            InventoryHealthOverviewRateConfigDO globalService = getGlobalRateConfig(globalRate, item.getProduct(),
                    startDate, "服务水平");
            if (globalService != null) {
                item.setGlobalServiceLevelTarget(globalService.getTargetValue());
            }
            if (item.getProduct().equals("CVM")) {
                InventoryHealthOverviewRateConfigDO globalEndToEnd = getGlobalRateConfig(globalRate,
                        item.getProduct(), startDate, "端到端利用率");
                if (globalEndToEnd != null) {
                    item.setGlobalEndToEndRateTarget(globalEndToEnd.getTargetValue());
                }
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal soldTotal = item.getSoldTotal();
                BigDecimal soldOut = item.getSoldOutTotal();
                BigDecimal apiTotal = item.getApiTotal();
                BigDecimal apiSuc = item.getApiSucTotal();
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, RoundingMode.UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, RoundingMode.UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
                BigDecimal soldScale = item.getSoldScale();
                BigDecimal totalScale = item.getTotalScale();
                if (totalScale.intValue() > 0) {
                    item.setEndToEndRate(soldScale.divide(totalScale, 4, RoundingMode.HALF_UP));
                }
            }else if (item.getProduct().equals("数据库-TDSQL-C")) {
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal soldTotal = item.getSoldTotal();
                BigDecimal soldOut = item.getSoldOutTotal();
                BigDecimal apiTotal = item.getApiTotal();
                BigDecimal apiSuc = item.getApiSucTotal();
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, RoundingMode.UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, RoundingMode.UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
            }else {
                if (item.getApiTotal().compareTo(BigDecimal.ZERO) > 0) {
                    item.setServiceLevel(item.getApiSucTotal().divide(item.getApiTotal(), 4, RoundingMode.HALF_UP));
                }
            }
        }
        return result;
    }

    @Override
    public void sendInventoryHealthMail() {
        LocalDate today = DateUtils.today();
        LocalDate yesterday = DateUtils.yesterday();
        // 获取上周的周日(上周最后一天)
        LocalDate lastWeekSunday = today.minusWeeks(1).with(DayOfWeek.SUNDAY);
        // 获取上周的周一(上周第一天)
        LocalDate lastWeekMonday = lastWeekSunday.minusDays(6);

        //获取近6个月的开始日期和结束日期
        String endDate = yesterday.toString();
        // 往前数5个月
        LocalDate fiveMonthsAgo = yesterday.minusMonths(5);
        // 获取该月的第一天
        String startDate = fiveMonthsAgo.withDayOfMonth(1).toString();
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("monthDescribe", yesterday.getMonth() != today.getMonth() ? "上月" : "当月");
        dataModel.put("timestamp", DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        dataModel.put("currentDate", DateUtils.format(yesterday, "MM/dd"));
        dataModel.put("monthFirstDay", DateUtils.format(yesterday.withDayOfMonth(1), "MM/dd"));
        dataModel.put("lastWeekMonday", DateUtils.format(lastWeekMonday, "MM/dd"));
        dataModel.put("lastWeekSunday", DateUtils.format(lastWeekSunday, "MM/dd"));
        String[] split = today.toString().split("-");
        dataModel.put("today", split[0] + "年" + split[1] + "月" + split[2] + "日");

        List<String> products = Arrays.asList("CVM", "CBS", "网络-ALL", "数据库-CDB", "EKS", "大数据");

        //1.请求上周的库存健康趋势
        InventoryOverviewTrendReq weekTrendReq = new InventoryOverviewTrendReq();
        weekTrendReq.setTimeDimension("week");
        weekTrendReq.setCustomhouseTitle(Arrays.asList("境外"));
        weekTrendReq.setStartDate(lastWeekMonday.toString());
        weekTrendReq.setEndDate(lastWeekSunday.toString());
        List<InventoryOverviewTrendData> weekTrendData = queryInventoryOverviewTrendReport(weekTrendReq);
        weekTrendData = weekTrendData.stream().filter(o -> products.contains(o.getProduct())).collect(Collectors.toList());

        //2.请求近6个月的库存健康趋势
        InventoryOverviewTrendReq monthTrendReq = new InventoryOverviewTrendReq();
        monthTrendReq.setTimeDimension("month");
        monthTrendReq.setCustomhouseTitle(Arrays.asList("境外"));
        monthTrendReq.setStartDate(startDate);
        monthTrendReq.setEndDate(endDate);
        List<InventoryOverviewTrendData> monthTrendData = queryInventoryOverviewTrendReport(monthTrendReq);
        monthTrendData = monthTrendData.stream().filter(o -> products.contains(o.getProduct())).collect(Collectors.toList());

        //3.请求上周的库存健康概览
        InventoryOverviewReq weekReq = new InventoryOverviewReq();
        weekReq.setTimeDimension("week");
        weekReq.setStatDate(lastWeekMonday.toString());
        weekReq.setDimension("区域别称");
        weekReq.setCustomhouseTitle(Arrays.asList("境外"));
        List<InventoryOverviewData> weekOverViewData = queryInventoryOverviewReport(weekReq);
        weekOverViewData = weekOverViewData.stream().filter(o -> o.getProduct().equals("CVM")).collect(Collectors.toList());

        //4.请求近6个月的库存健康概览
        List<InventoryOverviewWithDateData> monthViewData = new ArrayList<>();
        YearMonth firstMonth = YearMonth.of(fiveMonthsAgo.withDayOfMonth(1).getYear(), fiveMonthsAgo.withDayOfMonth(1).getMonthValue());
        YearMonth endMonth = YearMonth.of(yesterday.getYear(), yesterday.getMonthValue());
        while(!firstMonth.isAfter(endMonth)) {
            InventoryOverviewReq req = new InventoryOverviewReq();
            req.setTimeDimension("month");
            req.setStatDate(firstMonth.atDay(1).toString());
            req.setDimension("区域别称");
            req.setCustomhouseTitle(Arrays.asList("境外"));
            List<InventoryOverviewData> data = queryInventoryOverviewReport(req);
            data = data.stream().filter(o -> o.getProduct().equals("CVM")).collect(Collectors.toList());
            for (InventoryOverviewData datum : data) {
                InventoryOverviewWithDateData tempData = new InventoryOverviewWithDateData();
                tempData.setStatTime(firstMonth.toString());
                BeanUtils.copyProperties(datum, tempData);
                monthViewData.add(tempData);
            }
            firstMonth = firstMonth.plusMonths(1);
        }

        monthTrendData = monthTrendData.stream().filter(o -> products.contains(o.getProduct())).collect(Collectors.toList());
        Map<String, String> productChart = new HashMap<>();
        Map<String, List<InventoryOverviewTrendData>> productTrendMap = ListUtils.toMapList(monthTrendData,
                InventoryOverviewTrendData::getProduct,
                o -> o);
        for (Entry<String, List<InventoryOverviewTrendData>> entry : productTrendMap.entrySet()) {
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            List<InventoryOverviewTrendData> value = entry.getValue();
            BigDecimal productMin = value.stream().map(InventoryOverviewTrendData::getServiceLevel).sorted(BigDecimal::compareTo)
                    .collect(Collectors.toList()).get(0);
            value.sort(Comparator.comparing(InventoryOverviewTrendData::getStatTime));
            for (int i = 0; i < value.size(); i ++) {
                InventoryOverviewTrendData data = value.get(i);
                BigDecimal bigDecimal = data.getServiceLevel().setScale(3, BigDecimal.ROUND_HALF_UP);
                dataset.setValue(bigDecimal, "服务水平", data.getStatTime().substring(2));
            }
            JFreeChart chart = productMin.compareTo(BigDecimal.valueOf(0.9)) < 0? genTrendChart(dataset, 0, 1.18, 0.2) :
                    genTrendChart(dataset, 0.9, 1.018, 0.02);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            try{
                ChartUtils.writeChartAsPNG(outputStream, chart, 600, 225);
            }catch (Exception e) {
                AlarmRobotUtil.doAlarm("sendInventoryHealthMail",
                        "图表生成PNG失败", null,false);
            }
            // 4. 转换为Base64字符串
            String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());
            productChart.put(entry.getKey(), "data:image/png;base64," + base64Image);
        }
        Map<String, List<InventoryOverviewWithDateData>> regionTrendMap = ListUtils.toMapList(monthViewData,
                InventoryOverviewWithDateData::getZoneOtherName,
                o -> o);
        Map<String, String> regionChart = new HashMap<>();
        for (Entry<String, List<InventoryOverviewWithDateData>> entry : regionTrendMap.entrySet()) {
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            List<InventoryOverviewWithDateData> value = entry.getValue();
            BigDecimal regionMin = value.stream().map(InventoryOverviewWithDateData::getServiceLevel).sorted(BigDecimal::compareTo)
                    .collect(Collectors.toList()).get(0);
            value.sort(Comparator.comparing(InventoryOverviewWithDateData::getStatTime));
            for (int i = 0; i < value.size(); i ++) {
                InventoryOverviewWithDateData data = value.get(i);
                BigDecimal bigDecimal = data.getServiceLevel().setScale(3, BigDecimal.ROUND_HALF_UP);
                dataset.setValue(bigDecimal, "服务水平", data.getStatTime().substring(2));
                JFreeChart chart = regionMin.compareTo(BigDecimal.valueOf(0.9)) < 0? genTrendChart(dataset, 0, 1.18, 0.2) :
                        genTrendChart(dataset, 0.9, 1.018, 0.02);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                try{
                    ChartUtils.writeChartAsPNG(outputStream, chart, 600, 225);
                }catch (Exception e) {
                    AlarmRobotUtil.doAlarm("sendInventoryHealthMail",
                            "图表生成PNG失败", null,false);
                }
                // 4. 转换为Base64字符串
                String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());
                regionChart.put(entry.getKey(), "data:image/png;base64," + base64Image);
            }

        }
        //海外主力产品服务水平概览
        List<InventoryWeekMailData> productList = new ArrayList<>();
        for (InventoryOverviewTrendData weekTrend : weekTrendData) {
            InventoryWeekMailData inventoryWeekMailData = new InventoryWeekMailData();
            if (weekTrend.getProduct().equals("网络-ALL")) {
                inventoryWeekMailData.setTag("网络");
            }else if (weekTrend.getProduct().equals("数据库-CDB")) {
                inventoryWeekMailData.setTag("数据库");
            }else {
                inventoryWeekMailData.setTag(weekTrend.getProduct());
            }
            BigDecimal weekServiceLevel = weekTrend.getServiceLevel();
            BigDecimal target = weekTrend.getGlobalServiceLevelTarget();
            List<InventoryOverviewTrendData> collect = monthTrendData.stream().filter(o -> o.getStatTime()
                    .equals(YearMonth.of(yesterday.getYear(), yesterday.getMonthValue()).toString()) && o.getProduct()
                    .equals(weekTrend.getProduct())).collect(
                    Collectors.toList());
            InventoryOverviewTrendData monthTrend = collect.get(0);
            BigDecimal monthServiceLevel = monthTrend.getServiceLevel();
            if (target.compareTo(weekServiceLevel) < 0) {
                inventoryWeekMailData.setWeekColor("green");
            }else {
                inventoryWeekMailData.setWeekColor("red");
            }
            if (target.compareTo(monthServiceLevel) < 0) {
                inventoryWeekMailData.setMonthColor("green");
            }else {
                inventoryWeekMailData.setMonthColor("red");
            }
            inventoryWeekMailData.setWeekServiceLevel(toPercent(weekServiceLevel));
            inventoryWeekMailData.setMonthServiceLevel(toPercent(monthServiceLevel));
            inventoryWeekMailData.setChart(productChart.get(weekTrend.getProduct()));
            inventoryWeekMailData.setTarget(toPercent(target));
            productList.add(inventoryWeekMailData);
        }

        //海外CVM产品服务水平详情设置
        List<InventoryWeekMailData> regionList = new ArrayList<>();
        for (InventoryOverviewData weekView : weekOverViewData) {
            InventoryWeekMailData inventoryWeekMailData = new InventoryWeekMailData();
            inventoryWeekMailData.setTag(weekView.getZoneOtherName());
            BigDecimal weekServiceLevel = weekView.getServiceLevel();
            BigDecimal target = weekView.getServiceLevelTarget();
            List<InventoryOverviewWithDateData> collect = monthViewData.stream().filter(o -> o.getStatTime()
                    .equals(YearMonth.of(yesterday.getYear(), yesterday.getMonthValue()).toString()) && o.getZoneOtherName()
                    .equals(weekView.getZoneOtherName())).collect(
                    Collectors.toList());
            InventoryOverviewWithDateData monthTrend = collect.get(0);
            BigDecimal monthServiceLevel = monthTrend.getServiceLevel();
            if (target.compareTo(weekServiceLevel) < 0) {
                inventoryWeekMailData.setWeekColor("green");
            }else {
                inventoryWeekMailData.setWeekColor("red");
            }
            if (target.compareTo(monthServiceLevel) < 0) {
                inventoryWeekMailData.setMonthColor("green");
            }else {
                inventoryWeekMailData.setMonthColor("red");
            }
            inventoryWeekMailData.setWeekServiceLevel(toPercent(weekServiceLevel));
            inventoryWeekMailData.setMonthServiceLevel(toPercent(monthServiceLevel));
            inventoryWeekMailData.setTarget(toPercent(target));
            inventoryWeekMailData.setChart(regionChart.get(weekView.getZoneOtherName()));
            regionList.add(inventoryWeekMailData);
        }
        Map<String, Integer> productSort = new HashMap<>();
        productSort.put("CVM", 0);
        productSort.put("CBS", 1);
        productSort.put("网络", 2);
        productSort.put("数据库", 3);
        productSort.put("EKS", 4);
        productSort.put("大数据", 5);

        productList.sort((o1, o2) -> {
            Integer i1 = productSort.get(o1.getTag());
            Integer i2 = productSort.get(o2.getTag());
            return i1 - i2;
        });
        dataModel.put("productList", productList);
        dataModel.put("regionList", regionList);

        String template = ORMUtils.getSql("/freemarker/overseas_week_report_for_email.html.ftl");
        //使用freemarker模版引擎解析出通知内容
        StringReader sd = new StringReader(template);
        StringWriter sw = new StringWriter();
        try {
            Template tm = new Template("freemarker",sd,null);
            tm.process(dataModel,sw);
        } catch (IOException | TemplateException e) {
            throw new ITException(e);
        }finally {
            IOUtils.close(sd);
            IOUtils.close(sw);
        }
        template = sw.toString();
        String users = mailUsers.get();
        try{
            dictService.eventNotice("product_service_level_report_mail",null,template, users);
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("sendInventoryHealthMail",
                    "邮件发送失败", null,false);
        }
    }

    public  String toPercent(BigDecimal value) {
        // 乘以100，保留一位小数
        BigDecimal percent = value.multiply(BigDecimal.valueOf(100));
        // 格式化为最多一位小数（小数为0时自动去掉）
        DecimalFormat df = new DecimalFormat("0.#");
        return df.format(percent) + "%";
    }


    public JFreeChart genTrendChart(DefaultCategoryDataset dataset, double low, double upper, double unit) {
        JFreeChart chart = ChartFactory.createLineChart(
                null, // 标题
                null, // 横轴
                null, // 纵轴
                dataset,
                PlotOrientation.VERTICAL,
                false, // 不显示图例
                false,
                false
        );
        // 3. 美化
        CategoryPlot plot = chart.getCategoryPlot();
        plot.setBackgroundPaint(new Color(255, 255, 255)); // 背景淡蓝
        plot.setRangeGridlinePaint(new Color(200, 200, 200)); // 网格线浅灰
        plot.setRangeGridlineStroke(new BasicStroke(1f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL, 0, new float[]{4, 4}, 0)); // 虚线

        // 4. 线条和点
        LineAndShapeRenderer renderer = new AreaGradientLineRenderer();
        renderer.setSeriesPaint(0, new Color(41, 121, 255)); // 蓝色
        renderer.setSeriesStroke(0, new BasicStroke(3.0f)); // 粗线
        renderer.setSeriesShapesVisible(0, true); // 显示点
        renderer.setSeriesShape(0, new java.awt.geom.Ellipse2D.Double(-4, -4, 8, 8)); // 圆点
        renderer.setSeriesItemLabelsVisible(0, true); // 显示标签
        renderer.setDefaultPositiveItemLabelPosition(new ItemLabelPosition(
                ItemLabelAnchor.OUTSIDE12, TextAnchor.BOTTOM_CENTER));
        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        percentFormat.setMinimumFractionDigits(1);
        percentFormat.setMaximumFractionDigits(1);
        renderer.setDefaultItemLabelGenerator(new StandardCategoryItemLabelGenerator("{2}", percentFormat));
        renderer.setDefaultItemLabelFont(new Font("微软雅黑", Font.PLAIN, 18));
        renderer.setDefaultItemLabelPaint(new Color(0, 0, 0));
        plot.setRenderer(renderer);

        // 5. 纵轴百分比
        NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
        rangeAxis.setRange(low, upper);
        rangeAxis.setTickUnit(new org.jfree.chart.axis.NumberTickUnit(unit));
        rangeAxis.setNumberFormatOverride(NumberFormat.getPercentInstance());
        rangeAxis.setLabelFont(new Font("微软雅黑", Font.PLAIN, 18));
        rangeAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 18));

        // 6. 横轴字体
        plot.getDomainAxis().setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 18));

        plot.setOutlineVisible(false);
        plot.getRangeAxis().setAxisLineVisible(false);
        chart.setAntiAlias(true);
        chart.setTextAntiAlias(true);

        // 7. 透明度
        plot.setForegroundAlpha(0.8f);
        return chart;
    }
    private List<InventoryOverviewZoneData> queryNetworkInventoryOverviewReport(String startDate, String endDate,
            InventoryOverviewReq req) {
        List<InventoryOverviewZoneData> result = new ArrayList<>();
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            condition.and("country_name in (?)", req.getCountry());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            condition.and("region_name in (?)", req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            condition.and("customhouse_title in (?)", req.getCustomhouseTitle());
        }
        condition.and("stat_time between ? and ?", startDate, endDate);
        List<ClsLogServiceLevelDiDO> all = ckstdcrpDBHelper.getAll(ClsLogServiceLevelDiDO.class, condition.getSQL(),
                condition.getParams());
        Map<String, List<ClsLogServiceLevelDiDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", o.getRegionName(), o.getNetworkProduct()), o -> o);
        for (Entry<String, List<ClsLogServiceLevelDiDO>> entry : mapList.entrySet()) {
            List<ClsLogServiceLevelDiDO> value = entry.getValue();
            InventoryOverviewZoneData data = new InventoryOverviewZoneData();
            data.setRegionName(value.get(0).getRegionName());
            data.setCountry(value.get(0).getCountryName());
            data.setProduct("网络-" + value.get(0).getNetworkProduct());
            BigDecimal internalFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInternalFailed);
            BigDecimal insufficientFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInsufficientResourceFailed);
            BigDecimal total = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getTotal);
            data.setApiTotal(total);
            data.setApiSucTotal(total.subtract(insufficientFailed).subtract(internalFailed));
            result.add(data);
        }
        Map<String, List<ClsLogServiceLevelDiDO>> allMap = ListUtils.toMapList(all,
                ClsLogServiceLevelDiDO::getRegionName, o -> o);
        for (Entry<String, List<ClsLogServiceLevelDiDO>> entry : allMap.entrySet()) {
            List<ClsLogServiceLevelDiDO> value = entry.getValue();
            InventoryOverviewZoneData data = new InventoryOverviewZoneData();
            data.setRegionName(value.get(0).getRegionName());
            data.setCountry(value.get(0).getCountryName());
            data.setProduct("网络-ALL");
            BigDecimal internalFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInternalFailed);
            BigDecimal insufficientFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInsufficientResourceFailed);
            BigDecimal total = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getTotal);
            data.setApiTotal(total);
            data.setApiSucTotal(total.subtract(insufficientFailed).subtract(internalFailed));
            result.add(data);
        }
        return result;
    }

    private InventoryHealthOverviewZoneConfigDO getZoneConfigByZoneInfo(List<InventoryHealthOverviewZoneConfigDO> zoneConfig, InventoryOverviewZoneData data) {
        //首先找地域
        List<InventoryHealthOverviewZoneConfigDO> collect = zoneConfig.stream()
                .filter(o -> StringUtils.isNotBlank(o.getRegionName()) && StringUtils.isNotBlank(data.getRegionName())
                        && o.getRegionName().equals(data.getRegionName())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(collect)) {
            return collect.get(0);
        }
        //再找区域
        List<InventoryHealthOverviewZoneConfigDO> collect1 = zoneConfig.stream()
                .filter(o -> StringUtils.isNotBlank(o.getAreaName()) && StringUtils.isNotBlank(data.getAreaName())
                        && o.getAreaName().equals(data.getAreaName())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(collect1)) {
            return collect1.get(0);
        }
        //最后找国家
        List<InventoryHealthOverviewZoneConfigDO> collect2 = zoneConfig.stream()
                .filter(o -> StringUtils.isNotBlank(o.getCountry()) && StringUtils.isNotBlank(data.getCountry())
                        && o.getCountry().equals(data.getCountry())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(collect2)) {
            return collect2.get(0);
        }
        return null;
    }

    private InventoryHealthOverviewRateConfigDO getRateConfig(List<InventoryHealthOverviewRateConfigDO> rateConfig, InventoryOverviewData inventoryOverviewData, String statDate, String type) {
        LocalDate localDate = DateUtils.parseLocalDate(statDate);
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        Map<String, List<InventoryHealthOverviewRateConfigDO>> configMap = ListUtils.toMapList(rateConfig,
                o -> String.join("@", o.getConfigIndex(), o.getProduct(), o.getCustomhouseTitle(), String.valueOf(o.getConfigYear())), o -> o);
        String product = inventoryOverviewData.getProduct();
        if (product.contains("网络")) {
            product = "网络";
        }
        if (product.contains("数据库")) {
            product = "数据库";
        }
        String key = String.join("@", type, product, inventoryOverviewData.getCustomhouseTitle(), String.valueOf(year));
        List<InventoryHealthOverviewRateConfigDO> configList = configMap.get(key);
        if (ListUtils.isNotEmpty(configList)) {
            List<InventoryHealthOverviewRateConfigDO> collect = configList.stream()
                    .filter(o -> o.getConfigMonth() != null && o.getConfigMonth() == month).collect(
                            Collectors.toList());
            if (ListUtils.isNotEmpty(collect)) {
                return configList.get(0);
            }else {
                List<InventoryHealthOverviewRateConfigDO> nullCollect = configList.stream()
                        .filter(o -> o.getConfigMonth() == null).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(nullCollect)) {
                    return nullCollect.get(0);
                }
            }

        }
        return null;
    }

    private InventoryHealthOverviewRateConfigDO getGlobalRateConfig(List<InventoryHealthOverviewRateConfigDO> rateConfig, String product, String statDate, String type) {
        LocalDate localDate = DateUtils.parseLocalDate(statDate);
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        Map<String, List<InventoryHealthOverviewRateConfigDO>> configMap = ListUtils.toMapList(rateConfig,
                o -> String.join("@", o.getConfigIndex(), o.getProduct(), String.valueOf(o.getConfigYear())), o -> o);
        if (product.contains("网络")) {
            product = "网络";
        }
        if (product.contains("数据库")) {
            product = "数据库";
        }
        String key = String.join("@", type, product, String.valueOf(year));
        List<InventoryHealthOverviewRateConfigDO> configList = configMap.get(key);
        if (ListUtils.isNotEmpty(configList)) {
            List<InventoryHealthOverviewRateConfigDO> collect = configList.stream()
                    .filter(o -> o.getConfigMonth() != null && o.getConfigMonth() == month).collect(
                            Collectors.toList());
            if (ListUtils.isNotEmpty(collect)) {
                return configList.get(0);
            }else {
                List<InventoryHealthOverviewRateConfigDO> nullCollect = configList.stream()
                        .filter(o -> o.getConfigMonth() == null).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(nullCollect)) {
                    return nullCollect.get(0);
                }
            }

        }
        return null;
    }

    private List<InventoryOverviewZoneData> queryCvmInventoryOverviewReport(String startDate, String endDate,
            InventoryOverviewReq req) {
        List<InventoryOverviewZoneData> result = new ArrayList<>();
        //1.获取cvm端到端、服务水平、库存数据
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        List<String> instanceCategory = Collections.singletonList("PRINCIPAL");
        WhereSQL inventoryCondition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCondition = bean.genCategoryCondition(zoneCategory, instanceCategory, false,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        inventoryCondition.and(categoryCondition);
        List<String> invDetailType = Arrays.asList("用户预扣","大核库存","全空母机","上架缓冲","大核预留","上线","搬迁","改造","冗余库存","退役转出");
        List<String> materialType = Arrays.asList("好料","流转库存","冗余库存","周转库存");
        inventoryCondition.and("material_type in (?)", materialType);
        inventoryCondition.and("inv_detail_type in (?)", invDetailType);
        inventoryCondition.and("stat_time between ? and ?", startDate, endDate);
        List<DwsActualInventoryDfDO> inventoryAll = ckcldDBHelper.getAll(DwsActualInventoryDfDO.class,
                inventoryCondition.getSQL(), inventoryCondition.getParams());
        List<String> zoneList = new ArrayList<>();
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            List<String> category = bean.getZoneNamesByZoneCategory(zoneCategory,
                    DateUtils.formatDate(DateUtils.yesterday()));
            for (String zone : req.getZoneName()) {
                if (category.contains(zone)) {
                    zoneList.add(zone);
                }
            }
        }else {
            zoneList = bean.getZoneNamesByZoneCategory(zoneCategory, DateUtils.formatDate(DateUtils.yesterday()));
        }
        //zoneList通过境内外等筛选
        WhereSQL tempCondition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            tempCondition.and("customhouse_title in (?)", req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            tempCondition.and("region_name in (?)", req.getRegionName());
        }
        tempCondition.and("zone_name in (?)", zoneList);
        List<StaticZoneDO> zoneAll = cdCommonDbHelper.getAll(StaticZoneDO.class, tempCondition.getSQL(),
                tempCondition.getParams());
        List<String> finalZone = zoneAll.stream().map(StaticZoneDO::getZoneName).distinct().collect(Collectors.toList());
        List<String> instanceList = new ArrayList<>(bean.getInstanceTypesByInstanceCategory(instanceCategory,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle()));
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Map<String, List<String>> insMap = ListUtils.toMapList(allCvmType, CvmType::getInstanceType,
                CvmType::getDeviceType);
        List<String> deviceList = new ArrayList<>();
        for (String s : instanceList) {
            List<String> stringList = insMap.get(s);
            if (ListUtils.isNotEmpty(stringList)) {
                deviceList.addAll(stringList);
            }
        }
        String endToEndSql = ORMUtils.getSql(
                "/sql/operation_view/inventory_health/inventory_overview/end_to_end_total_scale_month.sql");
        List<EndToEndDTO> endTotalAll = planReportDBHelper.getRaw(EndToEndDTO.class, endToEndSql, startDate.replace("-", ""), endDate.replace("-", ""), zoneList,
                deviceList);
        List<CvmServiceLevel> levelAll = getCvmServiceLevelData(startDate, endDate, "忙时", zoneList, instanceList);
        Map<String, List<DwsActualInventoryDfDO>> inventoryMap = ListUtils.toMapList(inventoryAll,
                DwsActualInventoryDfDO::getZoneName, o -> o);
        if (ListUtils.isNotEmpty(endTotalAll)) {
            endTotalAll = endTotalAll.stream().filter(o -> finalZone.contains(o.getZoneName())).collect(Collectors.toList());
        }
        if (ListUtils.isNotEmpty(levelAll)) {
            levelAll = levelAll.stream().filter(o -> finalZone.contains(o.getZoneName())).collect(Collectors.toList());
        }
        Map<String, List<EndToEndDTO>> endMap = ListUtils.toMapList(endTotalAll, EndToEndDTO::getZoneName, o -> o);
        Map<String, List<CvmServiceLevel>> levelMap = ListUtils.toMapList(levelAll, CvmServiceLevel::getZoneName,
                o -> o);
        Set<String> zoneSet = new HashSet<>(zoneList);
        //获取当月的天数
        int count = 0;
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            count ++;
            start = start.plusDays(1);
        }
        List<String> saleType = Arrays.asList("内部领用规模", "外部计费规模", "弹性规模");
        List<String> items = Arrays.asList("CSIG领用", "其他BG领用", "云支撑区", "渲染领用", "CVM计费规模",
                "Lighthouse规模", "弹性好料", "弹性差料", "弹性呆料");
        for (String zone : zoneSet) {
            List<DwsActualInventoryDfDO> inventoryList = inventoryMap.get(zone);
            List<EndToEndDTO> endList = endMap.get(zone);
            List<CvmServiceLevel> levelList = levelMap.get(zone);
            if (ListUtils.isNotEmpty(inventoryList) || ListUtils.isNotEmpty(endList) || ListUtils.isNotEmpty(levelList)) {
                InventoryOverviewZoneData item = new InventoryOverviewZoneData();
                item.setProduct("CVM");
                item.setZoneName(zone);
                BigDecimal actualTotal = NumberUtils.sum(inventoryList, DwsActualInventoryDfDO::getActualInv);
                item.setActualInventory(actualTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
                if (ListUtils.isNotEmpty(endList)) {
                    List<EndToEndDTO> soldScale = endList.stream()
                            .filter(o -> o.getCategory().equals("售卖规模") && saleType.contains(o.getSaleType())
                                    && items.contains(o.getItem())).collect(
                                    Collectors.toList());
                    item.setSoldScale(NumberUtils.sum(soldScale, EndToEndDTO::getCoreNum));
                    item.setTotalScale(NumberUtils.sum(endList, EndToEndDTO::getCoreNum));
                }
                item.setApiTotal(NumberUtils.sum(levelList, CvmServiceLevel::getApiTotal));
                item.setApiSucTotal(NumberUtils.sum(levelList, CvmServiceLevel::getApiSucTotal));
                item.setSoldOutTotal(NumberUtils.sum(levelList, CvmServiceLevel::getSoldOutTotal));
                item.setSoldTotal(NumberUtils.sum(levelList, CvmServiceLevel::getSoldTotal));
                result.add(item);
            }
        }

        return result;

    }

    private List<InventoryOverviewZoneData> queryCDBInventoryOverviewReport(String startDate, String endDate,
            InventoryOverviewReq req) {
        List<InventoryOverviewZoneData> result = new ArrayList<>();
        WhereSQL condition = req.genBasicCondition();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        List<DwsCdbServiceLevelDataDO> all = ckcldDBHelper.getAll(DwsCdbServiceLevelDataDO.class,
                condition.getSQL(), condition.getParams());
        WhereSQL crsCondition = condition.copy();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            crsCondition.and("country in (?)", req.getCountry());
        }
        List<DwsCrsServiceLevelDataDO> crsAll = ckcldDBHelper.getAll(DwsCrsServiceLevelDataDO.class,
                crsCondition.getSQL(), crsCondition.getParams());
        List<DwsTdsqlServiceLevelDataDfDO> tdAll = ckcldDBHelper.getAll(DwsTdsqlServiceLevelDataDfDO.class,
                crsCondition.getSQL(), crsCondition.getParams());
        Map<String, List<DwsCdbServiceLevelDataDO>> mapList = ListUtils.toMapList(all,
                DwsCdbServiceLevelDataDO::getZoneName, o -> o);
        Map<String, List<DwsCrsServiceLevelDataDO>> crsMapList = ListUtils.toMapList(crsAll,
                DwsCrsServiceLevelDataDO::getZoneName,
                o -> o);
        Map<String, List<DwsTdsqlServiceLevelDataDfDO>> tdMapList = ListUtils.toMapList(tdAll,
                DwsTdsqlServiceLevelDataDfDO::getZoneName,
                o -> o);
        int count = 0;
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            count ++;
            start = start.plusDays(1);
        }
        for (Entry<String, List<DwsCdbServiceLevelDataDO>> entry : mapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsCdbServiceLevelDataDO> value = entry.getValue();
            item.setProduct("数据库-CDB");
            item.setZoneName(value.get(0).getZoneName());
            BigDecimal MemTotal = NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestMem);
            BigDecimal DiskTotal = NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestDisk);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setActualDisk(DiskTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getSucCnt));
            result.add(item);
        }
        for (Entry<String, List<DwsCrsServiceLevelDataDO>> entry : crsMapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsCrsServiceLevelDataDO> value = entry.getValue();
            item.setProduct("数据库-CRS");
            item.setZoneName(value.get(0).getZoneName());
            BigDecimal MemTotal = NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getRestMem);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getSucCnt));
            result.add(item);
        }

        for (Entry<String, List<DwsTdsqlServiceLevelDataDfDO>> entry : tdMapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsTdsqlServiceLevelDataDfDO> value = entry.getValue();
            item.setProduct("数据库-TDSQL-C");
            item.setZoneName(value.get(0).getZoneName());
            BigDecimal MemTotal = NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestMem);
            BigDecimal DiskTotal = NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestDisk);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setActualDisk(DiskTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSucCnt));
            item.setSoldTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldCnt));
            item.setSoldOutTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldOutCnt));
            result.add(item);
        }

        return result;

    }

    private List<InventoryOverviewZoneData> queryEKSInventoryOverviewReport(String startDate, String endDate,
            InventoryOverviewReq req) {
        List<InventoryOverviewZoneData> result = new ArrayList<>();
        WhereSQL condition = req.genBasicCondition();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        condition.and("product_type = ?", "EKS");
        List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        Map<String, List<DwsApiSuccessDataDfLocalDO>> mapList = ListUtils.toMapList(all,
                DwsApiSuccessDataDfLocalDO::getZoneName, o -> o);
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : mapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            item.setProduct("EKS");
            item.setZoneName(value.get(0).getZoneName());
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            result.add(item);
        }

        return result;

    }


    private List<InventoryOverviewZoneData> queryCBSInventoryOverviewReport(String startDate, String endDate, InventoryOverviewReq req) {

        List<InventoryOverviewZoneData> result = new ArrayList<>();
        WhereSQL condition = req.genBasicCondition();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        List<DwsCbsServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfDO.class,
                condition.getSQL(), condition.getParams());
        Map<String, List<DwsCbsServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                DwsCbsServiceLevelDataDfDO::getZoneName, o -> o);
        for (Entry<String, List<DwsCbsServiceLevelDataDfDO>> entry : mapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsCbsServiceLevelDataDfDO> value = entry.getValue();
            item.setProduct("CBS");
            item.setZoneName(value.get(0).getZoneName());
            item.setApiTotal(NumberUtils.sum(value, DwsCbsServiceLevelDataDfDO::getFailDiskSize).add(NumberUtils.sum(value,
                    DwsCbsServiceLevelDataDfDO::getSuccessDiskSize)));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCbsServiceLevelDataDfDO::getSuccessDiskSize));
            result.add(item);
        }
        return result;
    }

    private List<InventoryOverviewZoneData> queryBigDataInventoryOverviewReport(String startDate, String endDate,
            List<String> productType, InventoryOverviewReq req) {
        List<InventoryOverviewZoneData> result = new ArrayList<>();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        List<String> instanceCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL condition = req.genBasicCondition();
        List<String> zone = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        Set<String> instance = bean.getInstanceTypesByInstanceCategory(instanceCategory,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zone);
        categoryCondition.and("instance_family in (?)", instance);
        condition.and(categoryCondition);
        condition.and("product_type in (?)", productType);
        condition.and("stat_time between ? and ?",startDate, endDate);
        List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        Map<String, List<DwsApiSuccessDataDfLocalDO>> mapList = ListUtils.toMapList(all,
                DwsApiSuccessDataDfLocalDO::getZoneName, o -> o);
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : mapList.entrySet()) {
            InventoryOverviewZoneData item = new InventoryOverviewZoneData();
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            item.setProduct("大数据");
            item.setZoneName(value.get(0).getZoneName());
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            result.add(item);
        }

        return result;

    }




    private List<CvmServiceLevel> getCvmServiceLevelData(String startDate, String endDate,
            String type, List<String> zoneList, List<String> instanceList) {
        WhereSQL apiCondition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceList)) {
            apiCondition.and("instance_family in (?)", instanceList);
        }
        apiCondition.and("version between ? and ?", startDate.replace("-", ""), endDate.replace("-", ""));
        if (ListUtils.isNotEmpty(zoneList)) {
            apiCondition.and("zone_name in (?)", zoneList);
        }
        List<DwsCloudServerLevelDO> apiData = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, apiCondition.getSQL(),
                apiCondition.getParams());
        Map<String, List<DwsCloudServerLevelDO>> apiMap = ListUtils.toMapList(apiData,
                o -> String.join("@", o.getZoneName(), o.getInstanceFamily()), o -> o);
        List<CvmServiceLevel> result = new ArrayList<>();
        if (type.equals("全时段")) {
            for (Entry<String, List<DwsCloudServerLevelDO>> entry : apiMap.entrySet()) {
                CvmServiceLevel item = new CvmServiceLevel();
                List<DwsCloudServerLevelDO> value = entry.getValue();
                item.setZoneName(value.get(0).getZoneName());
                item.setInstanceType(value.get(0).getInstanceFamily());
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(value, DwsCloudServerLevelDO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(value, DwsCloudServerLevelDO::getApiSucTotal);
                BigDecimal soldTotal = value.get(0).getSoldTotal() == null ?
                        BigDecimal.ZERO : value.get(0).getSoldTotal();
                BigDecimal soldOut = value.get(0).getSoldOutTotal() == null ?
                        BigDecimal.ZERO : value.get(0).getSoldOutTotal();
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldOutTotal(soldOut);
                item.setSoldTotal(soldTotal);
                item.setServiceLevel(slap);
                result.add(item);
            }
        }else {
            WhereSQL condition = new WhereSQL();
            if (ListUtils.isNotEmpty(instanceList)) {
                condition.and("instance_type in (?)", instanceList);
            }
            condition.and("imp_date between ? and ?", startDate.replace("-", ""), endDate.replace("-", ""));
            if (ListUtils.isNotEmpty(zoneList)) {
                condition.and("zone_name in (?)", zoneList);
            }
            List<DwsLeisureAndBusySoldOutDataDfDO> soldData = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class,
                    condition.getSQL(),
                    condition.getParams());
            Map<String, List<DwsLeisureAndBusySoldOutDataDfDO>> soldList = ListUtils.toMapList(soldData,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
            Set<String> totalKeys = new HashSet<>();
            if (ListUtils.isNotEmpty(apiMap.keySet())) {
                totalKeys.addAll(apiMap.keySet());
            }
            if (ListUtils.isNotEmpty(soldList.keySet())) {
                totalKeys.addAll(soldList.keySet());
            }
            for (String key : totalKeys) {
                List<DwsCloudServerLevelDO> api = apiMap.get(key);
                List<DwsLeisureAndBusySoldOutDataDfDO> sold = soldList.get(key);
                CvmServiceLevel item = new CvmServiceLevel();
                if (ListUtils.isNotEmpty(api)) {
                    item.setInstanceType(api.get(0).getInstanceFamily());
                    item.setZoneName(api.get(0).getZoneName());
                }else if (ListUtils.isNotEmpty(sold)) {
                    item.setInstanceType(sold.get(0).getInstanceType());
                    item.setZoneName(sold.get(0).getZoneName());
                }
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(api, DwsCloudServerLevelDO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(api, DwsCloudServerLevelDO::getApiSucTotal);
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                if (type.equals("闲时")) {
                    soldOut = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getLeisureSoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getLeisureSoldTotal);
                }else if (type.equals("忙时")) {
                    soldOut = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getBusySoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getBusySoldTotal);
                }
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldTotal(soldTotal);
                item.setSoldOutTotal(soldOut);
                result.add(item);
            }
        }
        return result;
    }

    @Override
    public List<InventoryHealthOverviewRateConfigDO> getAllInventoryHealthConfig() {
        return demandDBHelper.getAll(InventoryHealthOverviewRateConfigDO.class);
    }

    @Override
    public List<InventoryHealthOverviewZoneConfigDO> getAllInventoryHealthZoneConfig() {

        List<InventoryHealthOverviewZoneConfigDO> all = demandDBHelper.getAll(
                InventoryHealthOverviewZoneConfigDO.class);
        all.sort(Comparator.comparing(InventoryHealthOverviewZoneConfigDO::getSortNum));
        return all;
    }


    @Data
    public static class CvmServiceLevel {

        private String zoneName;

        private String instanceType;

        private BigDecimal apiTotal;

        private BigDecimal apiSucTotal;

        private BigDecimal soldTotal;

        private BigDecimal soldOutTotal;

        private BigDecimal serviceLevel;
    }

    @Data
    public static class CvmServiceLevelTrend {

        private String statTime;

        private String zoneName;

        private String instanceType;

        private BigDecimal apiTotal;

        private BigDecimal apiSucTotal;

        private BigDecimal soldTotal;

        private BigDecimal soldOutTotal;

        private BigDecimal serviceLevel;
    }


    @Data
    public static class EndToEndDTO {

        @Column("zone_name")
        private String zoneName;

        @Column("device_type")
        private String deviceType;

        @Column("category")
        private String category;

        @Column("sale_type")
        private String saleType;

        @Column("item")
        private String item;

        @Column("core_num")
        private BigDecimal coreNum;

    }
    @Data
    public static class EndToEndTrendDTO {

        @Column("data_date")
        private String dataDate;

        @Column("zone_name")
        private String zoneName;

        @Column("device_type")
        private String deviceType;

        @Column("category")
        private String category;

        @Column("sale_type")
        private String saleType;

        @Column("item")
        private String item;

        @Column("core_num")
        private BigDecimal coreNum;

    }

    public Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getDayMap(String startDate, String endDate) {
        Map<String, String> result = new HashMap<>();
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            result.put(DateUtils.formatDate(start), DateUtils.formatDate(start));
            start = start.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    private List<InventoryOverviewTrendData> queryCvmInventoryOverviewTrendReport(String startDate, String endDate,
            InventoryOverviewTrendReq req, Map<String, String> dateMap, Set<String> region) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        //1.获取cvm端到端、服务水平、库存数据
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        List<String> instanceCategory = Collections.singletonList("PRINCIPAL");
        WhereSQL inventoryCondition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCondition = bean.genCategoryCondition(zoneCategory, instanceCategory, false,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        inventoryCondition.and(categoryCondition);
        List<String> invDetailType = Arrays.asList("用户预扣","大核库存","全空母机","上架缓冲","大核预留","上线","搬迁","改造","冗余库存","退役转出");
        List<String> materialType = Arrays.asList("好料","流转库存","冗余库存","周转库存");
        inventoryCondition.and("material_type in (?)", materialType);
        inventoryCondition.and("inv_detail_type in (?)", invDetailType);
        inventoryCondition.and("stat_time between ? and ?", startDate, endDate);
        inventoryCondition.and("region_name in (?)", new ArrayList<>(region));
        inventoryCondition.addGroupBy("stat_time, zone_name");
        List<DwsActualInventoryDfAnyDO> inventoryAll = ckcldDBHelper.getAll(DwsActualInventoryDfAnyDO.class,
                inventoryCondition.getSQL(), inventoryCondition.getParams());
        List<String> zoneList = new ArrayList<>();
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            List<String> category = bean.getZoneNamesByZoneCategory(zoneCategory,
                    DateUtils.formatDate(DateUtils.yesterday()));
            for (String zone : req.getZoneName()) {
                if (category.contains(zone)) {
                    zoneList.add(zone);
                }
            }
        }else {
            zoneList = bean.getZoneNamesByZoneCategory(zoneCategory, DateUtils.formatDate(DateUtils.yesterday()));
        }
        //zoneList通过境内外等筛选
        WhereSQL tempCondition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            tempCondition.and("customhouse_title in (?)", req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            tempCondition.and("region_name in (?)", req.getRegionName());
        }
        tempCondition.and("region_name in (?)", new ArrayList<>(region));
        tempCondition.and("zone_name in (?)", zoneList);
        List<StaticZoneDO> zoneAll = cdCommonDbHelper.getAll(StaticZoneDO.class, tempCondition.getSQL(),
                tempCondition.getParams());
        List<String> finalZone = zoneAll.stream().map(StaticZoneDO::getZoneName).distinct().collect(Collectors.toList());
        List<String> instanceList = new ArrayList<>(bean.getInstanceTypesByInstanceCategory(instanceCategory,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle()));
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Map<String, List<String>> insMap = ListUtils.toMapList(allCvmType, CvmType::getInstanceType,
                CvmType::getDeviceType);
        List<String> deviceList = new ArrayList<>();
        for (String s : instanceList) {
            List<String> stringList = insMap.get(s);
            if (ListUtils.isNotEmpty(stringList)) {
                deviceList.addAll(stringList);
            }
        }
        List<EndToEndTrendDTO> endTotalAll = new ArrayList<>();
        if (req.getTimeDimension().equals("day") || req.getTimeDimension().equals("week")) {
            String endToEndSql = ORMUtils.getSql(
                    "/sql/operation_view/inventory_health/inventory_overview/end_to_end_total_scale_trend.sql");
            endTotalAll = planReportDBHelper.getRaw(EndToEndTrendDTO.class, endToEndSql, startDate.replace("-", ""), endDate.replace("-", ""), zoneList,
                    deviceList);
        }else {
            WhereSQL condition = req.genBasicCondition();
            condition.and(categoryCondition);
            List<String> month = dateMap.values().stream().distinct().collect(Collectors.toList());
            month.sort(String::compareTo);
            condition.and("stat_time between ? and ?", month.get(0), month.get(month.size() - 1));
            List<DwsEndToEndZoneDeviceModelMonthDataDO> all = ckcldDBHelper.getAll(
                    DwsEndToEndZoneDeviceModelMonthDataDO.class, condition.getSQL(), condition.getParams());
            endTotalAll = DwsEndToEndZoneDeviceModelMonthDataDO.genEndToEnd(all);
        }
        List<CvmServiceLevelTrend> levelAll = getCvmServiceLevelTrendData(startDate, endDate, "忙时", zoneList, instanceList, dateMap);
        if (ListUtils.isNotEmpty(endTotalAll)) {
            endTotalAll = endTotalAll.stream().filter(o -> finalZone.contains(o.getZoneName())).collect(Collectors.toList());
        }
        if (ListUtils.isNotEmpty(levelAll)) {
            levelAll = levelAll.stream().filter(o -> finalZone.contains(o.getZoneName())).collect(Collectors.toList());
        }
        if (ListUtils.isNotEmpty(req.getCountry())) {
            if (ListUtils.isNotEmpty(inventoryAll)) {
                inventoryAll = inventoryAll.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                        Collectors.toList());
            }
            if (ListUtils.isNotEmpty(endTotalAll)) {
                endTotalAll = endTotalAll.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                        Collectors.toList());
            }
            if (ListUtils.isNotEmpty(levelAll)) {
                levelAll = levelAll.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                        Collectors.toList());
            }
        }
        Map<String, List<DwsActualInventoryDfAnyDO>> inventoryMap = ListUtils.toMapList(inventoryAll,
                o -> dateMap.get(o.getStatTime()), o -> o);
        Map<String, List<EndToEndTrendDTO>> endMap;
        if (req.getTimeDimension().equals("month")) {
            endMap = ListUtils.toMapList(endTotalAll, EndToEndTrendDTO::getDataDate, o -> o);
        }else if (req.getTimeDimension().equals("day")) {
            endMap = ListUtils.toMapList(endTotalAll, o -> DateUtils.formatDate(DateUtils.parse(o.getDataDate(), "yyyyMMdd")), o -> o);
        }else {
            endMap = ListUtils.toMapList(endTotalAll, o -> dateMap.get(DateUtils.formatDate(DateUtils.parse(o.getDataDate(), "yyyyMMdd"))), o -> o);
        }
        Map<String, List<CvmServiceLevelTrend>> levelMap = ListUtils.toMapList(levelAll,
                CvmServiceLevelTrend::getStatTime, o -> o);
        Set<String> dates = new HashSet<>(dateMap.values());
        for (String date : dates) {
            List<DwsActualInventoryDfAnyDO> inventoryList = inventoryMap.get(date);
            List<EndToEndTrendDTO> endList = endMap.get(date);
            List<CvmServiceLevelTrend> levelList = levelMap.get(date);
            if (ListUtils.isEmpty(inventoryList) && ListUtils.isEmpty(endList) && ListUtils.isEmpty(levelList)) {
                continue;
            }
            int count = 0;
            if (req.getTimeDimension().equals("day")) {
                count = 1;
            }else if (req.getTimeDimension().equals("month")) {
                YearMonth ym = YearMonth.parse(date);
                LocalDate start = ym.atDay(1);
                LocalDate end = ym.atEndOfMonth().isAfter(DateUtils.yesterday()) ? DateUtils.yesterday() : ym.atEndOfMonth();
                while(!start.isAfter(end)) {
                    count ++;
                    start = start.plusDays(1);
                }
            }else {
                for (Entry<String, String> entry : dateMap.entrySet()) {
                    LocalDate localDate = DateUtils.parseLocalDate(entry.getKey());
                    if (entry.getValue().equals(date) && !localDate.isAfter(DateUtils.yesterday())) {
                        count++;
                    }
                }
            }
            List<String> saleType = Arrays.asList("内部领用规模", "外部计费规模", "弹性规模");
            List<String> items = Arrays.asList("CSIG领用", "其他BG领用", "云支撑区", "渲染领用", "CVM计费规模",
                    "Lighthouse规模", "弹性好料", "弹性差料", "弹性呆料");
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            List<String> zone = new ArrayList<>();
            if (ListUtils.isNotEmpty(inventoryList)) {
                zone.addAll(inventoryList.stream().map(DwsActualInventoryDfAnyDO::getZoneName).collect(Collectors.toList()));
            }
            if (ListUtils.isNotEmpty(endList)) {
                zone.addAll(endList.stream().map(EndToEndTrendDTO::getZoneName).collect(Collectors.toList()));
            }
            if (ListUtils.isNotEmpty(levelList)) {
                zone.addAll(levelList.stream().map(CvmServiceLevelTrend::getZoneName).collect(Collectors.toList()));
            }
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> tempRegion = zone.stream().map(zoneName2RegionName::get).distinct().collect(Collectors.toList());
            item.setStatTime(date);
            item.setProduct("CVM");
            item.setRegionName(tempRegion);
            BigDecimal actualTotal = NumberUtils.sum(inventoryList, DwsActualInventoryDfAnyDO::getSumActualInv);
            item.setActualInventory(actualTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            if (ListUtils.isNotEmpty(endList)) {
                List<EndToEndTrendDTO> soldScale = endList.stream()
                        .filter(o -> o.getCategory().equals("售卖规模") && saleType.contains(o.getSaleType())
                                && items.contains(o.getItem())).collect(
                                Collectors.toList());
                item.setSoldScale(NumberUtils.sum(soldScale, EndToEndTrendDTO::getCoreNum));
                item.setTotalScale(NumberUtils.sum(endList, EndToEndTrendDTO::getCoreNum));
            }
            item.setApiTotal(NumberUtils.sum(levelList, CvmServiceLevelTrend::getApiTotal));
            item.setApiSucTotal(NumberUtils.sum(levelList, CvmServiceLevelTrend::getApiSucTotal));
            item.setSoldOutTotal(NumberUtils.sum(levelList, CvmServiceLevelTrend::getSoldOutTotal));
            item.setSoldTotal(NumberUtils.sum(levelList, CvmServiceLevelTrend::getSoldTotal));
            result.add(item);
        }
        return result;
    }

    private List<CvmServiceLevelTrend> getCvmServiceLevelTrendData(String startDate, String endDate,
            String type, List<String> zoneList, List<String> instanceList, Map<String, String> dateMap) {
        WhereSQL apiCondition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceList)) {
            apiCondition.and("instance_family in (?)", instanceList);
        }
        apiCondition.and("version between ? and ?", startDate.replace("-", ""), endDate.replace("-", ""));
        if (ListUtils.isNotEmpty(zoneList)) {
            apiCondition.and("zone_name in (?)", zoneList);
        }
        List<DwsCloudServerLevelDO> apiData = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, apiCondition.getSQL(),
                apiCondition.getParams());
        Map<String, List<DwsCloudServerLevelDO>> apiMap = ListUtils.toMapList(apiData,
                o -> String.join("@", o.getZoneName(), o.getInstanceFamily(),
                        dateMap.get(DateUtils.formatDate(DateUtils.parse(o.getVersion(), "yyyyMMdd")))), o -> o);
        List<CvmServiceLevelTrend> result = new ArrayList<>();
        if (type.equals("全时段")) {
            for (Entry<String, List<DwsCloudServerLevelDO>> entry : apiMap.entrySet()) {
                CvmServiceLevelTrend item = new CvmServiceLevelTrend();
                List<DwsCloudServerLevelDO> value = entry.getValue();
                item.setStatTime(
                        dateMap.get(DateUtils.formatDate(DateUtils.parse(value.get(0).getVersion(), "yyyyMMdd"))));
                item.setZoneName(value.get(0).getZoneName());
                item.setInstanceType(value.get(0).getInstanceFamily());
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(value, DwsCloudServerLevelDO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(value, DwsCloudServerLevelDO::getApiSucTotal);
                BigDecimal soldTotal = value.get(0).getSoldTotal() == null ?
                        BigDecimal.ZERO : value.get(0).getSoldTotal();
                BigDecimal soldOut = value.get(0).getSoldOutTotal() == null ?
                        BigDecimal.ZERO : value.get(0).getSoldOutTotal();
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldOutTotal(soldOut);
                item.setSoldTotal(soldTotal);
                item.setServiceLevel(slap);
                result.add(item);
            }
        }else {
            WhereSQL condition = new WhereSQL();
            if (ListUtils.isNotEmpty(instanceList)) {
                condition.and("instance_type in (?)", instanceList);
            }
            condition.and("imp_date between ? and ?", startDate.replace("-", ""), endDate.replace("-", ""));
            if (ListUtils.isNotEmpty(zoneList)) {
                condition.and("zone_name in (?)", zoneList);
            }
            List<DwsLeisureAndBusySoldOutDataDfDO> soldData = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class,
                    condition.getSQL(),
                    condition.getParams());
            Map<String, List<DwsLeisureAndBusySoldOutDataDfDO>> soldList = ListUtils.toMapList(soldData,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType(), dateMap.get(DateUtils.formatDate(DateUtils.parse(o.getImpDate(), "yyyyMMdd")))), o -> o);
            Set<String> totalKeys = new HashSet<>();
            if (ListUtils.isNotEmpty(apiMap.keySet())) {
                totalKeys.addAll(apiMap.keySet());
            }
            if (ListUtils.isNotEmpty(soldList.keySet())) {
                totalKeys.addAll(soldList.keySet());
            }
            for (String key : totalKeys) {
                List<DwsCloudServerLevelDO> api = apiMap.get(key);
                List<DwsLeisureAndBusySoldOutDataDfDO> sold = soldList.get(key);
                CvmServiceLevelTrend item = new CvmServiceLevelTrend();
                if (ListUtils.isNotEmpty(api)) {
                    item.setStatTime(dateMap.get(DateUtils.formatDate(DateUtils.parse(api.get(0).getVersion(), "yyyyMMdd"))));
                    item.setInstanceType(api.get(0).getInstanceFamily());
                    item.setZoneName(api.get(0).getZoneName());
                }else if (ListUtils.isNotEmpty(sold)) {
                    item.setStatTime(dateMap.get(DateUtils.formatDate(DateUtils.parse(sold.get(0).getImpDate(), "yyyyMMdd"))));
                    item.setInstanceType(sold.get(0).getInstanceType());
                    item.setZoneName(sold.get(0).getZoneName());
                }
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(api, DwsCloudServerLevelDO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(api, DwsCloudServerLevelDO::getApiSucTotal);
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                if (type.equals("闲时")) {
                    soldOut = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getLeisureSoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getLeisureSoldTotal);
                }else if (type.equals("忙时")) {
                    soldOut = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getBusySoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, DwsLeisureAndBusySoldOutDataDfDO::getBusySoldTotal);
                }
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldTotal(soldTotal);
                item.setSoldOutTotal(soldOut);
                result.add(item);
            }
        }
        return result;
    }

    private List<InventoryOverviewTrendData> queryCDBInventoryOverviewTrendReport(String startDate, String endDate,
            InventoryOverviewTrendReq req, Map<String, String> dateMap, Set<String> region) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        WhereSQL condition = req.genBasicCondition();
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        condition.and("region_name in (?)", new ArrayList<>(region));
        WhereSQL crsCondition = condition.copy();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            crsCondition.and("country in (?)", req.getCountry());
        }
        List<DwsCdbServiceLevelDataDO> all = ckcldDBHelper.getAll(DwsCdbServiceLevelDataDO.class,
                condition.getSQL(), condition.getParams());
        List<DwsCrsServiceLevelDataDO> crsAll = ckcldDBHelper.getAll(DwsCrsServiceLevelDataDO.class,
                crsCondition.getSQL(), crsCondition.getParams());
        List<DwsTdsqlServiceLevelDataDfDO> tdAll = ckcldDBHelper.getAll(DwsTdsqlServiceLevelDataDfDO.class,
                crsCondition.getSQL(), crsCondition.getParams());
        if (ListUtils.isNotEmpty(req.getCountry())) {
            all = all.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                    Collectors.toList());
        }
        Map<String, List<DwsCdbServiceLevelDataDO>> mapList = ListUtils.toMapList(all,
                o -> dateMap.get(o.getStatTime()), o -> o);
        Map<String, List<DwsCrsServiceLevelDataDO>> crsMapList = ListUtils.toMapList(crsAll,
                o -> dateMap.get(o.getStatTime()), o -> o);
        Map<String, List<DwsTdsqlServiceLevelDataDfDO>> tdMapList = ListUtils.toMapList(tdAll,
                o -> dateMap.get(o.getStatTime()), o -> o);
        for (Entry<String, List<DwsCdbServiceLevelDataDO>> entry : mapList.entrySet()) {
            int count = 0;
            if (req.getTimeDimension().equals("day")) {
                count = 1;
            }else if (req.getTimeDimension().equals("month")) {
                YearMonth ym = YearMonth.parse(entry.getKey());
                LocalDate start = ym.atDay(1);
                LocalDate end = ym.atEndOfMonth().isAfter(DateUtils.yesterday()) ? DateUtils.yesterday() : ym.atEndOfMonth();
                while(!start.isAfter(end)) {
                    count ++;
                    start = start.plusDays(1);
                }
            }else {
                for (Entry<String, String> temp : dateMap.entrySet()) {
                    LocalDate localDate = DateUtils.parseLocalDate(temp.getKey());
                    if (temp.getValue().equals(entry.getKey()) && !localDate.isAfter(DateUtils.yesterday())) {
                        count++;
                    }
                }
            }
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            item.setStatTime(entry.getKey());
            List<DwsCdbServiceLevelDataDO> value = entry.getValue();
            item.setProduct("数据库-CDB");
            List<String> zone = value.stream().map(DwsCdbServiceLevelDataDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zone.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            BigDecimal MemTotal = NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestMem);
            BigDecimal DiskTotal = NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestDisk);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setActualDisk(DiskTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getSucCnt));
            result.add(item);
        }
        for (Entry<String, List<DwsCrsServiceLevelDataDO>> entry : crsMapList.entrySet()) {
            int count = 0;
            if (req.getTimeDimension().equals("day")) {
                count = 1;
            }else if (req.getTimeDimension().equals("month")) {
                YearMonth ym = YearMonth.parse(entry.getKey());
                LocalDate start = ym.atDay(1);
                LocalDate end = ym.atEndOfMonth().isAfter(DateUtils.yesterday()) ? DateUtils.yesterday() : ym.atEndOfMonth();
                while(!start.isAfter(end)) {
                    count ++;
                    start = start.plusDays(1);
                }
            }else {
                for (Entry<String, String> temp : dateMap.entrySet()) {
                    LocalDate localDate = DateUtils.parseLocalDate(temp.getKey());
                    if (temp.getValue().equals(entry.getKey()) && !localDate.isAfter(DateUtils.yesterday())) {
                        count++;
                    }
                }
            }
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            item.setStatTime(entry.getKey());
            List<DwsCrsServiceLevelDataDO> value = entry.getValue();
            item.setProduct("数据库-CRS");
            List<String> zone = value.stream().map(DwsCrsServiceLevelDataDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zone.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            BigDecimal MemTotal = NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getRestMem);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getSucCnt));
            result.add(item);
        }
        for (Entry<String, List<DwsTdsqlServiceLevelDataDfDO>> entry : tdMapList.entrySet()) {
            int count = 0;
            if (req.getTimeDimension().equals("day")) {
                count = 1;
            }else if (req.getTimeDimension().equals("month")) {
                YearMonth ym = YearMonth.parse(entry.getKey());
                LocalDate start = ym.atDay(1);
                LocalDate end = ym.atEndOfMonth().isAfter(DateUtils.yesterday()) ? DateUtils.yesterday() : ym.atEndOfMonth();
                while(!start.isAfter(end)) {
                    count ++;
                    start = start.plusDays(1);
                }
            }else {
                for (Entry<String, String> temp : dateMap.entrySet()) {
                    LocalDate localDate = DateUtils.parseLocalDate(temp.getKey());
                    if (temp.getValue().equals(entry.getKey()) && !localDate.isAfter(DateUtils.yesterday())) {
                        count++;
                    }
                }
            }
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            item.setStatTime(entry.getKey());
            List<DwsTdsqlServiceLevelDataDfDO> value = entry.getValue();
            item.setProduct("数据库-TDSQL-C");
            List<String> zone = value.stream().map(DwsTdsqlServiceLevelDataDfDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zone.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            BigDecimal MemTotal = NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestMem);
            BigDecimal DiskTotal = NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestDisk);
            item.setActualMem(MemTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setActualDisk(DiskTotal.divide(BigDecimal.valueOf(count), RoundingMode.HALF_UP));
            item.setApiTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getTotalCnt));
            item.setApiSucTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSucCnt));
            item.setSoldTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldCnt));
            item.setSoldOutTotal(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldOutCnt));
            result.add(item);
        }
        return result;

    }

    private List<InventoryOverviewTrendData> queryEKSInventoryOverviewTrendReport(String startDate, String endDate,
            InventoryOverviewTrendReq req, Map<String, String> dateMap, Set<String> region) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        WhereSQL condition = req.genBasicCondition();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        condition.and("product_type = ?", "EKS");
        condition.and("region_name in (?)", new ArrayList<>(region));
        List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getCountry())) {
            all = all.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                    Collectors.toList());
        }
        Map<String, List<DwsApiSuccessDataDfLocalDO>> mapList = ListUtils.toMapList(all,
                o -> dateMap.get(o.getStatTime()), o -> o);
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : mapList.entrySet()) {
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            item.setStatTime(entry.getKey());
            item.setProduct("EKS");
            List<String> zone = value.stream().map(DwsApiSuccessDataDfLocalDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zone.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            result.add(item);
        }

        return result;

    }

    private List<InventoryOverviewTrendData> queryCBSInventoryOverviewTrendReport(String startDate, String endDate, InventoryOverviewTrendReq req, Map<String, String> dateMap, Set<String> region) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        WhereSQL condition = req.genBasicCondition();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            condition.and("country in (?)", req.getCountry());
        }
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        List<String> zoneList = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zoneList);
        condition.and(categoryCondition);
        condition.and("stat_time between ? and ?", startDate, endDate);
        condition.and("region_name in (?)", new ArrayList<>(region));
        condition.addGroupBy("stat_time, zone_name");
        List<DwsCbsServiceLevelDataDfAnyDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfAnyDO.class,
                condition.getSQL(), condition.getParams());
        Map<String, List<DwsCbsServiceLevelDataDfAnyDO>> mapList = ListUtils.toMapList(all,
                o -> dateMap.get(o.getStatTime()), o -> o);
        for (Entry<String, List<DwsCbsServiceLevelDataDfAnyDO>> entry : mapList.entrySet()) {
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            List<DwsCbsServiceLevelDataDfAnyDO> value = entry.getValue();
            item.setStatTime(entry.getKey());
            item.setProduct("CBS");
            List<String> zone = value.stream().map(DwsCbsServiceLevelDataDfAnyDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zone.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            item.setApiTotal(NumberUtils.sum(value, DwsCbsServiceLevelDataDfAnyDO::getApiSuc).add(NumberUtils.sum(value, DwsCbsServiceLevelDataDfAnyDO::getApiFail)));
            item.setApiSucTotal(NumberUtils.sum(value, DwsCbsServiceLevelDataDfAnyDO::getApiSuc));
            result.add(item);
        }
        return result;
    }



    private List<InventoryOverviewTrendData> queryBigDataInventoryOverviewTrendReport(String startDate, String endDate,
            List<String> productType, InventoryOverviewTrendReq req, Map<String, String> dateMap, Set<String> region) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        List<String> zoneCategory = Collections.singletonList("PRINCIPAL");
        List<String> instanceCategory = Collections.singletonList("PRINCIPAL");
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL condition = req.genBasicCondition();
        List<String> zone = bean.getZoneNamesByZoneCategory(zoneCategory,
                DateUtils.formatDate(DateUtils.yesterday()));
        Set<String> instance = bean.getInstanceTypesByInstanceCategory(instanceCategory,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        WhereSQL categoryCondition = new WhereSQL();
        categoryCondition.and("zone_name in (?)", zone);
        categoryCondition.and("instance_family in (?)", instance);
        condition.and(categoryCondition);
        condition.and("product_type in (?)", productType);
        condition.and("stat_time between ? and ?",startDate, endDate);
        condition.and("region_name in (?)", new ArrayList<>(region));
        List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getCountry())) {
            all = all.stream().filter(o -> req.getCountry().contains(zoneToCountryMap.get(o.getZoneName()))).collect(
                    Collectors.toList());
        }
        Map<String, List<DwsApiSuccessDataDfLocalDO>> mapList = ListUtils.toMapList(all,
                o -> dateMap.get(o.getStatTime()), o -> o);
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : mapList.entrySet()) {
            InventoryOverviewTrendData item = new InventoryOverviewTrendData();
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            item.setStatTime(entry.getKey());
            item.setProduct("大数据");
            List<String> zoneList = value.stream().map(DwsApiSuccessDataDfLocalDO::getZoneName).distinct()
                    .collect(Collectors.toList());
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            List<String> regionList = zoneList.stream().map(zoneName2RegionName::get).collect(Collectors.toList());
            item.setRegionName(regionList);
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            result.add(item);
        }

        return result;

    }

    private List<InventoryOverviewTrendData> queryNetworkInventoryOverviewTrendReport(String startDate, String endDate,
            InventoryOverviewTrendReq req, Map<String, String> dateMap, Map<String, List<String>> regionMap) {
        List<InventoryOverviewTrendData> result = new ArrayList<>();
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            condition.and("country_name in (?)", req.getCountry());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            condition.and("region_name in (?)", req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            condition.and("customhouse_title in (?)", req.getCustomhouseTitle());
        }
        condition.and("stat_time between ? and ?", startDate, endDate);
        List<ClsLogServiceLevelDiDO> all = ckstdcrpDBHelper.getAll(ClsLogServiceLevelDiDO.class, condition.getSQL(),
                condition.getParams());
        Map<String, List<ClsLogServiceLevelDiDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", dateMap.get(DateUtils.formatDate(o.getStatTime())), o.getNetworkProduct()), o -> o);
        for (Entry<String, List<ClsLogServiceLevelDiDO>> entry : mapList.entrySet()) {
            List<ClsLogServiceLevelDiDO> value = entry.getValue();
            List<String> region = regionMap.get(dateMap.get(DateUtils.formatDate(value.get(0).getStatTime())));
            value = value.stream().filter(o -> region.contains(o.getRegionName())).collect(Collectors.toList());
            InventoryOverviewTrendData data = new InventoryOverviewTrendData();
            data.setStatTime(dateMap.get(DateUtils.formatDate(value.get(0).getStatTime())));
            data.setProduct("网络-" + value.get(0).getNetworkProduct());
            BigDecimal internalFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInternalFailed);
            BigDecimal insufficientFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInsufficientResourceFailed);
            BigDecimal total = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getTotal);
            data.setApiTotal(total);
            data.setApiSucTotal(total.subtract(insufficientFailed).subtract(internalFailed));
            data.setRegionName(value.stream().map(ClsLogServiceLevelDiDO::getRegionName).distinct().collect(Collectors.toList()));
            result.add(data);
        }
        Map<String, List<ClsLogServiceLevelDiDO>> allMap = ListUtils.toMapList(all,
                o -> dateMap.get(DateUtils.formatDate(o.getStatTime())), o -> o);
        for (Entry<String, List<ClsLogServiceLevelDiDO>> entry : allMap.entrySet()) {
            List<ClsLogServiceLevelDiDO> value = entry.getValue();
            List<String> region = regionMap.get(dateMap.get(DateUtils.formatDate(value.get(0).getStatTime())));
            value = value.stream().filter(o -> region.contains(o.getRegionName())).collect(Collectors.toList());
            InventoryOverviewTrendData data = new InventoryOverviewTrendData();
            data.setStatTime(dateMap.get(DateUtils.formatDate(value.get(0).getStatTime())));
            data.setProduct("网络-ALL");
            BigDecimal internalFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInternalFailed);
            BigDecimal insufficientFailed = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInsufficientResourceFailed);
            BigDecimal total = NumberUtils.sum(value, ClsLogServiceLevelDiDO::getTotal);
            data.setApiTotal(total);
            data.setApiSucTotal(total.subtract(insufficientFailed).subtract(internalFailed));
            data.setRegionName(value.stream().map(ClsLogServiceLevelDiDO::getRegionName).distinct().collect(Collectors.toList()));
            result.add(data);
        }
        return result;
    }


    @Table("dws_actual_inventory_df")
    @Data
    public static class DwsActualInventoryDfAnyDO{
        @Column("stat_time")
        private String statTime;

        @Column("zone_name")
        private String zoneName;

        @Column(value = "sum_actual_inv",computed = "sum(actual_inv)")
        private BigDecimal sumActualInv;
    }


    @Table("dws_cbs_service_level_data_df")
    @Data
    public static class DwsCbsServiceLevelDataDfAnyDO {
        @Column("stat_time")
        private String statTime;

        @Column("zone_name")
        private String zoneName;

        @Column(value = "api_suc", computed = "sum(success_disk_size)")
        private BigDecimal apiSuc;

        @Column(value = "api_fail", computed = "sum(fail_disk_size)")
        private BigDecimal apiFail;
    }
}
