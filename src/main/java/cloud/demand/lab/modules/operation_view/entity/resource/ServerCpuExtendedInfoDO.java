package cloud.demand.lab.modules.operation_view.entity.resource;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 服务器CPU扩展表
 */
@Data
@ToString
@Table("server_cpu_extended_info")
public class ServerCpuExtendedInfoDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isAutoIncrement = true)
    private Integer id;

    /** 处理器型号缩写<br/>Column: [cpu_abbr] */
    @Column(value = "cpu_abbr")
    private String cpuAbbr;

    /** 处理器厂家<br/>Column: [cpu_vender] */
    @Column(value = "cpu_vender")
    private String cpuVender;

    /** 处理器架构<br/>Column: [cpu_architecture] */
    @Column(value = "cpu_architecture")
    private String cpuArchitecture;

    /** 处理器平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform")
    private String cpuPlatform;

    /** 处理器系列<br/>Column: [cpu_series] */
    @Column(value = "cpu_series")
    private String cpuSeries;

    /** 处理器制程<br/>Column: [cpu_process] */
    @Column(value = "cpu_process")
    private String cpuProcess;

    /** 基频<br/>Column: [cpu_frequency] */
    @Column(value = "cpu_frequency")
    private String cpuFrequency;

    /** 处理器功率<br/>Column: [cpu_power] */
    @Column(value = "cpu_power")
    private String cpuPower;

    /** 单处理器核心数量<br/>Column: [cpu_core] */
    @Column(value = "cpu_core")
    private Integer cpuCore;

    /** 最大内存通道数<br/>Column: [max_memory_channel] */
    @Column(value = "max_memory_channel")
    private Integer maxMemoryChannel;

    /** 线程倍数<br/>Column: [ht_num] */
    @Column(value = "ht_num")
    private Integer htNum;

}
