package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ClsLogServiceLevelDiDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCbsServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.NetworkHealthActualService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class NetworkHealthActualServiceImpl implements NetworkHealthActualService {

    @Resource
    DBHelper ckstdcrpDBHelper;

    @Override
    public List<NetworkHealthActualTrendData> queryNetworkHealthActualTrendReport(NetworkHealthActualTrendReq req) {
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }
        WhereSQL condition = req.genBasicCondition();
        //获取网络服务水平数据
        List<ClsLogServiceLevelDiDO> all = ckstdcrpDBHelper.getAll(ClsLogServiceLevelDiDO.class, condition.getSQL(),
                condition.getParams());
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<ClsLogServiceLevelDiDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(DateUtils.formatDate(o.getStatTime())), o.getRegionName()), o -> o);
        List<NetworkHealthActualTrendData> result = new ArrayList<>();
        for (Entry<String, List<ClsLogServiceLevelDiDO>> entry : mapList.entrySet()) {
            List<ClsLogServiceLevelDiDO> value = entry.getValue();
            NetworkHealthActualTrendData data = new NetworkHealthActualTrendData();
            data.setStatTime(dateMap.get(DateUtils.formatDate(value.get(0).getStatTime())));
            data.setAreaName(value.get(0).getAreaName());
            data.setRegionName(value.get(0).getRegionName());
            data.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            data.setInsufficientFailedNum(NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInsufficientResourceFailed));
            data.setInternalFailedNum(NumberUtils.sum(value, ClsLogServiceLevelDiDO::getInternalFailed));
            data.setTotalNum(NumberUtils.sum(value, ClsLogServiceLevelDiDO::getTotal));
            if (data.getTotalNum().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal subtract = data.getTotalNum().subtract(data.getInsufficientFailedNum())
                        .subtract(data.getInternalFailedNum());
                data.setServiceLevel(subtract.divide(data.getTotalNum(), 4, RoundingMode.HALF_UP));
            }
            result.add(data);
        }
        return result;
    }


    public Map<String, String> getDayMap(String startDate, String endDate) {
        Map<String, String> result = new HashMap<>();
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            result.put(DateUtils.formatDate(start), DateUtils.formatDate(start));
            start = start.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }
}
