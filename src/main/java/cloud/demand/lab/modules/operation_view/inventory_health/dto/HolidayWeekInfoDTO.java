package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import lombok.Data;

/**
 * 安全库存计算三种算法用到的DTO，包含过去与未来
 * 和HolidayWeekDTO不同的是有当周周日日期，这里先不改动原来的代码
 */
@Data
public class HolidayWeekInfoDTO {

    //  当周周一日期
    private String startDate;

    //  当周周日日期
    private String endDate;

    //  节假周-年
    private Integer year;

    //  节假周-月
    private Integer month;

    //  节假周-周
    private Integer week;

    //  对于当前是第几周
    private Integer weekNFromNow;

    public String getYearWeek(){
        return String.join("@", this.getYear().toString(), this.getWeek().toString());
    }
}
