package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_turnover;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;

@Data
public class InventoryTurnoverReq {

    /**
     * 切片时间
     */
    private String statDate;

    /**
     * 时间范围
     */
    private DateRange dateRange;

    /** 可用区类型: 主力园区、在售非主力园区、其他 */
    private List<String> zoneCategory;

    /** 机型类型: 主力机型、在售非主力机型、其他 */
    private List<String> instanceTypeCategory;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /**实例类型*/
    private List<String> instanceType;

    /**
     * 库存二级分类：好料、差料、呆料、流转库存、冗余库存、周转库存
     */
    private List<String> materialType;

    /** 库存类型：线上库存，线下库存 */
    private List<String> lineType;

    /**
     * 库存细分：用户预扣、小核库存、大核库存、大核预留等等
     */
    private List<String> invDetailType;

    /**
     * 是否剔除安全库存
     */
    private Boolean reduceHealthInventory;

    @Data
    public static class DateRange {

        /**
         * 时间类型：day or month
         */
        private String dateType;

        /**
         * 开始时间
         */
        private String start;
        /**
         * 结束时间
         */
        private String end;
    }


    public WhereSQL genBasicCondition() {

        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceType)){
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)){
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)){
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)){
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)){
            condition.and("zone_name in (?)", zoneName);
        }
        return condition;
    }

}
