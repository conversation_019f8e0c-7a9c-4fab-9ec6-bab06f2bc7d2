package cloud.demand.lab.modules.operation_view.operation_view.web;


import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view.model.ClsLogInfo;
import cloud.demand.lab.modules.operation_view.operation_view.model.ClsLogInfoReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.EffectiveAlgorithmDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.EnableDeliveryDataForAlgorithmDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.InstanceModelManualConfigBatchReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.InstanceModelManualConfigReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewExternalResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewInstanceModelResp;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.ResultMsgDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.SafetyInvManualReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.SafetyInvThresholdReq;
import cloud.demand.lab.modules.operation_view.operation_view.service.ClsLogService;
import cloud.demand.lab.modules.operation_view.operation_view.service.ExcelService;
import cloud.demand.lab.modules.operation_view.operation_view.service.InstanceModelManualConfigService;
import cloud.demand.lab.modules.operation_view.operation_view.service.ManualConfigService;
import cloud.demand.lab.modules.operation_view.operation_view.service.OperationViewService2;
import cloud.demand.lab.modules.operation_view.operation_view.service.ThresholdTransferService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.function.Supplier;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.config.DynamicProperty;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 新版的运营视图
 */
@JsonrpcController("/operation-view2")
public class OperationViewController2 {
    public static final Supplier<List<Object>> indexBoards = DynamicProperty.create("app.config.report.index-board", "[]", o -> JSON.parse(o, ArrayList.class));

    @Autowired
    private OperationViewService2 operationViewService2;
    @Resource
    private ThresholdTransferService thresholdTransferService;

    @Resource
    private ManualConfigService manualConfigService;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private RedisHelper redisHelper;

    @Resource
    InstanceModelManualConfigService instanceModelManualConfigService;

    @Resource
    ExcelService excelService;

    @Resource
    private ClsLogService clsLogService;


    /**
     * 新版运营视图主查询接口-for Web
     * 目前只支持CVM产品
     */
    @RequestMapping
    public OperationViewResp2 queryAllProductSummary(@JsonrpcParam @Valid OperationViewReq2 req) {
        checkOuterInvokeParam(req);
        return operationViewService2.queryAllProductSummary(req);
    }

    /**
     * 新版运营视图主查询接口-for Outer Invoke
     * 底层实现和queryAllProductSummary完全相同
     */
    @RequestMapping
    public OperationViewExternalResp2 queryAllProductSummaryExternal(@JsonrpcParam @Valid OperationViewExternalReq2 req){
        if (req == null){
            throw new WrongWebParameterException("缺失请求参数，请确认！");
        }
        if (req.getDate() == null){
            //  日期为空取昨天
            req.setDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        }

        return operationViewService2.queryAllProductSummaryExternal(req);
    }

    /**
     * 检查外部api调用的请求参数
     */
    private void checkOuterInvokeParam(OperationViewReq2 req){
        if (req == null){
            throw new WrongWebParameterException("缺失请求参数，请确认！");
        }
        if (req.getDate() == null){
            //  日期为空取昨天
            req.setDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        }
    }

    @RequestMapping
    public List<String> queryAllStatTimes() {
        String raw = "select distinct stat_time from cloud_demand.dws_inventory_health_weekly_scale_df order by stat_time desc";
        return ckcldDBHelper.getRaw(String.class, raw);
    }

    @RequestMapping
    public ResultMsgDTO setEffectiveAlgorithm(@JsonrpcParam EffectiveAlgorithmDTO effectiveAlgorithm){
        boolean flag = redisHelper.setString("operationViewAlgorithm", Integer.MAX_VALUE, effectiveAlgorithm.getAlgorithm());
        ResultMsgDTO dto = new ResultMsgDTO();
        dto.setMsg(flag ? "success" : "fail");
        return dto;
    }

    @RequestMapping
    public EffectiveAlgorithmDTO getEffectiveAlgorithm(){
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        EffectiveAlgorithmDTO dto = new EffectiveAlgorithmDTO();
        dto.setAlgorithm(operationViewAlgorithm);
        return dto;
    }

    @RequestMapping
    public ResultMsgDTO setEnableDeliveryDataForAlgorithm(@JsonrpcParam EnableDeliveryDataForAlgorithmDTO effectiveAlgorithm){
        boolean flag = redisHelper.setString("operationViewEnableDeliveryDataForAlgorithm", Integer.MAX_VALUE, effectiveAlgorithm.getStatus());
        ResultMsgDTO dto = new ResultMsgDTO();
        dto.setMsg(flag ? "success" : "fail");
        return dto;
    }

    @RequestMapping
    public EnableDeliveryDataForAlgorithmDTO getEnableDeliveryDataForAlgorithm(){
        String status = redisHelper.getString("operationViewEnableDeliveryDataForAlgorithm");
        EnableDeliveryDataForAlgorithmDTO dto = new EnableDeliveryDataForAlgorithmDTO();
        dto.setStatus(status);
        return dto;
    }


    @RequestMapping
    public Result setSafetyInvThreshold(@Valid @JsonrpcParam SafetyInvThresholdReq req){
        if (req == null || ListUtils.isEmpty(req.getData())){
            return Result.fail(-1, "安全库存阈值设置为空");
        }
        return thresholdTransferService.setSafetyInvThreshold(req, LoginUtils.getUserName());
    }

    @RequestMapping
    public Result setSafetyInvManual(@Valid @JsonrpcParam SafetyInvManualReq req){
        if (req == null || ListUtils.isEmpty(req.getData())){
            return Result.fail(-1, "安全库存阈值设置为空");
        }
        return manualConfigService.setSafetyInvManual(req, LoginUtils.getUserName());
    }

    @RequestMapping
    public Result getIndexBoards(){
        return Result.success(indexBoards.get());
    }


    @RequestMapping
    public Result setInstanceModelManualConfig(@JsonrpcParam InstanceModelManualConfigReq req) {
        return instanceModelManualConfigService.setManualValue(req);
    }

    @RequestMapping
    public Result setInstanceModelManualConfigBatch(@JsonrpcParam InstanceModelManualConfigBatchReq req) {
        for (InstanceModelManualConfigReq item : req.getData()) {
            setInstanceModelManualConfig(item);
        }

        return Result.success("success");
    }

    @RequestMapping
    OperationViewInstanceModelResp queryAllProductSummaryInstanceModel(@JsonrpcParam OperationViewInstanceModelReq req) {
        return operationViewService2.queryAllProductSummaryInstanceModel(req);
    }

    @RequestMapping
    public Object exportInstanceModelConfigExcel(@JsonrpcParam OperationViewInstanceModelReq req) {
        FileNameAndBytesDTO res = excelService.exportInstanceModelConfigExcel(req);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    @RequestMapping
    public Object importInstanceModelConfigExcel(@RequestParam("file") MultipartFile file, @RequestParam("date") String date) {
        excelService.importInstanceModelConfigExcel(file, date);
        return Result.success("success");
    }

    /** 查询 cls 日志 */
    @RequestMapping
    public List<ClsLogInfo> queryClsLogInfo(@JsonrpcParam ClsLogInfoReq req){
        return clsLogService.queryClsLog(ClsLogProductEnum.valueOf(req.getProduct()), req.getStatTime());
    }
}
