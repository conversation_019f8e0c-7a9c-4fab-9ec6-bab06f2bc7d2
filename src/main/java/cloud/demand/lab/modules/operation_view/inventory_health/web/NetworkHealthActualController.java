package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.NetworkHealthActualService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@JsonrpcController("/network-inventory-health")
public class NetworkHealthActualController {

    @Resource
    NetworkHealthActualService networkHealthActualService;


    @RequestMapping
    public Object queryNetworkHealthActualTrendReport(@JsonrpcParam NetworkHealthActualTrendReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        return networkHealthActualService.queryNetworkHealthActualTrendReport(req);
    }

}
