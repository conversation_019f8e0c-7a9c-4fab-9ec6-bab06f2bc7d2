package cloud.demand.lab.modules.operation_view.inventory_health.entity;


import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cynos_applyset_success")
public class CynosApplysetSuccessDO {

    /** 自增主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 总发货实例个数<br/>Column: [total_num] */
    @Column(value = "total_num")
    private Integer totalNum;

    /** 超时15min为失败，失败发货实例个数<br/>Column: [fail_num] */
    @Column(value = "fail_num")
    private Integer failNum;

    /** 地域信息<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 数据插入时间(年月日)<br/>Column: [createDate] */
    @Column(value = "createDate")
    private String createDate;

    /** 发货成功率<br/>Column: [successPct] */
    @Column(value = "successPct")
    private String successPct;

    /** 可用区<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

}