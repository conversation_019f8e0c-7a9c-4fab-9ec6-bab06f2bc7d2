package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 弹性备货配额，计算所需要的中间数据以及计算结果
 */
@Table("dws_buffer_safe_inventory_df")
@Data
public class DwsBufferSafeInventoryDfDO {
    /**
     * 统计时间
     */
    @Column("stat_time")
    private String statTime;

    /**
     * 产品类型：CVM、GPU、裸金属
     */
    @Column("product_type")
    private String productType;

    /**
     * 境内外
     */
    @Column("customhouse_title")
    private String customhouseTitle;

    /**
     * 区域名
     */
    @Column("area_name")
    private String areaName;

    /**
     * 地域名
     */
    @Column("region_name")
    private String regionName;

    /**
     * 可用区名
     */
    @Column("zone_name")
    private String zoneName;

    /**
     * 实例类型
     */
    @Column("instance_type")
    private String instanceType;

    /**
     * 弹性备货配额
     */
    @Column("buffer_safety_inv")
    private BigDecimal bufferSafetyInv;

    /**
     * MCK弹性备货配额算法 = 弹性用量平均核心数 / ROI
     */
    @Column("mck_buffer_safety_inv")
    private BigDecimal mckBufferSafetyInv;

    /**
     * 计算 MCK 弹性备货配额算法的 ROI
     */
    @Column("mck_buffer_roi")
    private BigDecimal mckBufferRoi;
    /**
     * 弹性库存利用率=1/ROI
     */
    @Column("mck_buffer_rate")
    private BigDecimal mckBufferRate;

    /**
     * 弹性用量平均核心数
     */
    @Column("buffer_average_core")
    private BigDecimal bufferAverageCore;

    /**
     * 弹性用量统计时的开始时间
     */
    @Column("buffer_average_start_date")
    private String bufferAverageStartDate;
    /**
     * 弹性用来统计时的结束时间
     */
    @Column("buffer_average_end_date")
    private String bufferAverageEndDate;

    /**
     * 弹性用量的服务水平
     */
    @Column("buffer_average_service_level")
    private BigDecimal bufferServiceLevel;

    /**
     * 弹性服务水平系数
     */
    @Column("buffer_average_service_level_factor")
    private BigDecimal bufferServiceLevelFactor;

    /**
     * 计算最终的弹性备货配额，兼容新老两种算法：
     * 1. 如果mck_buffer_safety_inv不为空，则使用MCK算法
     * 2. 否则如果 buffer_average_core不为空，则使用旧算法
     * 3. 否则返回0
     *
     * @return
     */
    public BigDecimal getFinalBufferSafetyInv() {
        BigDecimal finalBufferSafetyInv = this.mckBufferSafetyInv;

        if (finalBufferSafetyInv == null || finalBufferSafetyInv.compareTo(BigDecimal.ZERO) == 0) {
            if (this.bufferAverageCore != null && this.bufferAverageCore.compareTo(BigDecimal.ZERO) != 0) {
                finalBufferSafetyInv = this.bufferSafetyInv;
            }
        }

        return finalBufferSafetyInv == null ? BigDecimal.ZERO : finalBufferSafetyInv;
    }
}
