package cloud.demand.lab.modules.operation_view.entity.plan;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Objects;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("static_stock_principal_hosttype")
public class StaticStockPrincipalHosttypeDO {

    @Column(value = "hosttype", isKey = true)
    private String hosttype;

    @Override
    public boolean equals(Object o) {
        // 如果是同一个对象，直接返回true
        if (this == o) {
            return true;
        }
        // 如果对象为null或类型不匹配，返回false
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StaticStockPrincipalHosttypeDO o1 = (StaticStockPrincipalHosttypeDO) o;

        return Objects.equals(hosttype, o1.getHosttype());
    }


    @Override
    public int hashCode() {
        return Objects.hash(hosttype);
    }
}
