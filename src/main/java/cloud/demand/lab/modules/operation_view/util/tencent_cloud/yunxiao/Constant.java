package cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao;

import java.math.BigDecimal;

public class Constant {
    public static final String NOT_HAVE = "Not have";

    public static final String EMPTY_VALUE = "(空值)";
    public static final String TAIL_CUSTOMER = "中长尾客户";

    /** 中长尾客户的客户简称为：uin:0 */
    public static final String TAIL_CUSTOMER_SHORT_NAME = "uin:0";

    public static final String HEAD_INDUSTRYDEPT = "战略客户部";

    public static final BigDecimal b05 = new BigDecimal("0.5");
    public static final BigDecimal b03 = new BigDecimal("0.3");
    public static final BigDecimal b02 = new BigDecimal("0.2");
    public static final BigDecimal b3 = new BigDecimal("3");
    public static final BigDecimal b100 = new BigDecimal("100");

    /** 新增退回权重（新中长尾算法） */
    public static BigDecimal addOrRetWeight = new BigDecimal("0.90");

    /** 弹性权重（新中长尾算法） */
    public static BigDecimal elaWeight = new BigDecimal("0.10");


    public static BigDecimal getAddOrRetWeight(){
        return addOrRetWeight;
    }

    public static BigDecimal getElaWeight(){
        return elaWeight;
    }
}
