package cloud.demand.lab.modules.operation_view.operation_view.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ReturnT<T> implements IReturn<T>{

    /**状态码: 0是成功; 1为失败; 其他状态码待补充*/
    private Integer status;

    /**返回结果体*/
    private T body;

    /**返回结果信息*/
    private String message;

    /** T为集合且分页情况下提供的总页数，这里非必填，使用SopHttpIteratorUtil时
     * 以返回的size和请求的size是否一致为最高优先级的判定是否分页结束的标准 */
    private Integer total;

    public static <T> ReturnT<T> ok(T t){
        return ReturnT.<T>builder().status(0).body(t).build();
    }

    public static <T> ReturnT<T> fail(T t){
        return ReturnT.<T>builder().status(ReturnTStatus.FAIL.getStatus()).body(t).build();
    }

    public static <T> ReturnT<T> ok(){
        return ReturnT.<T>builder().status(0).body(null).build();
    }

    public static boolean isOk(ReturnT<?> returnT){
        return returnT != null && returnT.isOk();
    }

    public boolean isOk(){
        return status !=null && status.equals(ReturnTStatus.SUCCESS.getStatus());
    }

    @Override
    public String getError() {
        return message;
    }
}
