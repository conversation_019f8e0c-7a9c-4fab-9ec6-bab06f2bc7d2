package cloud.demand.lab.modules.operation_view.test;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.lab.modules.operation_view.entity.web.common.StreamDownloadBean;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/4 17:48
 */
@Slf4j
@Service
public class TestServiceImpl implements TestService {

    @Resource
    private DictService dictService;

    @Resource
    private DBHelper prodReadOnlyCkStdCrpDBHelper;

    @Override
    public ResponseEntity<InputStreamResource> exportDemand() {
        List<IndustryDemandIndustryWarZoneDictDO> dicts = dictService.queryEnableIndustryWarZoneCustomerConfig();
        Map<String, IndustryDemandIndustryWarZoneDictDO> map = ListUtils.toMap(dicts,
                IndustryDemandIndustryWarZoneDictDO::getCustomerName, item -> item);
        List<String> customerNames = dicts.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).distinct().collect(Collectors.toList());
        List<DwdTxyAppidInfoCfDO> dbList = prodReadOnlyCkStdCrpDBHelper.getAll(DwdTxyAppidInfoCfDO.class, "where customer_short_name in (?) ", customerNames);
        List<IndustryDemandIndustryWarZoneDictVO> retList = ListUtils.newArrayList();
        for (DwdTxyAppidInfoCfDO item : dbList) {
            IndustryDemandIndustryWarZoneDictDO dict = map.get(item.getCustomerShortName());
            IndustryDemandIndustryWarZoneDictVO vo = new IndustryDemandIndustryWarZoneDictVO();
            BeanUtils.copyProperties(dict, vo);
            vo.setCustomerName1(item.getCustomerName());
            vo.setGroupName(item.getGname());
            vo.setBusinessManagerOaDept(item.getBusinessManagerOaDept());
            retList.add(vo);
        }

        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();

            WriteSheet tempSheet = EasyExcel
                    .writerSheet("数据")
                    .head(IndustryDemandIndustryWarZoneDictVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            writer.write(retList, tempSheet);

            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "customer_info.xlsx";
        return new StreamDownloadBean(filename, in);
    }
}
