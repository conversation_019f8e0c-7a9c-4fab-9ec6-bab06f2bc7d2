package cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class BigDataHealthActualData {

    private String statTime;

    private String instanceType;

    private String zoneName;

    private String areaName;

    private String regionName;

    private String customhouseTitle;

    private String product;

    private String zoneCategory;

    private String instanceCategory;

    private String ginsFamily;

    private BigDecimal apiSucTotal;

    private BigDecimal apiTotal;

    private BigDecimal apiSucRate;

}
