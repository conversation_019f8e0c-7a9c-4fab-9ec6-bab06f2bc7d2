package cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 安全库存历史周净增、历史周峰计算DTO
 */
@Data
public class SafetyInventoryHistoryPeakDTO extends SafetyInventoryHistoryDTO {
    @Column(value = "bill_num")
    private BigDecimal billNum;
    @Column(value = "service_num")
    private BigDecimal serviceNum;
}
