package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;
import org.nutz.lang.Strings;

@Data
@ToString
@Table("dws_inventory_health_weekly_scale_df")
public class DwsInventoryHealthWeeklyScaleDfDO {

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 安全库存计算的节假年<br/>Column: [holiday_year] */
    @Column(value = "holiday_year")
    private Integer holidayYear;

    /** 安全库存计算的节假月<br/>Column: [holiday_month] */
    @Column(value = "holiday_month")
    private Integer holidayMonth;

    /** 安全库存计算的节假周<br/>Column: [holiday_week] */
    @Column(value = "holiday_week")
    private Integer holidayWeek;

    /** 节假周开始时间<br/>Column: [holiday_week_start_date] */
    @Column(value = "holiday_week_start_date")
    private LocalDate holidayWeekStartDate;

    /** 节假周结束时间<br/>Column: [holiday_week_end_date] */
    @Column(value = "holiday_week_end_date")
    private LocalDate holidayWeekEndDate;

    /** holiday_week相对于stat_time是第几周<br/>Column: [week_index] */
    @Column(value = "week_index")
    private Integer weekIndex;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域名称 华南地区<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域中文名称 广州<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名 广州四区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 名单客户/报备客户/中长尾客户/头部客户/ALL<br/>Column: [customer_custom_group] */
    @Column(value = "customer_custom_group")
    private String customerCustomGroup;

    /** 用户自主剔除的uin列表<br/>Column: [exclude_uin_list] */
    @Column(value = "exclude_uin_list")
    private String excludeUinList;

    /** 周净增逻辑数（内部计费、外部服务）<br/>Column: [week_diff_logic_num] */
    @Column(value = "week_diff_logic_num")
    private BigDecimal weekDiffLogicNum;

    /** 周峰逻辑数<br/>Column: [week_peak_logic_num] */
    @Column(value = "week_peak_logic_num")
    private BigDecimal weekPeakLogicNum;

    /** 净增周峰-服务用量 */
    @Column(value = "week_peak_service_logic_num")
    private BigDecimal weekPeakServiceLogicNum;

    /** 净增周峰-计费用量 */
    @Column(value = "week_peak_bill_logic_num")
    private BigDecimal weekPeakBillLogicNum;

    /** 净增周峰-服务用量-近12周平均 */
    @Column(value = "week_peak_service_logic_num_avg_12")
    private BigDecimal weekPeakServiceLogicNumAvg12;

    /** 净增周峰-服务用量-近13周平均 */
    @Column(value = "week_peak_service_logic_num_avg_13")
    private BigDecimal weekPeakServiceLogicNumAvg13;

    /** 净增周峰-计费用量-近12周平均 */
    @Column(value = "week_peak_bill_logic_num_avg_12")
    private BigDecimal weekPeakBillLogicNumAvg12;

    /** 净增周峰-计费用量-近13周平均 */
    @Column(value = "week_peak_bill_logic_num_avg_13")
    private BigDecimal weekPeakBillLogicNumAvg13;

    /** 净增周峰-售卖（内部服务，外部计费）-近12周平均 */
    @Column(value = "week_peak_logic_num_avg_12")
    private BigDecimal weekPeakLogicNumAvg12;

    /** 净增周峰-售卖（内部服务，外部计费）-近13周平均 */
    @Column(value = "week_peak_logic_num_avg_13")
    private BigDecimal weekPeakLogicNumAvg13;

    /**
     * 获取分组K
     */
    public String getGroupK(){
        return Strings.join("@", this.getHolidayYear(), this.getHolidayWeek(),
                this.getInstanceType(), this.getProductType(), this.getZoneName());
    }

}
