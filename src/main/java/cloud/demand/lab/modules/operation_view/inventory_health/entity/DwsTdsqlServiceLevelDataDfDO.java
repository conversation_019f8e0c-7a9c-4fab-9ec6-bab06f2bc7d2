package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dws_tdsql_service_level_data_df")
public class DwsTdsqlServiceLevelDataDfDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 可用区code<br/>Column: [zone_code] */
    @Column(value = "zone_code")
    private String zoneCode;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;


    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 地域code<br/>Column: [region_code] */
    @Column(value = "region_code")
    private String regionCode;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 线上最小剩余内存量<br/>Column: [min_rest_mem] */
    @Column(value = "min_rest_mem")
    private Integer minRestMem;

    /** 线上最小剩余磁盘量<br/>Column: [min_rest_disk] */
    @Column(value = "min_rest_disk")
    private Integer minRestDisk;

    /** 线上剩余内存量<br/>Column: [rest_mem] */
    @Column(value = "rest_mem")
    private Integer restMem;

    /** 线上剩余磁盘量<br/>Column: [rest_disk] */
    @Column(value = "rest_disk")
    private Integer restDisk;

    /** 发货成功数量<br/>Column: [suc_cnt] */
    @Column(value = "suc_cnt")
    private Integer sucCnt;

    /** 发货总量<br/>Column: [total_cnt] */
    @Column(value = "total_cnt")
    private Integer totalCnt;

    /** 售罄总量<br/>Column: [sold_out_cnt] */
    @Column(value = "sold_out_cnt")
    private Integer soldOutCnt;

    /** 售卖总量<br/>Column: [sold_cnt] */
    @Column(value = "sold_cnt")
    private Integer soldCnt;

}
