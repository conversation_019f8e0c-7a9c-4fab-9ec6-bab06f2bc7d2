package cloud.demand.lab.modules.operation_view.inventory_health.enums;

/**
 * 安全库存可用区态
 * 主力园区、辅助园区、待收敛园区、收敛园区、其他园区
 */
public enum InventoryHealthZoneType {
    PRINCIPAL("PRINCIPAL", "主力可用区"),
    SECONDARY("SECONDARY", "辅助可用区"),
    WITHDRAWING("WITHDRAWING", "待收敛可用区"),
    WITHDRAW("WITHDRA<PERSON>", "已收敛可用区"),
    SPECIAL("SPECIAL", "特殊专区"),
    OTHER("OTHER", "其他可用区");

    String code;
    String name;

    InventoryHealthZoneType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameFromCode(String code) {
        for (InventoryHealthZoneType status : InventoryHealthZoneType.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }

    public static String getCodeFromName(String name) {
        for (InventoryHealthZoneType status : InventoryHealthZoneType.values()) {
            if (status.getName().equals(name)) {
                return status.getCode();
            }
        }
        return null;
    }
}
