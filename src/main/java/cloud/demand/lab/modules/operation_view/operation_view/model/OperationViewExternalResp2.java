package cloud.demand.lab.modules.operation_view.operation_view.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OperationViewExternalResp2 {

    private List<Item> data;

    @Data
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class Item {
        /**
         * 境内外
         */
        private String customhouseTitle;
        /**
         * 区域名
         */
        private String areaName;
        /**
         * 地域名
         */
        private String regionName;
        /**
         * 可用区名
         */
        private String zoneName;

        /**
         * 实例类型，云产品那边期望用 instanceFamily，所以将 instanceType 改成 instanceFamily
         */
        private String instanceFamily;

        /**
         * 实例规格，暂未实现，留空备用
         */
        private String instanceModel;

        /**
         * 安全库存 = 弹性备货配额(bufferSafetyInv) + 包月安全库存(monthlySafetyInv)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal safetyInv;

        /**
         * 实际库存
         */
        private BigDecimal actualInv;

        /**
         * 实际冗余库存系数 = 实际库存 / 安全库存
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal redundantInvRatio;

        /**
         * 目标冗余系数，2023 年 为 1.5
         */
        private BigDecimal targetRedundantInvRatio;

        /**
         * 实际冗余库存 = max(实际库存量-冗余系数目标值*安全库存量,0)
         */
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal redundantInv;

        /**包月安全库存*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal monthlySafetyInv;

        /** 弹性备货配额，特别说明，虽然现在弹性备货配额3种算法都一样，
         * 但它其实应该跟着算法走的，包括参与计算和未来扩展性的设计的考虑*/
        @JsonSerialize(using = BigDecimal2ScaleSerializer.class)
        private BigDecimal bufferSafetyInv;

        /**
         * 安全库存人工调整
         */
        private BigDecimal safeInvManualConfig;
    }
}
