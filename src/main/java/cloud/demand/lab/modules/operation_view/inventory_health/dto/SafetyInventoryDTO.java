package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 安全库存
 */
@Data
public class SafetyInventoryDTO extends CommonConditionDTO {

    /**对应的日期*/
    private String date;

    private String customhouseTitle;
    private String areaName;
    private String regionName;

    /**
     * 可用区
     */
    private String zoneName;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 总核心数 = 包年包月 + 弹性用量
     */
    private Integer safetyInventoryCore;

    /**
     * 包年包月安全库存核心数 = z(90%) * D * MAPE * sqrt(LT/t)
     */
    private Integer prePaidSafetyInventoryCore;

    /**
     * 计算用到的：包年包月预测需求
     */
    private Integer forecastDemandCore;


    /**
     * 计算用到的：包年包月预测需求-外部行业
     */
    private Integer forecastDemandCoreOuter;

    /**
     * 计算用到的：包年包月预测需求-内部领用
     */
    private Integer forecastDemandCoreInner;


    /**服务水平*/
    private BigDecimal serviceLevel;

    /**服务水平系数*/
    private BigDecimal serviceLevelFactor;

    /**需求预测准确率*/
    private BigDecimal forecastDemandAccuracyRate;

    /**需求预测开始时间*/
    private String forecastDemandStartDate;
    /**需求预测结束时间*/
    private String forecastDemandEndDate;

    /**
     * 弹性用量安全库存核心数 = X%(服务水平）*L(弹性量/天)
     */
    private BigDecimal bufferSafetyInventoryCore;

    /**
     * 弹性日均用量
     */
    private Integer bufferAverageCore;

    /**弹性用量开始时间*/
    private String bufferAverageStartDate;

    /**弹性用量结束时间*/
    private String bufferAverageEndDate;

    /**
     * 计算用到的sla，也就是公式里面的LT
     */
    private Integer sla;

    /**计算出安全库存的完整表达式*/
    private String safetyInventoryCoreExpression;

}
