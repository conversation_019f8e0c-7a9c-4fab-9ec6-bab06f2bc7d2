package cloud.demand.lab.modules.operation_view.operation_view.entity;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.InstanceModelManualConfigReq;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import org.nutz.lang.Strings;

@Table(value = "inventory_health_instance_model_manual_config")
@Data
public class InventoryHealthInstanceModelManualConfigDO extends BaseDO {
    @Column("product")
    private String product;

    @Column("zone_name")
    private String zoneName;

    @Column("instance_type")
    private String instanceType;

    @Column("instance_model")
    private String instanceModel;

    @Column("date")
    private LocalDate date;

    @Column("value")
    private BigDecimal value;

    public static InventoryHealthInstanceModelManualConfigDO fromReq(InstanceModelManualConfigReq req) {
        InventoryHealthInstanceModelManualConfigDO res = new InventoryHealthInstanceModelManualConfigDO();
        res.setProduct("CVM");
        res.setZoneName(req.getZoneName());
        res.setInstanceType(req.getInstanceType());
        res.setInstanceModel(req.getInstanceModel());
        res.setDate(DateUtils.parseLocalDate(req.getDate()));
        res.setValue(req.getValue());
        return res;
    }

    public static InventoryHealthInstanceModelManualConfigDO transform(InventoryHealthInstanceModelManualConfigDO req) {
        InventoryHealthInstanceModelManualConfigDO res = new InventoryHealthInstanceModelManualConfigDO();
        res.setProduct("CVM");
        res.setZoneName(req.getZoneName());
        res.setInstanceType(req.getInstanceType());
        res.setInstanceModel(req.getInstanceModel());
        res.setDate(req.getDate());
        res.setValue(req.getValue());
        return res;
    }

    public String toKey() {
        return Strings.join("@", this.getZoneName(), this.getInstanceModel());
    }
}

