package cloud.demand.lab.modules.operation_view.operation_view.entity;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class InstanceModelScaleDO {
    @Column("zone_name")
    private String zoneName;
    @Column("instance_type")
    private String instanceType;
    @Column("instance_model")
    private String instanceModel;

    @Column("start_bill")
    private BigDecimal startBill;
    @Column("start_serve")
    private BigDecimal startServe;
    @Column("end_bill")
    private BigDecimal endBill;
    @Column("end_serve")
    private BigDecimal endServe;
    @Column("change_bill")
    private BigDecimal changeBill;
    @Column("change_serve")
    private BigDecimal changeServe;
}
