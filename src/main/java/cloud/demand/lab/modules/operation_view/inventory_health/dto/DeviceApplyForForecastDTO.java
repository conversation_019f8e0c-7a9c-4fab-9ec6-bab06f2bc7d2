package cloud.demand.lab.modules.operation_view.inventory_health.dto;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Map;
import lombok.Data;

@Table("device_apply")
@Data
public class DeviceApplyForForecastDTO {

    @Column(value = "sub_id")
    private String subId;

    @Column(value = "module_name")
    private String moduleName;

    @Column(value = "zone")
    private String campus;

    @Column(value = "business1")
    private String business1;

    @Column(value = "business2")
    private String business2;

    @Column(value = "business3")
    private String business3;

    @Column(value = "product")
    private String product;

    @Column(value = "omdPromiseInfo")
    private String omdPromiseInfo;

    @Column(value = "deviceType")
    private String deviceType;

    private String instanceType;

    /**
     * 星云子单数量
     */
    @Column(value = "totalNum")
    private Integer totalNum;

    /**
     * 已到货量
     */
    @Column(value = "usedNum")
    private Integer usedNum;

    /**
     * 已提货量
     */
    @Column(value = "deliverNum")
    private Integer deliverNum;

    /**
     * 未提货量
     */
    private Integer noDeliverNum;

    /**
     * 未到货量
     */
    private Integer noUsedNum;

    private String customhouseTitle;

    private String areaName;

    private String regionName;

    private String zoneName;

    /**
     * 项目类型-proj_type：自研上云/非自研上云
     */
    private String projType;

    /**
     * 未来采购设备台数
     */
    private Integer futureNum;

    /**
     * 未来采购设备核心数
     */
    private Integer futureCoreNum;

    /**
     * 承诺交付日期
     */
    private Map<String, Integer> promiseDeliveryInfo;

    @Column("industry")
    private String industryDept;

    @Column("plan_product")
    private String planProductName;

    private String computeType;
}
