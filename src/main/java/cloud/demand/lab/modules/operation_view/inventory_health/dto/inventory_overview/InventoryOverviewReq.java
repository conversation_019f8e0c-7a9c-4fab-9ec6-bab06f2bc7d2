package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;

@Data
public class InventoryOverviewReq {

    private String statDate;

    private String timeDimension;

    private List<String> customhouseTitle;

    private List<String> country;

    private List<String> regionName;

    private List<String> zoneName;

    private String dimension;


    public WhereSQL genBasicCondition() {

        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }
        return condition;
    }


}
