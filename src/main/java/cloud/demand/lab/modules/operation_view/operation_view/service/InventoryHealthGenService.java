package cloud.demand.lab.modules.operation_view.operation_view.service;

import cloud.demand.lab.modules.operation_view.operation_view.enums.ClsLogProductEnum;
import java.util.List;

/**
 * 库存健康数据生成Service
 */
public interface InventoryHealthGenService {

    /**
     * 生成三种算法的安全库存中间表的数据
     * 作为定时任务跑固化名单客户时：uins和isPositive都为空
     * 作为页面试验性筛选时：
     *      ListUtils.isNotEmpty(uins) && isPositive == true， 正向过滤uins
     *      ListUtils.isNotEmpty(uins) && isPositive == false, 反向剔除uins
     *      客户名称同理
     */
    void genSafetyInventoryDetail(String statTime, List<String> uins, List<String> customerNames, Boolean isPositive);

    /**
     * 刷新历史周峰算法中间表的额外字段
     */
    void genSafetyInventoryDataHistoryPeak(String statTime, List<String> uins, List<String> customerNames, Boolean isPositive);

    /**
     * 生成物理机真实交付周期信息，用于计算安全库存。
     * 底数基于需求交付集市
     */
    void genDeliveryDaysData(String statTime);

    /**
     * 生成安全库存供应汇总表
     * 底数基于需求交付集市
     */
    void genSupplySummaryData(String statTime);

    /**
     * 对战略客户部配置的安全库存数据进行切片
     */
    void snapshotHeadZlkhbData(String statTime);

    /**
     * 生成包月安全库存数据
     * @param statTime
     */
    void genMonthlySafeInventoryData(String statTime);

    /**
     * 生成包月安全库存数据 - 仅仅包含 MCK 库存数据
     * @param statTime
     */
    void genMckMonthlySafeInventoryData(String statTime);

    /**
     * 生成实际库存数据
     */
    void genActualInventoryData(String statTime);


    /**
     * 生成弹性安全库存数据
     */
    void genBufferSafeInventoryData(String statTime);

    /**
     * 生成 Week - N 预测底表，用于 MCK历史安全库存算法
     */
    void genWeekNForecastData(String statTime);

    /**
     * 生成 MCK 周转库存算法底表
     */
    void genMckTurnoverInventoryData(String statTime);

    /**
     * 生成 MCK 预测周转库存算法底表
     * @param statTime
     */
    void genMckForecastTurnoverInventoryData(String statTime);

    /**
     * 生成 MCK 预测安全库存算法底表
     */
    void genMckForecastSafeInventoryData(String statTime);

    void genServiceLevelSoldData(String unix, String statTime);

    void genServiceLevelApiSuccessData(String unix, String statTime);

    void genLeisureAndBusySoldOutData(String statTime);

    void genInventoryHealthWeekData(String statTime);

    void genInventoryHealthMonthData(String statTime);

    void genCDBServiceLevelData(String statTime);

    void genCBSServiceLevelData(String statTime);


    /**
     * 生成 CLS 日志服务水平数据（CLB/EIP）
     * @param productEnum {@link ClsLogProductEnum}
     * @param statTime 切片时间，格式：yyyy-MM-dd，不传默认昨天
     */
    void genClsLogServiceLevelData(ClsLogProductEnum productEnum,String statTime);


    void genEndToEndMonthData(String yearMonth, String start, String end);

    void genCRSServiceLevelData(String statTime);

    void genTDSQLServiceLevelData(String statTime);

    void synchronizeStaticCvmType();
}
