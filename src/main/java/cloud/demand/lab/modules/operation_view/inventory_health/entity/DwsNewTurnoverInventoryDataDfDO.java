package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_new_turnover_inventory_data_df")
public class DwsNewTurnoverInventoryDataDfDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 未预扣周转库存核心数<br/>Column: [not_pre_deduct_turnover_cores] */
    @Column(value = "not_pre_deduct_turnover_cores")
    private Integer notPreDeductTurnoverCores;

    /** 待库存满足数<br/>Column: [await_for_inventory_cores] */
    @Column(value = "await_for_inventory_cores")
    private Integer awaitForInventoryCores;

    /** 库存满足订单核心数<br/>Column: [total_cores] */
    @Column(value = "total_cores")
    private Integer totalCores;

    /** 未预扣实际库存<br/>Column: [not_pre_actual_inventory] */
    @Column(value = "not_pre_actual_inventory")
    private Integer notPreActualInventory;

    /** 订单已预扣核心数<br/>Column: [acutal_pre_deduct_cores] */
    @Column(value = "actual_pre_deduct_cores")
    private Integer actualPreDeductCores;

}
