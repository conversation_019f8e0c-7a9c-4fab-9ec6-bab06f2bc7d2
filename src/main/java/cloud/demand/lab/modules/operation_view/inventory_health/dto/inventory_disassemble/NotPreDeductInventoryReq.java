package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble;


import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.List;
import lombok.Data;

@Data
public class NotPreDeductInventoryReq extends InventoryBasicReq{

    String start;

    String end;

    private List<String> industryDept;

    private List<String> warZone;

    private List<String> customerShortName;

    private List<String> uin;

    private List<String> appId;


    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }

        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL cateCondition = bean.genCategoryCondition(zoneCategory, instanceCategory, false,
                DateUtils.formatDate(DateUtils.yesterday()), customhouseTitle);
        condition.and(cateCondition);
        return condition;
    }

    public WhereSQL genCustomerCondition() {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(industryDept)) {
            condition.and("industry_dept in (?)", industryDept);
        }
        if (ListUtils.isNotEmpty(warZone)) {
            condition.and("war_zone in (?)", warZone);
        }
        if (ListUtils.isNotEmpty(customerShortName)) {
            condition.and("customer_short_name in (?)", customerShortName);
        }
        if (ListUtils.isNotEmpty(uin)) {
            condition.and("customer_uin in (?)", uin);
        }
        if (ListUtils.isNotEmpty(appId)) {
            condition.and("app_id in (?)", appId);
        }
        return condition;
    }

}
