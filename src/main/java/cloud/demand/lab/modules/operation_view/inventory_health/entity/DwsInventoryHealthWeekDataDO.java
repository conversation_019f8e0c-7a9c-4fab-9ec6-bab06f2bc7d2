package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dws_inventory_health_week_data")
public class DwsInventoryHealthWeekDataDO {

    /** 年周<br/>Column: [year_week] */
    @Column(value = "year_week")
    private String yearWeek;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 弹性平均开始时间<br/>Column: [buffer_avg_start_date] */
    @Column(value = "buffer_avg_start_date")
    private LocalDate bufferAvgStartDate;

    /** 弹性平均结束时间<br/>Column: [buffer_avg_end_date] */
    @Column(value = "buffer_avg_end_date")
    private LocalDate bufferAvgEndDate;

    /** 客户类型<br/>Column: [customer_custom_group] */
    @Column(value = "customer_custom_group")
    private String customerCustomGroup;

    /** 闲置预扣<br/>Column: [is_include_reserved] */
    @Column(value = "is_include_reserved")
    private Integer isIncludeReserved;

    /** 13周平均交付时长<br/>Column: [delivery_avg] */
    @Column(value = "delivery_avg")
    private BigDecimal deliveryAvg;

    /** 服务水平<br/>Column: [service_level] */
    @Column(value = "service_level")
    private BigDecimal serviceLevel;

    /** 弹性服务水平系数<br/>Column: [buffer_service_level_factor] */
    @Column(value = "buffer_service_level_factor")
    private BigDecimal bufferServiceLevelFactor;

    /** 服务水平系数<br/>Column: [service_level_factor] */
    @Column(value = "service_level_factor")
    private BigDecimal serviceLevelFactor;

    /** 弹性服务水平<br/>Column: [buffer_service_level] */
    @Column(value = "buffer_service_level")
    private BigDecimal bufferServiceLevel;

    /** 包月安全库存<br/>Column: [pre_paid_safety_inventory_core] */
    @Column(value = "pre_paid_safety_inventory_core")
    private Integer prePaidSafetyInventoryCore;

    /** 弹性用量平均核心数<br/>Column: [buffer_average_core] */
    @Column(value = "buffer_average_core")
    private Integer bufferAverageCore;

    /** 弹性用量<br/>Column: [buffer_safety_inventory_core] */
    @Column(value = "buffer_safety_inventory_core")
    private BigDecimal bufferSafetyInventoryCore;

    /** 安全库存人工调整<br/>Column: [safe_inv_manual_config] */
    @Column(value = "safe_inv_manual_config")
    private BigDecimal safeInvManualConfig;

    /** 安全库存核心数<br/>Column: [safety_inventory_core] */
    @Column(value = "safety_inventory_core")
    private Integer safetyInventoryCore;

    /** 周转库存 - 当周预扣均值<br/>Column: [turnover_week_reserved_avg_core] */
    @Column(value = "turnover_week_reserved_avg_core")
    private BigDecimal turnoverWeekReservedAvgCore;

    /** 周转库存<br/>Column: [turnover_inv] */
    @Column(value = "turnover_inv")
    private BigDecimal turnoverInv;

    /** 周转库存 - 当周执行量周峰<br/>Column: [turnover_week_peak_core] */
    @Column(value = "turnover_week_peak_core")
    private BigDecimal turnoverWeekPeakCore;
}
