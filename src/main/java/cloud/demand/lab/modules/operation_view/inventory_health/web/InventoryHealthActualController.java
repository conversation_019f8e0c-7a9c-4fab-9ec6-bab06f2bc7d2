package cloud.demand.lab.modules.operation_view.inventory_health.web;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.QueryInvDetailTypesReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualRangeReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.BaseConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType2;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.lab.modules.operation_view.inventory_health.service.ForecastViewService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthActualV2Service;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.TrendGraphService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import com.alibaba.druid.sql.ast.statement.SQLForeignKeyImpl.On;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 库存健康度 实际库存
 */
@Slf4j
@JsonrpcController("/inventory-health")
public class InventoryHealthActualController {

    @Resource
    private ForecastViewService forecastViewService;
    @Resource
    private TrendGraphService trendGraphService;
    @Resource
    private InventoryHealthActualV2Service inventoryHealthActualV2Service;
    @Resource
    private InventoryHealthConfigService inventoryHealthConfigService;

    @Resource
    private OutsideViewOldService outsideViewOldService;


    @RequestMapping
    public InventoryHealthActualResp queryInventoryHealthActual(@JsonrpcParam InventoryHealthActualReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        Date date = DateUtils.parse(req.getDate());
        if (date == null) {
            throw new WrongWebParameterException("日期date为空或者格式(正确格式yyyy-MM-dd)错误");
        }

        long startTime = System.currentTimeMillis();
        InventoryHealthActualResp resp = forecastViewService.queryInventoryHealthActual(req);
        long endTime = System.currentTimeMillis();
        log.info("queryInventoryHealthActual api cost:" + (endTime - startTime) + "ms");
        return resp;
    }

    /**
     * 库存健康新接口，主要取数逻辑跟运营视图对齐。创建一个新接口主要是为了：
     * 1. 重新整合取数逻辑
     * 2. 方便与老接口对比响应
     * 3. 方便随时回滚，回滚前端改就行
     * @param req
     * @return
     */
    @RequestMapping
    public InventoryHealthActualResp queryInventoryHealthActualV2(@JsonrpcParam InventoryHealthActualReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }
        Date date = DateUtils.parse(req.getDate());
        if (date == null) {
            throw new WrongWebParameterException("日期date为空或者格式(正确格式yyyy-MM-dd)错误");
        }
        if (req.getTimeDimension() == null || req.getTimeDimension().isEmpty()) {
            throw new WrongWebParameterException("时间维度timeDimension为空");
        }

        long startTime = System.currentTimeMillis();
        InventoryHealthActualResp resp = inventoryHealthActualV2Service.queryInventoryHealthActualV2(req);
        long endTime = System.currentTimeMillis();
        log.info("queryInventoryHealthActualV2 api cost:" + (endTime - startTime) + "ms");
        return resp;
    }

    /**
     * 库存健康趋势主接口
     */

    @RequestMapping
    public InventoryHealthTrendResp queryInventoryHealthTrend(@JsonrpcParam InventoryHealthTrendReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数为null");
        }

        if (req.getTimeDimension() == null || req.getTimeDimension().isEmpty()) {
            throw new WrongWebParameterException("时间维度timeDimension为空");
        }

        if (StringUtils.isBlank(req.getStart()) || StringUtils.isBlank(req.getEnd())) {
            throw new WrongWebParameterException("开始或者结束日期为空");
        }
        return inventoryHealthActualV2Service.queryInventoryHealthTrend(req);

    }

    @RequestMapping
    public List<String> queryMainInstanceFamily(@JsonrpcParam BaseConfigReq req){
        Map<String, List<String>> instanceTypeConfigMap = inventoryHealthConfigService.getInstanceTypeConfigMap(req.getDate());
        List<String> result = instanceTypeConfigMap.getOrDefault(InventoryHealthInstanceFamilyType2.PRINCIPAL.getCode(), ListUtils.newList());
        // 去重
        result = result.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
        return result;
    }

    @RequestMapping
    public List<String> queryMainZoneName(@JsonrpcParam BaseConfigReq req){
        Map<String, List<String>> zoneNameConfigMap = inventoryHealthConfigService.getZoneConfigMap(req.getDate());
        return zoneNameConfigMap.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()).stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
    }


    @RequestMapping
    public TrendGraphResp queryInventoryHealthTrendGraph(@JsonrpcParam TrendGraphReq req) {
        return trendGraphService.queryInventoryHealthTrendGraph(req);
    }


    @RequestMapping
    public Object exportPurchaseDetailExcel(@JsonrpcParam TrendGraphReq req) {
        FileNameAndBytesDTO res = trendGraphService.exportPurchaseDetailExcel(req);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    @RequestMapping
    public Object exportDeliveryDetailExcel(@JsonrpcParam TrendGraphReq req) {
        FileNameAndBytesDTO res = inventoryHealthActualV2Service.exportDeliveryDetailExcel(req);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    @RequestMapping
    public Object exportMoveDetailExcel(@JsonrpcParam TrendGraphReq req) {
        FileNameAndBytesDTO res = trendGraphService.exportMoveDetailExcel(req);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    @RequestMapping
    public Object queryInvDetailTypes(@JsonrpcParam QueryInvDetailTypesReq req) {
        return inventoryHealthActualV2Service.queryInvDetailTypes(req);
    }

    @RequestMapping
    public Object queryLineType(@JsonrpcParam QueryInvDetailTypesReq req) {
        return inventoryHealthActualV2Service.queryLineType(req);
    }

    @RequestMapping
    public Object queryMaterialType(@JsonrpcParam QueryInvDetailTypesReq req) {
        return inventoryHealthActualV2Service.queryMaterialType(req);
    }

    @RequestMapping
    public Object queryFullExcelData(@JsonrpcParam InventoryHealthActualRangeReq req) {
        return inventoryHealthActualV2Service.queryFullExcelData(req);
    }

    @RequestMapping
    public Object queryCvmfGinsFamily() {
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Set<String> result = new HashSet<>();
        for (CvmType cvmType : allCvmType) {
            result.add(cvmType.getGinsFamily());
        }
        return new ArrayList<>(result);
    }
}
