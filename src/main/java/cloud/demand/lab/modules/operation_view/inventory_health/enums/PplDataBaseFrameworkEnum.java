package cloud.demand.lab.modules.operation_view.inventory_health.enums;

import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum PplDataBaseFrameworkEnum {

    CDB_SINGLE("CDB-单节点", true, PplDatabaseEnum.MySQL),

    CDB_DOUBLE("CDB-双节点", true, PplDatabaseEnum.MySQL),

    CDB_THREE("CDB-三节点", true, PplDatabaseEnum.MySQL),

    CDB_CLUSTER("CDB-集群版", true, PplDatabaseEnum.MySQL),

    REDIS_STANDARD("Redis-标准架构", true, PplDatabaseEnum.Redis),

    REDIS_CLUSTER("Redis-集群架构", false, PplDatabaseEnum.Redis),

    MONGODB_SLICE_CLUSTER("Mongo-分片集群", false, PplDatabaseEnum.MongoDB),

    MONGODB_REPLICA_CLUSTER("Mongo-副本集群", true, PplDatabaseEnum.MongoDB),

    TDSQLC_CLUSTER("TDSQL-C-集群版", false, PplDatabaseEnum.TDSQLC),

    TDSQL_STANDARD("TDSQL-标准版", true, PplDatabaseEnum.TDSQL),

    TDSQL_CLUSTER("TDSQL-集群版", false, PplDatabaseEnum.TDSQL),

    SQLSERVER_SINGLE("SQL Server-单节点", true, PplDatabaseEnum.SqlServer),

    SQLSERVER_DOUBLE("SQL Server-双节点", false, PplDatabaseEnum.SqlServer),

    POSTGRESQL_DOUBLE_HA("PostgreSQL-双机高可用（一主一从）", true, PplDatabaseEnum.PostgreSQL),

    KEEWIDB_CLUSTER("KeeWiDB-集群架构", false, PplDatabaseEnum.KeeWiDB),

    VECTORDB_HA("向量数据库-高可用版", false, PplDatabaseEnum.VectorDB),

    VECTORDB_SINGLE("向量数据库-单机版", true, PplDatabaseEnum.VectorDB),

    CTSDB_STANDARD("CTSDB-标准版", true, PplDatabaseEnum.CTSDB),

    TCAPLUS_CLUSTER("Tcaplus-集群版", false, PplDatabaseEnum.Tcaplus),

    ;

    private String name;

    private boolean singleNode;

    private PplDatabaseEnum database;

    PplDataBaseFrameworkEnum(String name, boolean singleNode, PplDatabaseEnum database)  {
        this.name = name;
        this.singleNode = singleNode;
        this.database = database;
    }

    public static List<String> allNames() {
        List<String> res = new ArrayList<>();
        for (PplDataBaseFrameworkEnum item : PplDataBaseFrameworkEnum.values()) {
            res.add(item.getName());
        }
        return res;
    }

    public static PplDataBaseFrameworkEnum getByName(String name) {
        for (PplDataBaseFrameworkEnum item : PplDataBaseFrameworkEnum.values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static PplDataBaseFrameworkEnum getByNameAndDatabase(String name, PplDatabaseEnum database) {
        if (database == null) {
            return null;
        }
        for (PplDataBaseFrameworkEnum item : PplDataBaseFrameworkEnum.values()) {
            if (item.getDatabase() == database && item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static List<Framework> allFrameworks() {
        List<Framework> res = new ArrayList<>();
        for (PplDataBaseFrameworkEnum item : PplDataBaseFrameworkEnum.values()) {
            res.add(new Framework(item));
        }
        return res;
    }

    @Data
    public static class Framework {
        private String name;

        private boolean singleNode;

        private String database;

        private String alias;

        public Framework(PplDataBaseFrameworkEnum frameworkEnum) {
            this.name = frameworkEnum.getName();
            this.singleNode = frameworkEnum.isSingleNode();
            this.database = frameworkEnum.getDatabase().getName();
            this.alias = frameworkEnum.getDatabase().getAlias();
        }
    }

}
