package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("t_cynosdb_duandaoduan_service_level")
public class CynosdbDuandaoduanServiceLevelDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "fdate")
    private String fdate;

    /** reigion信息，如广州<br/>Column: [regionname] */
    @Column(value = "regionname")
    private String regionname;

    /** 集群id，如广州四区<br/>Column: [zonename] */
    @Column(value = "zonename")
    private String zonename;

    /** 总计算内存容量<br/>Column: [maxSellMem] */
    @Column(value = "maxSellMem")
    private Integer maxSellMem;

    /** 总存储容量<br/>Column: [maxSellDisk] */
    @Column(value = "maxSellDisk")
    private Integer maxSellDisk;

    @Column(value = "minRestMemG")
    private Integer minRestMemG;

    @Column(value = "minRestDiskG")
    private Integer minRestDiskG;

    /** 线上剩余内存量 G<br/>Column: [X1RealRestMem] */
    @Column(value = "X1RealRestMem")
    private Integer x1RealRestMem;

    /** 线上剩余磁盘量，折算的；G<br/>Column: [X1RealRestDiskG] */
    @Column(value = "X1RealRestDiskG")
    private Integer x1RealRestDiskG;

    /** 线上物理机设备数量<br/>Column: [machineCnt] */
    @Column(value = "machineCnt")
    private Integer machineCnt;

    /** 当天发货小于15分钟的实例数量<br/>Column: [Less15MinCnt] */
    @Column(value = "Less15MinCnt")
    private Integer less15MinCnt;

    /** 当天发货实例总数<br/>Column: [totalCnt] */
    @Column(value = "totalCnt")
    private Integer totalCnt;

    /** 发货速度合格百分比,99.99格式,一天内啥都没发货的，填--<br/>Column: [fahuoOKPct] */
    @Column(value = "fahuoOKPct")
    private String fahuoOKPct;

    /** 服务水平<br/>Column: [serviceLevelPct] */
    @Column(value = "serviceLevelPct")
    private String serviceLevelPct;

    @Column(value = "updatetime")
    private LocalDateTime updatetime;


    public DwsTdsqlServiceLevelDataDfDO genTdsqlData(String statTime) {
        DwsTdsqlServiceLevelDataDfDO item = new DwsTdsqlServiceLevelDataDfDO();
        item.setStatTime(statTime);
        item.setZoneCode(zonename);
        item.setMinRestMem(minRestMemG);
        item.setMinRestDisk(minRestDiskG == null ? 0 : minRestDiskG);
        item.setRestMem(x1RealRestMem);
        item.setRestDisk(x1RealRestDiskG);
        item.setSucCnt(totalCnt - less15MinCnt);
        item.setTotalCnt(totalCnt);
        return item;
    }

}
