package cloud.demand.lab.modules.common_dict.service.impl;

import cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.lab.common.config.DynamicProperties;
import cloud.demand.lab.common.entity.UserPermissionDto;
import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.common.utils.Alert;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.ORMUtils.WhereContent;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.BasMrpV2GenerationInstanceTypeDO;
import cloud.demand.lab.modules.common_dict.DO.ErpRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.DO.ObsBizInfo;
import cloud.demand.lab.modules.common_dict.DO.PaasProductUinWhiteListDO;
import cloud.demand.lab.modules.common_dict.DO.ProductInfoDTO;
import cloud.demand.lab.modules.common_dict.DO.ReportConfigGpuTypeDO;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticGinstypeDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.entity.PplGpuRegionZoneDO;
import cloud.demand.lab.modules.common_dict.entity.UserPersonalConfig;
import cloud.demand.lab.modules.common_dict.enums.ComputeTypeEnum;
import cloud.demand.lab.modules.common_dict.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.lab.modules.common_dict.enums.PlanDetailCOSIndicatorEnum;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.model.req.UpdateBizTimeConfigReq;
import cloud.demand.lab.modules.common_dict.model.req.UserPersonalConfigReq;
import cloud.demand.lab.modules.common_dict.model.rsp.UserPersonalConfigResp;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.http.dto.QCloudZoneReq;
import cloud.demand.lab.modules.common_dict.http.dto.QCloudZoneResp;
import cloud.demand.lab.modules.common_dict.http.service.IdcDataHttpService;
import cloud.demand.lab.modules.operation_view.entity.cloud_crs.RedisDevclassConfigDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpBizTimeConfigDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpCommonHolidayWeekDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpCommonHolidayWeekDO.HolidayDate;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpEventNoticeConfigDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandAuthDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.ServerPartsExtendedInfoDO;
import cloud.demand.lab.modules.operation_view.entity.plan.StaticCvmtypeDO;
import cloud.demand.lab.modules.operation_view.entity.plan.StaticStockPrincipalHosttypeDO;
import cloud.demand.lab.modules.operation_view.entity.resource.BasCmdbCityDO;
import cloud.demand.lab.modules.operation_view.entity.resource.BasStrategyDeviceVersionNettypeDO;
import cloud.demand.lab.modules.operation_view.entity.resource.ServerPartsCompositionVO;
import cloud.demand.lab.modules.operation_view.entity.resource.ServerPartsExtendedInfoPDO;
import cloud.demand.lab.modules.operation_view.entity.rrp.ReportConfigCbsStoreNumDO;
import cloud.demand.lab.modules.operation_view.entity.rrp.ReportConfigCdbStoreNumDO;
import cloud.demand.lab.modules.operation_view.entity.rrp.ReportConfigCmongoMemDO;
import cloud.demand.lab.modules.operation_view.entity.rrp.ReportConfigCosStoreNumDO;
import cloud.demand.lab.modules.operation_view.entity.rrp.ReportPlanDetailDO;
import cloud.demand.lab.modules.operation_view.entity.web.dict.QueryCampusAndBizType2ZoneDTO;
import cloud.demand.lab.modules.operation_view.entity.web.dict.QueryCampusAndBizType2ZonePageDTO;
import cloud.demand.lab.modules.operation_view.entity.yunti.BasObsCloudCvmTypeDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandInventoryCostConfigDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthRegionDefaultZoneConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.Constant;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.util.Map.Entry;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.pugwoo.wooutils.collect.SortingUtils.sortDescNullLast;

@Service
@Slf4j
public class DictServiceImpl implements DictService, ApplicationContextAware {

    /**
     * 这里测试环境会被智能网关拦截， 因此这里生产不用加这个配置
     */
    @Value(value = "${tcresCookies:}")
    String tcresCookies;
    private ApplicationContext applicationContext;
    /**
     * 这个用于控制相同的设备仅告警一次
     */
    private Set<String> warnedDeviceTypeForNetType = new HashSet<>();

    @Resource
    private DBHelper cdCommonDbHelper;
    @Resource
    private DBHelper resourcedbDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DBHelper obsDBHelper;
    @Resource
    private DBHelper crsDBHelper;
    @Resource
    private DBHelper resplanDBHelper;
    @Resource
    private DBHelper erpBakDBHelper;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private Alert alert;

    @Resource
    private CvmPlanService cvmPlanService;

    @Resource
    private IdcDataHttpService idcDataHttpService;

    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, StaticCvmtypeDO> getAllStaticCvmTypes() {
        List<StaticCvmtypeDO> cvmTypes = cdCommonDbHelper.getAll(StaticCvmtypeDO.class);
        return ListUtils.toMap(cvmTypes, o -> o.getCvmtype(), o -> o);
    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, ServerPartsExtendedInfoPDO> getAllErpDeviceInfo() {
        List<ServerPartsExtendedInfoPDO> cvmTypes =
                resourcedbDBHelper.getAll(ServerPartsExtendedInfoPDO.class,
                        "where default_flag = 1 and gpu_number > 0");
        return ListUtils.toMap(cvmTypes, o -> o.getDeviceType(), o -> o);
    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    public Map<String, StaticGinstypeDO> getAllInstanceTypes() {
        List<StaticGinstypeDO> types = cdCommonDbHelper.getAll(StaticGinstypeDO.class);
        return ListUtils.toMap(types, o -> o.getGinstype(), o -> o);
    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCbsStore() {
        List<ReportConfigCbsStoreNumDO> all = rrpDBHelper.getAll(ReportConfigCbsStoreNumDO.class);
        return ListUtils.toMap(all, ReportConfigCbsStoreNumDO::getDeviceType,
                ReportConfigCbsStoreNumDO::getStoreNum);
    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCdbStore() {
        List<ReportConfigCdbStoreNumDO> all = rrpDBHelper.getAll(ReportConfigCdbStoreNumDO.class);
        return ListUtils.toMap(all, ReportConfigCdbStoreNumDO::getDeviceType,
                ReportConfigCdbStoreNumDO::getMaxCanSaleCap);
    }

    /**
     * cdb单机内存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCdbMemCap() {
        List<ReportConfigCdbStoreNumDO> all = rrpDBHelper.getAll(ReportConfigCdbStoreNumDO.class);
        return ListUtils.toMap(all, ReportConfigCdbStoreNumDO::getDeviceType,
                ReportConfigCdbStoreNumDO::getMemCap);
    }

    /**
     * crs逻辑数
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCrsMemCap() {
        List<RedisDevclassConfigDO> all = crsDBHelper.getAll(RedisDevclassConfigDO.class);
        return ListUtils.toMap(all, o -> o.getDevClass(),
                o -> (o.getMemSize() == null ? BigDecimal.ZERO : AmountUtils.divideScale6(o.getMemSize(), 1024)));
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCMongoMemCap() {
        List<ReportConfigCmongoMemDO> all = rrpDBHelper.getAll(ReportConfigCmongoMemDO.class);
        return ListUtils.toMap(all, o -> o.getDeviceType(),
                o -> (o.getMaxCanUseMem() == null ? BigDecimal.ZERO : o.getMaxCanUseMem()));
    }

    /**
     * Cmongo逻辑数
     */
    @Override
    public BigDecimal getCmongoDeviceMem(String deviceType) {
        if (Strings.isBlank(deviceType)) {
            return BigDecimal.ZERO;
        }
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCMongoMemCap();
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            if (deviceType.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        return BigDecimal.ZERO;

    }

    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, BigDecimal> getAllCosStore() {
        List<ReportConfigCosStoreNumDO> all = rrpDBHelper.getAll(ReportConfigCosStoreNumDO.class);
        return ListUtils.toMap(all, o ->
                        (StringUtils.isBlank(o.getPlanProduct()) ? "" : o.getPlanProduct())
                                + (StringUtils.isBlank(o.getDeviceType()) ? "" : o.getDeviceType()),
                ReportConfigCosStoreNumDO::getStoreNum);
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, String> getRegionNameMap() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getRegionName, StaticZoneDO::getApiRegion, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, String> getRegion2CustomhouseTitleMap() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getRegionName, StaticZoneDO::getCustomhouseTitle,
                        (v1, v2) -> v1));
    }

    @Override
    public Map<String, String> getZoneToCountryMap() {
        Map<String, String> zoneName2RegionName = getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> ret = new HashMap<>();
        for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
            String zone = entry.getKey();
            String region = entry.getValue();
            SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
            if (regionDO != null) {
                ret.put(zone, regionDO.getCountryName());
            }
        }
        return ret;
    }

    @Override
    @HiSpeedCache(expireSecond = 600, keyScript = "args[0]")
    public List<String> getRegionNameByCustomhouseTitle(String customhouseTitle) {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class, "where customhouse_title = ?",
                customhouseTitle);
        return all.stream().map(StaticZoneDO::getRegionName).distinct().collect(Collectors.toList());
    }


    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, String> getRegionMap() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getApiRegion, StaticZoneDO::getRegionName, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public List<String> queryRegionNameList() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream().map(StaticZoneDO::getRegionName).distinct().collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, String> getZoneNameMap() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getZoneName, StaticZoneDO::getZone, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600, cloneReturn = false)
    @SynchronizedHiSpeedCache1Second
    public Map<String, String> getZoneName2RegionName() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getZoneName, StaticZoneDO::getRegionName, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, String> getZoneMap() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream()
                .collect(Collectors.toMap(StaticZoneDO::getZone, StaticZoneDO::getZoneName, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public List<String> queryZoneNameList() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream().map(StaticZoneDO::getZoneName).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> queryZoneNameList(String regionName) {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        return all.stream().filter(v -> v.getRegionName().equals(regionName)).map(StaticZoneDO::getZoneName)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> queryRegionNameList(String areaName) {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        if (Strings.equals(areaName, "domestic")) {
            all = ListUtils.filter(all, (o) -> Strings.equals("境内", o.getCustomhouseTitle()));
        }
        if (Strings.equals(areaName, "oversea")) {
            all = ListUtils.filter(all, (o) -> Strings.equals("境外", o.getCustomhouseTitle()));
        }
        return all.stream().map(StaticZoneDO::getRegionName)
                .distinct().collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<Long, StaticZoneDO> getAllPlanZoneInfos() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<Long, StaticZoneDO> map = ListUtils.toMap(all, o -> o.getZoneid(), o -> o);
        return map;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByName() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> map = ListUtils.toMap(all, o -> o.getZoneName(), o -> o);
        return map;
    }

    @Override
    public Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegionName() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> map = ListUtils.toMap(all, o -> o.getRegionName(), o -> o);
        return map;
    }

    @Override
    public Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegion() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> map = ListUtils.toMap(all, StaticZoneDO::getApiRegion, o -> o);
        return map;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByCode() {
        List<StaticZoneDO> all = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> map = ListUtils.toMap(all, o -> o.getZone(), o -> o);
        return map;
    }

    @Override
    public StaticZoneDO getStaticZoneInfoById(Long zoneId) {
        if (zoneId == null) {
            return null;
        }
        return SpringUtil.getBean(DictServiceImpl.class).getAllPlanZoneInfos().get(zoneId);
    }

    @Override
    public StaticZoneDO getStaticZoneInfoByName(String zoneName) {
        if (Strings.isBlank(zoneName)) {
            return null;
        }
        return SpringUtil.getBean(DictServiceImpl.class).getAllPlanZoneInfosGroupByName().get(zoneName);
    }

    @Override
    public StaticZoneDO getStaticZoneInfoByCampus(String campus) {
        if (StringTools.isBlank(campus)) {
            return null;
        }
        Map<String, StaticZoneDO> map = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        return map.get(campus);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<StaticZoneDO> getAllZoneInfos() {
        return cdCommonDbHelper.getAll(StaticZoneDO.class);
    }

    @Override
    public StaticZoneDO getStaticZoneInfoByModule(String moduleName) {
        if (StringTools.isBlank(moduleName)) {
            return null;
        }
        Map<String, Long> map = SpringUtil.getBean(DictServiceImpl.class).getZoneIdByModuleName();
        Long zoneId = map.get(moduleName);
        if (zoneId == null) {
            return null;
        }
        return SpringUtil.getBean(DictServiceImpl.class).getAllPlanZoneInfos().get(zoneId);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, Integer> getDeviceLogicCpuCore() {
        List<ServerPartsExtendedInfoPDO> all = resourcedbDBHelper.getAll(ServerPartsExtendedInfoPDO.class,
                "where default_flag=1");
        return ListUtils.toMap(all, ServerPartsExtendedInfoPDO::getDeviceType,
                ServerPartsExtendedInfoPDO::getCpuLogicCore);
    }

    /**
     * 物理机设备类型获取机型族 物理机类型-机型族
     */
    @HiSpeedCache(expireSecond = 300, continueFetchSecond = 3600, cloneReturn = false)
    @Override
    public Map<String, String> getDeviceFamilyMap() {
        List<Map> types = resourcedbDBHelper.getRaw(Map.class, "select name,DeviceFamilyName from bas_stratege_device_type");
        return ListUtils.toMap(types, o -> o.get("name").toString(), o -> o.get("DeviceFamilyName").toString());
    }


    @Override
    public Integer getDeviceLogicCpuCore(String deviceType, boolean matchPrefix) {
        if (StringTools.isBlank(deviceType)) {
            return 0;
        }

        Map<String, Integer> map = SpringUtil.getBean(DictServiceImpl.class).getDeviceLogicCpuCore();
        Integer core = map.get(deviceType);
        if (core != null) {
            return core;
        }

        if (matchPrefix) { // 处理前缀匹配
            List<String> deviceTypes = new ArrayList<>();
            for (Map.Entry<String, Integer> e : map.entrySet()) {
                if (deviceType.startsWith(e.getKey())) {
                    deviceTypes.add(e.getKey());
                }
            }
            if (deviceTypes.isEmpty()) {
                return 0;
            }
            ListUtils.sortDescNullLast(deviceTypes, String::length);
            return map.get(deviceTypes.get(0));
        }

        return 0;
    }

    @Override
    public Integer getDeviceGpuCard(String deviceType) {
        if (StringTools.isBlank(deviceType)) {
            return 0;
        }

        Map<String, StaticCvmtypeDO> cvmTypes = SpringUtil.getBean(DictServiceImpl.class).getAllStaticCvmTypes();
        //  erp的gpu设备字典表
        Map<String, ServerPartsExtendedInfoPDO> erpTypes = SpringUtil.getBean(DictServiceImpl.class)
                .getAllErpDeviceInfo();

        //  如果腾讯云字典表有，优先取腾讯云的，没有再去erp取，如果还没有为0
        StaticCvmtypeDO cvmType = cvmTypes.get(deviceType);
        if (cvmType == null) {
            ServerPartsExtendedInfoPDO pdo = erpTypes.get(deviceType);
            if (pdo == null) {
                return 0;
            }
            return pdo.getGpuNumber();
        }
        return cvmType.getGpucard() == null ? 0 : cvmType.getGpucard().intValue();
    }

    @Override
    public BigDecimal getDeviceCbsStore(String deviceType) {
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCbsStore();
        BigDecimal store = map.get(deviceType);
        if (store != null) {
            return NumberUtils.divide(store, 1024, 6);
        } else {
            BigDecimal aDefault = map.get("DEFAULT");
            if (aDefault != null) {
                return NumberUtils.divide(aDefault, 1024, 6);
            } else {
                return NumberUtils.divide(3, 1024, 6);
            }
        }
    }

    @Override
    public BigDecimal getDeviceCdbStore(String deviceType) {
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCdbStore();
        BigDecimal store = map.get(deviceType);
        if (store == null) {
            log.error("CDB线下库存中存在机型:{}找不到对应的GB映射", deviceType);
            return BigDecimal.ZERO;
        }
        return NumberUtils.divide(store, 1024, 6);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, BasCmdbCityDO> getBasCmdbCityInfoMap() {
        List<BasCmdbCityDO> cmdbCityDOS = resourcedbDBHelper.getAll(BasCmdbCityDO.class);
        return ListUtils.toMap(cmdbCityDOS, o -> o.getCityName(), o -> o);
    }

    @Override
    public BigDecimal getDeviceCdbMemCap(String deviceType) {
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCdbMemCap();
        BigDecimal store = map.get(deviceType);
        if (store == null) {
            log.error("CDB线下库存中存在机型:{}找不到对应的单机内存GB映射", deviceType);
            return BigDecimal.ZERO;
        }
        return NumberUtils.divide(store, 1024, 6);
    }

    /**
     * 这里做了GB->TB的处理
     */
    @Override
    public BigDecimal getDeviceCrsMemCap(String deviceType) {
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCrsMemCap();
        BigDecimal cap = map.get(deviceType);
        return cap == null ? BigDecimal.ZERO : cap;
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    public CosRedundancyDTO getCosRedundancy() {
        List<ReportPlanDetailDO> redundancy = rrpDBHelper.getAll(ReportPlanDetailDO.class,
                "where product_type=? AND indicator_code=?",
                ProductTypeEnum.COS.getCode(), PlanDetailCOSIndicatorEnum.AVG_REDUNDANCY.getCode());

        CosRedundancyDTO dto = new CosRedundancyDTO();
        dto.setCn(ListUtils.toMap(ListUtils.filter(redundancy, o -> "境内".equals(o.getCustomhouseTitle())),
                o -> o.getStatTime(), o -> o.getLogicNum()));
        dto.setOversea(ListUtils.toMap(ListUtils.filter(redundancy, o -> "境外".equals(o.getCustomhouseTitle())),
                o -> o.getStatTime(), o -> o.getLogicNum()));
        dto.setCnAvg(NumberUtils.avg(dto.getCn().values(), 6));
        dto.setOverseaAvg(NumberUtils.avg(dto.getOversea().values(), 6));

        return dto;
    }

    @Override
    public BigDecimal getDeviceCosStore(String deviceType, Date date, boolean isCN) {
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCosStore();
        BigDecimal store = map.get(deviceType);
        if (store == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal pb = NumberUtils.divide(store, 1024, 6);

        // 除以冗余度
        CosRedundancyDTO redundancyDTO = SpringUtil.getBean(DictServiceImpl.class).getCosRedundancy();
        BigDecimal redundancy = null;
        if (isCN) {
            BigDecimal r = redundancyDTO.getCn().get(date);
            if (r == null) { // 尝试取前一天的
                r = redundancyDTO.getCn().get(DateUtils.addTime(date, Calendar.DATE, -1));
            }
            redundancy = r == null ? redundancyDTO.getCnAvg() : r;
        } else {
            BigDecimal r = redundancyDTO.getOversea().get(date);
            if (r == null) { // 尝试取前一天的
                r = redundancyDTO.getOversea().get(DateUtils.addTime(date, Calendar.DATE, -1));
            }
            redundancy = r == null ? redundancyDTO.getOverseaAvg() : r;
        }

        if (redundancy != null) {
            pb = NumberUtils.divide(pb, redundancy, 6);
        }

        return pb;
    }

    @Override
    public BigDecimal getDeviceCosStore(String planProduct, String deviceType) {
        BigDecimal pb = BigDecimal.ZERO;
        if (StringUtils.isBlank(planProduct) && StringUtils.isBlank(deviceType)) {
            return pb;
        }
        Map<String, BigDecimal> map = SpringUtil.getBean(DictServiceImpl.class).getAllCosStore();
        BigDecimal store = map.get(
                (StringUtils.isBlank(planProduct) ? "" : planProduct) + (StringUtils.isBlank(deviceType) ? ""
                        : deviceType));
        if (store == null) {
            return pb;
        }
        pb = NumberUtils.divide(store, 1024, 6);
        return pb;
    }

    @Override
    public Integer getPlanDeviceLogicCore(String deviceType) {
        if (StringTools.isBlank(deviceType)) {
            return 0;
        }

        Map<String, StaticCvmtypeDO> cvmTypes = SpringUtil.getBean(DictServiceImpl.class).getAllStaticCvmTypes();
        StaticCvmtypeDO cvmType = cvmTypes.get(deviceType);
        if (cvmType != null) {
            return cvmType.getCpuPhysic() == null ? 0 : new BigDecimal(cvmType.getCpuPhysic()).intValue();
        }

        return 0;
    }

    @Override
    public String getComputeType(String deviceType) {
        if (StringTools.isBlank(deviceType)) {
            return "";
        }

        Integer deviceGpuCard = getDeviceGpuCard(deviceType);
        return deviceGpuCard > 0 ? ComputeTypeEnum.GPU.getCode() : ComputeTypeEnum.CPU.getCode();
    }

    @Override
    public String getCvmComputeType(String instanceType) {
        if (StringTools.isBlank(instanceType)) {
            return "";
        }

        Map<String, StaticGinstypeDO> type = SpringUtil.getBean(DictServiceImpl.class).getAllInstanceTypes();
        StaticGinstypeDO staticGinstypeDO = type.get(instanceType);
        if (staticGinstypeDO != null) {
            return staticGinstypeDO.getGpu() > 0 ? ComputeTypeEnum.GPU.getCode() : ComputeTypeEnum.CPU.getCode();
        }

        return ComputeTypeEnum.CPU.getCode(); // 默认CPU
    }

    @Override
    public Integer getInstanceCoreNum(String instanceModel) {
        if (StringTools.isBlank(instanceModel)) {
            return 0;
        }

        Map<String, StaticGinstypeDO> type = SpringUtil.getBean(DictServiceImpl.class).getAllInstanceTypes();
        StaticGinstypeDO staticGinstypeDO = type.get(instanceModel);
        if (staticGinstypeDO == null || staticGinstypeDO.getCpu() == null) {
            return 0;
        }
        return (int) (staticGinstypeDO.getCpu() / 100);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, ServerPartsCompositionVO> getAllServerPartsCompositions() {
        List<ServerPartsCompositionVO> list = resourcedbDBHelper.getAll(ServerPartsCompositionVO.class,
                "where default_flag=1");
        return ListUtils.toMap(list, ServerPartsCompositionVO::getDeviceType, o -> o);
    }

    @Override
    public ServerPartsCompositionVO getServerPartsCompositionByDeviceType(String deviceType) {
        ServerPartsCompositionVO result = null;
        if (StringUtils.isNotBlank(deviceType)) {
            DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
            Map<String, ServerPartsCompositionVO> map = bean.getAllServerPartsCompositions();
            //  腾讯云机型有_后缀，这里先通过带后缀的版本去找，如果没有则按第一个下划线之前的机型类型去找
            result = map.get(deviceType);
            if (result == null) {
                result = map.get(deviceType.split("_")[0]);
            }
        }
        return result;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, BasStrategyDeviceVersionNettypeDO> getAllNetTypes() {
        List<BasStrategyDeviceVersionNettypeDO> all = resourcedbDBHelper.getAll(
                BasStrategyDeviceVersionNettypeDO.class);
        Map<String, List<BasStrategyDeviceVersionNettypeDO>> map = ListUtils.groupBy(all, o -> o.getDeviceType());
        Map<String, BasStrategyDeviceVersionNettypeDO> result = new HashMap<>();
        for (Map.Entry<String, List<BasStrategyDeviceVersionNettypeDO>> entry : map.entrySet()) {
            List<BasStrategyDeviceVersionNettypeDO> value = entry.getValue();
            // 按最大的网卡优先
            sortDescNullLast(value,
                    o -> NumberUtils.parseInt(RegexUtils.getFirstMatchStr(o.getDeviceNetType(), "\\d+")));
            result.put(entry.getKey(), value.get(0));
        }
        return result;
    }

    @Override
    public BasStrategyDeviceVersionNettypeDO getNetTypeByDeviceType(String deviceType) {
        BasStrategyDeviceVersionNettypeDO deviceInfo = null;
        //  mark：这里先按照最大的网卡类型走
        if (StringUtils.isNotBlank(deviceType)) {
            DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
            deviceInfo = bean.getAllNetTypes().get(deviceType);
            if (deviceInfo == null) {
                deviceInfo = bean.getAllNetTypes().get(deviceType.split("_")[0]);
                if (deviceInfo == null) {
                    if (!warnedDeviceTypeForNetType.contains(deviceType)) {
                        log.error("该机型：{}没有对应的设备类型网络类型数据", deviceType);
                        warnedDeviceTypeForNetType.add(deviceType);
                    }
                }
            }
        }
        return deviceInfo;
    }


    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, CloudDemandCsigDeviceExtendInfoDO> getAllCsigDeviceExtendInfos() {
        List<CloudDemandCsigDeviceExtendInfoDO> all = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        return ListUtils.toMap(all, CloudDemandCsigDeviceExtendInfoDO::getDeviceType, o -> o);
    }

    @Override
    public CloudDemandCsigDeviceExtendInfoDO getCsigDeviceExtendInfoByDeviceType(String deviceType) {
        if (StringUtils.isNotBlank(deviceType)) {
            DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
            Map<String, CloudDemandCsigDeviceExtendInfoDO> map = bean.getAllCsigDeviceExtendInfos();
            return map.get(deviceType);
        }
        return null;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, String> getCsigDeviceTypeToInstanceTypeMap() {
        List<CloudDemandCsigDeviceExtendInfoDO> all = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        return ListUtils.toMap(all, CloudDemandCsigDeviceExtendInfoDO::getDeviceType,
                CloudDemandCsigDeviceExtendInfoDO::getInstanceTypeEng);
    }

    @Override
    public String getCsigInstanceTypeByDeviceType(String deviceType) {
        DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
        String instanceType = bean.getCsigDeviceTypeToInstanceTypeMap().get(deviceType);

//        if (instanceType == null) {
//            throw BizException.makeThrow("找不到设备类型到实例类型的映射关系(设备类型：" + deviceType + ")，请联系 faiywang 配置。");
//        }

        return instanceType;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, List<String>> getCsigInstanceTypeToDeviceTypeMap() {
        List<CloudDemandCsigDeviceExtendInfoDO> all = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        return ListUtils.toMapList(all, o -> o.getInstanceTypeEng(), o -> o.getDeviceType());
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, keyScript = "args[0]")
    public List<String> getCsigDeviceTypeByInstanceType(String instanceType) {
        DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
        List<String> deviceTypes = bean.getCsigInstanceTypeToDeviceTypeMap().get(instanceType);

//        if (deviceTypes == null || deviceTypes.isEmpty()) {
//            throw BizException.makeThrow("找不到实例类型到设备类型的映射关系(实例类型" + instanceType + ")，请联系 faiywang 配置。");
//        }

        if (deviceTypes == null) {
            deviceTypes = new ArrayList<>();
        }

        return deviceTypes;
    }

    @Override
    public List<String> getCsigDeviceTypeByInstanceType(List<String> instanceTypes) {
        List<String> deviceTypes = new ArrayList<>();
        DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
        for (String instanceType : instanceTypes) {
            deviceTypes.addAll(bean.getCsigDeviceTypeByInstanceType(instanceType));
        }
        return deviceTypes;
    }

    @Override
    public String getDeviceFamilyByDeviceType(String deviceType) {
        if (StringUtils.isNotBlank(deviceType)) {
            DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
            Map<String, String> map = bean.getInfoByDeviceType();
            return map.get(deviceType);
        }
        return "";
    }

    @Override
    public String getDeviceTypeByDeviceFamily(String deviceFamily) {
        if (StringUtils.isNotBlank(deviceFamily)) {
            DictServiceImpl bean = applicationContext.getBean(DictServiceImpl.class);
            Map<String, String> map = bean.getInfoByDeviceFamily();
            return map.get(deviceFamily);
        }
        return "";
    }


    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, String> getInfoByDeviceType() {
        List<StaticCvmtypeDO> all = cdCommonDbHelper.getAll(StaticCvmtypeDO.class);
        if (ListUtils.isEmpty(all)) {
            taskLogService.genRunWarnLog("", "", "static_cvmtype配置表为空");
            return new HashMap<>();
        }
        Map<String, String> cvm2GinsFamily = ListUtils.toMap(all, o -> o.getCvmtype(), o -> o.getGinsfamily());

        //  取字典表中stdType和ginsFamily均不为空的记录
        List<StaticCvmtypeDO> filtered = all.stream().filter(o -> StringTools.isNotBlank(o.getStdtype())
                && StringTools.isNotBlank(o.getGinsfamily())).collect(Collectors.toList());

        //  stdType ： cvmType 为一对多
        Map<String, String> std2GinsFamily = ListUtils.toMap(filtered, o -> o.getStdtype(), o -> o.getGinsfamily());

        //  stdTypeMap遍历
        //  若发现cvmTypeMap中的k存在，但v为空 或 k不存在时，将它put到cvmTypeMap中
        for (Map.Entry<String, String> entry : std2GinsFamily.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String cvmTypeValue = cvm2GinsFamily.get(key);
            if (StringTools.isBlank(cvmTypeValue)) {
                cvm2GinsFamily.put(key, value);
            }
        }
        return cvm2GinsFamily;
    }


    /**
     * 内部方法做缓存
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, String> getInfoByDeviceFamily() {
        List<StaticCvmtypeDO> all = cdCommonDbHelper.getAll(StaticCvmtypeDO.class);
        if (ListUtils.isEmpty(all)) {
            taskLogService.genRunWarnLog("", "", "static_cvmtype配置表为空");
            return new HashMap<>();
        }
        Map<String, List<StaticCvmtypeDO>> map = ListUtils.groupBy(all, o -> o.getGinsfamily());
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, List<StaticCvmtypeDO>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<StaticCvmtypeDO> value = entry.getValue();
            if (StringTools.isBlank(key) || ListUtils.isEmpty(value)) {
                continue;
            }
            result.put(key, value.get(0).getCvmtype());
        }
        return result;
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, ReportConfigGpuTypeDO> loadGpuCardConfig() {
        List<ReportConfigGpuTypeDO> config = rrpDBHelper.getAll(ReportConfigGpuTypeDO.class);
        if (ListUtils.isEmpty(config)) {
            taskLogService.genRunWarnLog("", "", "GPU卡类配置表还没有数据，请配置");
            return null;
        }
        return ListUtils.toMap(config, ReportConfigGpuTypeDO::getDeviceType, o -> o);
    }


    @Override
    public List<Integer> getCsigDeptId() {
        String sql = "select DeptId\n"
                + "from bas_obs_business_dept a\n"
                + "         left join bas_obs_bg b on a.BgId = b.BgId\n"
                + "where  a.EnableFlag = 1\n"
                + "  and b.EnableFlag = 1\n"
                + "  and b.EnableFlag = 1 and (b.BgType = 2 or b.BgShortName='CSIG')";
        return obsDBHelper.getRaw(Integer.class, sql);
    }


    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, ObsBizInfo> getBizInfoByPlanProductName() {
        String sql = "select 0         business1Id, "
                + "       ''       business1Name, "
                + "       0            business2Id, "
                + "       ''          business2Name, "
                + "       0            business3Id, "
                + "       ''          business3Name, "
                + "       p.ProductId        productId, "
                + "       p.ProductName      productName, "
                + "       pp.PlanProductId   planProductId, "
                + "       pp.PlanProductName planProductName, "
                + "       d.DeptId           deptId, "
                + "       d.DeptName         deptName, "
                + "       bg.BgId            bgId, "
                + "       bg.BgName          bgName, "
                + "       bg.BgShortName     bgShortName "
                + "       from bas_obs_product p "
                + "         left join bas_obs_plan_product pp on pp.PlanProductId = p.PlanProductId "
                + "         left join bas_obs_business_dept d on d.DeptId = pp.VirtualDeptId "
                + "         left join bas_obs_bg bg on bg.BgId = d.BgId "
                + "where  p.EnableFlag = 1 "
                + "  and pp.EnableFlag = 1 "
                + "  and d.EnableFlag = 1 "
                + "  and bg.EnableFlag = 1";
        List<ObsBizInfo> allData = obsDBHelper.getRaw(ObsBizInfo.class, sql);
        return ListUtils.toMap(allData, ObsBizInfo::getPlanProductName, o -> o);
    }


    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    public Map<String, ProductInfoDTO> getProductInfoByProductName() {
        String sql = ORMUtils.getSql("/sql/obs/get_all_product_info.sql");
        List<ProductInfoDTO> all = obsDBHelper.getRaw(ProductInfoDTO.class, sql);
        return ListUtils.toMap(all, ProductInfoDTO::getProductName, o -> o);
    }

    @Override
    public Map<String, BasObsCloudCvmTypeDO> queryInstanceModelInfos() {
        List<BasObsCloudCvmTypeDO> all = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class);
        return ListUtils.toMap(all, BasObsCloudCvmTypeDO::getCvmInstanceModel, (o) -> o);
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1200)
    @Override
    public Map<String, List<String>> queryTypeToInstanceModel() {
        List<BasObsCloudCvmTypeDO> all = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class);
        return all.stream().collect(Collectors.groupingBy(BasObsCloudCvmTypeDO::getCvmInstanceTypeCode,
                Collectors.mapping(BasObsCloudCvmTypeDO::getCvmInstanceModel, Collectors.toList())));
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800)
    @Override
    public List<IndustryCustomerInfo> getIndustryCustomerInfoList() {
        String sql = "SELECT customer_short_name,war_zone,industry_dept from std_crp.dwd_txy_appid_info_cf\n"
                + "WHERE customer_short_name != '(空值)'\n"
                + "GROUP BY customer_short_name,war_zone,industry_dept";
        List<IndustryCustomerInfo> raw = ckcldStdCrpDBHelper.getRaw(IndustryCustomerInfo.class, sql);
        List<IndustryDemandIndustryWarZoneDictDO> zoneDictDOS = queryEnableIndustryWarZoneCustomerConfig();
        Map<String, IndustryDemandIndustryWarZoneDictDO> customerMap = ListUtils.toMap(
                zoneDictDOS, IndustryDemandIndustryWarZoneDictDO::getCustomerName, item -> item);
        for (IndustryCustomerInfo simpleCustomerInfo : raw) {
            String customerShortName = simpleCustomerInfo.getCustomerShortName();
            IndustryDemandIndustryWarZoneDictDO industryDemandIndustryWarZoneDictDO = customerMap.get(
                    customerShortName);
            if (industryDemandIndustryWarZoneDictDO != null) {
                simpleCustomerInfo.setWarZone(industryDemandIndustryWarZoneDictDO.getWarZoneName());
                simpleCustomerInfo.setUnCustomerShortName(industryDemandIndustryWarZoneDictDO.getCommonCustomerName());
            } else {
                simpleCustomerInfo.setUnCustomerShortName(simpleCustomerInfo.getCustomerShortName());
            }
        }
        return raw;
    }

    @HiSpeedCache(expireSecond = 600)
    @Override
    public Map<String, List<String>> queryWarZoneToCustomerShortName() {
        List<IndustryCustomerInfo> industryCustomerInfoList = getIndustryCustomerInfoList();
        return industryCustomerInfoList.stream().collect(Collectors.groupingBy(
                IndustryCustomerInfo::getWarZone,
                Collectors.mapping(IndustryCustomerInfo::getUnCustomerShortName, Collectors.toList())
        ));
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1200)
    public Map<String, String> queryInstanceTypeToGroup() {
        List<BasObsCloudCvmTypeDO> all = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class, "group by CvmInstanceTypeCode");
        return all.stream()
                .collect(Collectors.toMap(BasObsCloudCvmTypeDO::getCvmInstanceTypeCode,
                        BasObsCloudCvmTypeDO::getCvmInstanceGroup));
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1200)
    public Map<String, List<String>> queryGroupToInstanceType() {
        List<BasObsCloudCvmTypeDO> all = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class, "group by CvmInstanceTypeCode");
        return all.stream()
                .collect(Collectors.groupingBy(BasObsCloudCvmTypeDO::getCvmInstanceGroup,
                        Collectors.mapping(BasObsCloudCvmTypeDO::getCvmInstanceTypeCode, Collectors.toList())));
    }

    @Override
    public String getInstanceGroupByInstanceType(String instanceType) {
        DictService dictService = SpringUtil.getBean(DictService.class);
        Map<String, String> map = dictService.queryInstanceTypeToGroup();
        return map.get(instanceType);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1200)
    public List<String> getNewGenerationInstanceType() {
        // 获取采购机型
        List<BasMrpV2GenerationInstanceTypeDO> all = demandDBHelper.getAll(BasMrpV2GenerationInstanceTypeDO.class,
                "where default_flag = '1' and generation = '新机型'");
        return all.stream().map(BasMrpV2GenerationInstanceTypeDO::getInstanceType).collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, Long> getZoneIdByCampusName() {

        //todo
        String url = "http://api.tcres.woa.com/api/module/get_area_region_zone_campus";

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("staffName", "fireflychen");
        if (Strings.isNotBlank(tcresCookies)) {
            headers.add("cookie", tcresCookies);
        }

        ImmutableMap<String, Object> params = ImmutableMap.of("params", ImmutableMap.of("", ""));

        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            String responseString = restTemplate.postForObject(url, requestEntity, String.class);
            QueryCampus2ZoneIdDTO parse = JSON.parse(responseString, QueryCampus2ZoneIdDTO.class);

            if (parse == null || parse.getCode() != 0) {
                throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", parse, url);
            }

            // 这里空指针报错
            List<QueryCampus2ZoneIdDTO.Data.Item> items = parse.getData().getItems();

            HashMap<String, Long> ret = new HashMap<>();
            for (QueryCampus2ZoneIdDTO.Data.Item item : items) {
                List<String> campus = item.getCampus();
                Long zoneId = item.getZoneId();
                for (String s : campus) {
                    ret.put(s, zoneId);
                }
            }
            return ret;
        } catch (Exception e) {
            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
        }
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, String> getCampusByZone() {

        // 8-10 更换为yunti数据源
        List<YuntiStategyZoneDO> all = yuntiDBHelper.getAll(YuntiStategyZoneDO.class);
        Map<String, String> collect = all.stream()
                .collect(Collectors.toMap(YuntiStategyZoneDO::getZone, YuntiStategyZoneDO::getCampusName));
        return collect;
        //todo
//        String url = "http://api.tcres.woa.com/api/module/get_area_region_zone_campus";
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Content-Type", "application/json");
//        headers.add("staffName", "fireflychen");
//        if (Strings.isNotBlank(tcresCookies)) {
//            headers.add("cookie", tcresCookies);
//        }
//
//        ImmutableMap<String, Object> params = ImmutableMap.of("params", ImmutableMap.of("", ""));
//
//        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
//
//        try {
//            RestTemplate restTemplate = new RestTemplate();
//            String responseString = restTemplate.postForObject(url, requestEntity, String.class);
//            QueryCampus2ZoneIdDTO parse = JSON.parse(responseString, QueryCampus2ZoneIdDTO.class);
//
//            if (parse == null || parse.getCode() != 0) {
//                throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", parse, url);
//            }
//
//            // 这里空指针报错
//            List<QueryCampus2ZoneIdDTO.Data.Item> items = parse.getData().getItems();
//
//            HashMap<String, List<String>> ret = new HashMap<>();
//            for (QueryCampus2ZoneIdDTO.Data.Item item : items) {
//                List<String> campus = item.getCampus();
//                ret.put(item.getZoneEn(), campus);
//            }
//            return ret;
//        } catch (Exception e) {
//            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
//        }
    }

    /**
     * module和可用区的关系一对一，这里替换掉
     */
    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, Long> getZoneIdByModuleName() {
        log.info("开始调用get_module方法");
        String url = "http://api.tcres.woa.com/api/module/get_module";

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("staffName", "fireflychen");
        if (Strings.isNotBlank(tcresCookies)) {
            headers.add("cookie", tcresCookies);
        }

        //  这里指定要传入这个参数，才能拿到全量的数据
        ImmutableMap<String, Object> params = ImmutableMap.of("params", ImmutableMap.of("filter_accepted", 2));

        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            String responseString = restTemplate.postForObject(url, requestEntity, String.class);
            QueryModule2ZoneIdDTO parse = JSON.parse(responseString, QueryModule2ZoneIdDTO.class);

            if (parse == null || parse.getCode() != 0) {
                throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", parse, url);
            }

            // 这里空指针报错
            List<QueryModule2ZoneIdDTO.Item> items = parse.getData();

            HashMap<String, Long> ret = new HashMap<>();
            for (QueryModule2ZoneIdDTO.Item item : items) {
                String module = item.getModule();
                Long zoneId = item.getZoneId();
                ret.put(module, zoneId);
            }
            return ret;
        } catch (Exception e) {
            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
        }
    }

    @Override
    public Map<String, BigDecimal> getProductUnitPrice() {
        List<CloudDemandInventoryCostConfigDO> all = yuntiDBHelper.getAll(CloudDemandInventoryCostConfigDO.class);
        return ListUtils.toMap(all, o -> o.getProductType(), o -> o.getUnitPrice());
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, StaticStockPrincipalHosttypeDO> getAllGoodDeviceType() {
        return ListUtils.toMap(cdCommonDbHelper.getAll(StaticStockPrincipalHosttypeDO.class), o -> o.getHosttype(), o -> o);
    }

    @Override
    @HiSpeedCache
    public List<TxyRegionInfoDTO> getAllTxyRegionInfo() {
        String sql = "select distinct customhouse_title, area_name, region ,region_name, zone_name from static_zone";
        List<TxyRegionInfoDTO> raw = cdCommonDbHelper.getRaw(TxyRegionInfoDTO.class, sql);
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        for (TxyRegionInfoDTO txyRegionInfoDTO : raw) {
            SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(txyRegionInfoDTO.getRegionName());
            if (soeRegionNameCountryDO != null) {
                txyRegionInfoDTO.setCountry(soeRegionNameCountryDO.getCountryName());
            } else {
                if (txyRegionInfoDTO.getCustomhouseTitle().equals("境内")) {
                    txyRegionInfoDTO.setCountry("中国内地");
                }
            }
        }
        return raw;
    }

    @Override
    @HiSpeedCache(expireSecond = 600)
    public List<PplGpuRegionZoneDO> queryGpuInstanceList() {
        return demandDBHelper.getAll(PplGpuRegionZoneDO.class, "where gpu_type is not null");
    }

    @HiSpeedCache(expireSecond = 600)
    @Override
    public Map<Integer, TxyRegionInfoDTO> getAllTxyRegionInfoMap() {
        String sql = "select distinct zoneid,customhouse_title, area_name, region_name, zone_name from static_zone";
        List<TxyRegionInfoWithZoneIdDTO> raw = cdCommonDbHelper.getRaw(TxyRegionInfoWithZoneIdDTO.class, sql);
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<Integer, TxyRegionInfoDTO> ret = new HashMap<>();
        for (TxyRegionInfoWithZoneIdDTO item : raw) {
            SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(item.getRegionName());
            if (soeRegionNameCountryDO != null) {
                item.setCountry(soeRegionNameCountryDO.getCountryName());
            } else {
                if (item.getCustomhouseTitle().equals("境内")) {
                    item.setCountry("中国内地");
                }
            }
            ret.put(item.getZoneId(), item.transform());
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 600)
    @Override
    public Map<String, List<String>> getAreaNameToRegionName() {
        return getAllTxyRegionInfo().stream().collect(Collectors.groupingBy(TxyRegionInfoDTO::getAreaName,
                Collectors.mapping(TxyRegionInfoDTO::getRegionName, Collectors.toList())));
    }

    @Override
    public String getCountryChineseByCityName(String cityName) {
        if (StringTools.isBlank(cityName)) {
            return "";
        }
        if (Objects.equals(cityName, "莫斯科")) {
            return "莫斯科";
        }
        if (Objects.equals(cityName, "EIC国内")) {
            return "中国内地";
        }
        return SpringUtil.getBean(DictServiceImpl.class).getAllCountryChinese().get(cityName);
    }

    @Override
    public Map<String, ErpRegionInfoDTO> getErpRegionInfo() {
        List<ErpRegionInfoDTO> allErpRegionInfos = SpringUtil.getBean(DictServiceImpl.class).getAllErpRegionInfos();
        return ListUtils.toMap(allErpRegionInfos, o -> o.getCampus(), o -> o);
    }

    /**
     * 获取Erp的地域信息，包括campus、zone、city、area、country信息
     */
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<ErpRegionInfoDTO> getAllErpRegionInfos() {
        String sql =
                "select distinct c.Name campus, z.ZoneName zone, city.CityName city, z.AreaName area, country.ChineseName country\n"
                        +
                        "from bas_dis_module m -- module\n" +
                        "left join bas_dis_zone c on c.Id = m.SzoneId -- campus\n" +
                        "left join bas_dis_parent_zone z on z.ZoneId = c.ZoneId -- zone\n" +
                        "left join bas_dis_region r on r.id = z.AreaId\n" +
                        "left join bas_cmdb_city city on city.CityName = c.CityName -- 城市\n" +
                        "left join bas_rmdb_country country on country.ChineseName = z.CountryName";
        return resourcedbDBHelper.getRaw(ErpRegionInfoDTO.class, sql);
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, String> getAllCountryChinese() {
        return ListUtils.toMap(resourcedbDBHelper.getAll(BasCmdbCityDO.class), o -> o.getCityName(),
                o -> o.getCountryChinese());
    }

    @HiSpeedCache(expireSecond = 3600, continueFetchSecond = 60)
    @Override
    @SynchronizedHiSpeedCache1Second
    public List<ResPlanHolidayWeekDO> getAllHolidayWeekInfos() {
        return resplanDBHelper.getAll(ResPlanHolidayWeekDO.class);
    }

    @Override
    public ResPlanHolidayWeekDO getHolidayWeekInfoByBaseAndOffset(ResPlanHolidayWeekDO base, boolean isAfter,
                                                                  int offset) {
        DictServiceImpl dictService = SpringUtil.getBean(DictServiceImpl.class);
        List<ResPlanHolidayWeekDO> allHolidayWeekInfos = dictService.getAllHolidayWeekInfos();
        for (int i = 0; i < allHolidayWeekInfos.size(); i++) {
            if (allHolidayWeekInfos.get(i).same(base)) {
                if (isAfter && i + offset < allHolidayWeekInfos.size()) {
                    return allHolidayWeekInfos.get(i + offset);
                } else if (!isAfter && i - offset >= 0) {
                    return allHolidayWeekInfos.get(i - offset);
                } else {
                    return null;
                }
            }
        }
        return null;
    }

    @Override
    @HiSpeedCache(expireSecond = 3600, keyScript = "args[0] + '-' + args[1]")
    public ResPlanHolidayWeekDO getHolidayWeekInfoByYearWeek(int year, int week) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        List<ResPlanHolidayWeekDO> filter = ListUtils.filter(all,
                o -> Objects.equals(o.getYear(), year) && Objects.equals(o.getWeek(), week));
        if (filter.isEmpty() || filter.size() > 1) {
            throw new RuntimeException("节假周信息: {year = " + year + " , week = " + week + " }不存在或不唯一");
        }
        return filter.get(0);
    }

    @Override
    @HiSpeedCache(expireSecond = 3600, keyScript = "args[0] + '-' + args[1]")
    public List<ResPlanHolidayWeekDO> getHolidayWeekInfoByYearMonth(int year, int month) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        return ListUtils.filter(all, o -> Objects.equals(o.getYear(), year) && Objects.equals(o.getMonth(), month));
    }

    /**
     * 获取dateStr所在的节假周信息
     *
     * @param dateStr
     * @return
     */
    @Override
    public ResPlanHolidayWeekDO getHolidayWeekInfoByDate(String dateStr) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        List<ResPlanHolidayWeekDO> filter =
                ListUtils.filter(all,
                        o -> o != null && o.getStart().compareTo(dateStr) <= 0 && o.getEnd().compareTo(dateStr) >= 0);
        if (filter.size() != 1) {
            throw new RuntimeException("节假周信息有误，请查看");
        }
        return filter.get(0);
    }

    @Override
    public Map<String, ResPlanHolidayWeekDO> getHolidayWeekInfoByDates(List<String> dateStrs) {
        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        Map<String, ResPlanHolidayWeekDO> map = new HashMap<>();
        for (String dateStr : dateStrs) {
            List<ResPlanHolidayWeekDO> filter =
                    ListUtils.filter(all, o -> o != null && o.getStart().compareTo(dateStr) <= 0
                            && o.getEnd().compareTo(dateStr) >= 0);
            if (filter.size() != 1) {
                throw new RuntimeException("节假周信息有误，请查看");
            }
            map.put(dateStr, filter.get(0));
        }
        return map;
    }

    private String washDeviceTypeInfoStringField(String origin) {
        origin = StringUtils.trim(origin);
        if ("not have".equalsIgnoreCase(origin)) {
            origin = "";
        }
        return origin;
    }

    @Override
    public PageData<ServerPartsExtendedInfoDO> queryDeviceTypeInfoList(int pageNum, int pageSize, Boolean defaultFlag) {
        WhereSQL whereSQL = new WhereSQL();
        if (defaultFlag != null) {
            whereSQL.and("default_flag = ?", defaultFlag);
        }
        whereSQL.addOrderBy("id asc");
        PageData<ServerPartsExtendedInfoDO> pageData = erpBakDBHelper.getPage(ServerPartsExtendedInfoDO.class,
                pageNum, pageSize,
                whereSQL.getSQL(), whereSQL.getParams());
        return pageData.transform(e -> {
            e.setDeviceType(washDeviceTypeInfoStringField(e.getDeviceType()));
            e.setDeviceVersion(washDeviceTypeInfoStringField(e.getDeviceVersion()));
            e.setCpuAbbr(washDeviceTypeInfoStringField(e.getCpuAbbr()));
            e.setCpuVender(washDeviceTypeInfoStringField(e.getCpuVender()));
            e.setCpuSeries(washDeviceTypeInfoStringField(e.getCpuSeries()));
            e.setMemoryAbbr(washDeviceTypeInfoStringField(e.getMemoryAbbr()));
            e.setDiskAbbr(washDeviceTypeInfoStringField(e.getDiskAbbr()));
            e.setDisk2Abbr(washDeviceTypeInfoStringField(e.getDisk2Abbr()));
            e.setSsdAbbr(washDeviceTypeInfoStringField(e.getSsdAbbr()));
            e.setSsd2Abbr(washDeviceTypeInfoStringField(e.getSsd2Abbr()));
            e.setGpuAbbr(washDeviceTypeInfoStringField(e.getGpuAbbr()));
            e.setGpuModel(washDeviceTypeInfoStringField(e.getGpuModel()));
            return e;
        });
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0]")
    public List<String> queryPlanProductByDemandCategory(String bizGroup) {
        List<CloudDemandCsigResourceViewCategoryDO> result = Lang.list();
        if (StringTools.isBlank(bizGroup)) {
            result.addAll(yuntiDBHelper.getAll(CloudDemandCsigResourceViewCategoryDO.class));
        } else {
            result.addAll(yuntiDBHelper.getAll(CloudDemandCsigResourceViewCategoryDO.class,
                    "where category_5=?", bizGroup));
        }
        return ListUtils.transform(result, o -> o.getPlanProductName());
    }

    @Override
    public String queryDefaultZoneNameByRegionName(String regionName) {
        if (StringTools.isBlank(regionName)) {
            return "";
        }
        Map<String, String> map =
                SpringUtil.getBean(DictServiceImpl.class).queryRegionDefaultZoneMap();
        return map.get(regionName) == null ? "" : map.get(regionName);

    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public Map<String, String> queryRegionDefaultZoneMap() {
        List<InventoryHealthRegionDefaultZoneConfigDO> all =
                demandDBHelper.getAll(InventoryHealthRegionDefaultZoneConfigDO.class);
        return ListUtils.toMap(all, InventoryHealthRegionDefaultZoneConfigDO::getRegionName,
                InventoryHealthRegionDefaultZoneConfigDO::getZoneName);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    public List<String> queryIndustryDeptFromAccountInfo() {
        return ckcldStdCrpDBHelper.getRaw(String.class,
                "select distinct industry_dept from dwd_txy_appid_info_cf order by industry_dept desc");
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public QueryCampusAndBizType2ZoneDTO getCampusAndBizType2ZoneInfos() {
        String url = "http://tcres.woa.com/api/posv2/get_posv_region_config_new";

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("staffname", "xingyun_admin");

        if (Strings.isNotBlank(tcresCookies)) {
            headers.add("cookie", tcresCookies);
        }

        ImmutableMap<String, Object> params = ImmutableMap.of();
        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            String responseString = restTemplate.postForObject(url, requestEntity, String.class);
            QueryCampusAndBizType2ZoneDTO parse = JSON.parse(responseString, QueryCampusAndBizType2ZoneDTO.class);
            if (parse == null || parse.getCode() != 0 || ListUtils.isEmpty(parse.getData())) {
                throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", parse, url);
            }
            return parse;
        } catch (Exception e) {
            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
        }
    }

    // 从这个页面获取的：https://tcres.woa.com/v3/resource/publicCloudSeat/index。faiywang 表示这里的 1 对 1 映射可用且全
    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, useRedis = true)
    public QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage() {
        return getCampusAndBizType2ZoneInfosPage("25G");
    }

    @Override
    public QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage100() {
        return getCampusAndBizType2ZoneInfosPage("100G");
    }

    private QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage(String modBandTypeName) {
        String url = "http://tcres.woa.com/api/posv2/get_pos_health_detail_new"; // 说明：本地是访问不通这个的，需要将域名加入到proxifier代理中

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("staffname", "xingyun_admin");

        if (Strings.isNotBlank(tcresCookies)) {
            headers.add("cookie", tcresCookies);
        }

        ImmutableMap<String, Object> params = ImmutableMap.of("mod_business_type_name", "腾讯云-公有云-通用",
                "mod_band_type_name", modBandTypeName, "pos_type", "通用机位");
//        params.put("mod_business_type_name", "腾讯云-公有云-通用");
//        params.put("mod_band_type_name", "25G");
//        params.put("pos_type", "通用机位");
        HttpEntity<ImmutableMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            String responseString = restTemplate.postForObject(url, requestEntity, String.class);
            QueryCampusAndBizType2ZonePageDTO parse = JSON.parse(responseString,
                    QueryCampusAndBizType2ZonePageDTO.class);
            if (parse == null || parse.getCode() != 0 || ListUtils.isEmpty(parse.getData())) {
                throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", parse, url);
            }
            return parse;
        } catch (Exception e) {
            throw Lang.makeThrow("调用资源中台接口数据异常, 错误信息:%s\n, 数据页面：%s", e.toString(), url);
        }
    }

    @Override
    public Map<String, String> getIdcDataCampus2ZoneName() {
        QCloudZoneReq req = new QCloudZoneReq();
        req.setResource_name("qcloud_zone");
        req.setResult_column(ListUtils.newArrayList("qcloud_zone_name", "qcloud_zone_campus"));
        QCloudZoneResp resp = idcDataHttpService.queryQCloudZone(req);
        Map<String, String> ret = new HashMap<>();
        for (QCloudZoneResp.Item item : resp.getData()) {
            List<String> campusNames = item.getCampusName();
            if (ListUtils.isEmpty(campusNames)) {
                continue;
            }
            //只查 腾讯云-公有云-通用 的
            /*if(!ListUtils.contains(item.getBusinessType(),o -> StringUtils.equals(o,"腾讯云-公有云-通用"))){
                continue;
            }*/
            for (String name : campusNames) {
                ret.put(name, item.getZoneName());
            }
        }
        return ret;
    }

    @Override
    @HiSpeedCache(expireSecond = 7200, continueFetchSecond = 8000, useRedis = true)
    public Map<String, StaticZoneDO> getCampus2ZoneInfoMap() {
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);

        Map<String, StaticZoneDO> zoneNameMap = new HashMap<>();
        QueryCampusAndBizType2ZonePageDTO all = bean.getCampusAndBizType2ZoneInfosPage();
        if (all != null && ListUtils.isNotEmpty(all.getData())) {
            for (QueryCampusAndBizType2ZonePageDTO.Item item : all.getData()) {
                if (zoneNameMap.containsKey(item.getCampus())) {
                    continue;
                }
                String zoneName = item.getZoneName();
                StaticZoneDO zoneInfo = bean.getStaticZoneInfoByName(zoneName);
                if (zoneInfo != null) {
                    zoneNameMap.put(item.getCampus(), zoneInfo);
                }
            }
        }

        QueryCampusAndBizType2ZonePageDTO page100 = bean.getCampusAndBizType2ZoneInfosPage100();
        // 补充 100G 的 campus数据
        if (page100 != null && ListUtils.isNotEmpty(page100.getData())) {
            for (QueryCampusAndBizType2ZonePageDTO.Item item : page100.getData()) {
                String campus = item.getCampus();
                if (zoneNameMap.containsKey(campus)) {
                    continue;
                }
                String zoneName = item.getZoneName();
                StaticZoneDO zoneInfo = bean.getStaticZoneInfoByName(zoneName);

                if (zoneInfo != null) {
                    zoneNameMap.put(item.getCampus(), zoneInfo);
                }
            }
        }
        //补充云梯ZONE
        List<YuntiStategyZoneDO> zoneList = getYunTiStategyZoneDOList();
        for (YuntiStategyZoneDO item : zoneList) {
            if (zoneNameMap.containsKey(item.getCampusName())) {
                continue;
            }
            StaticZoneDO zoneInfo = bean.getStaticZoneInfoByName(item.getZoneName());
            if (Objects.nonNull(zoneInfo)) {
                zoneNameMap.put(item.getCampusName(), zoneInfo);
            }
        }

        Map<String, String> campus2ZoneMap = bean.getIdcDataCampus2ZoneName();
        for (Map.Entry<String, String> entry : campus2ZoneMap.entrySet()) {
            if (zoneNameMap.containsKey(entry.getKey())) {
                continue;
            }
            StaticZoneDO zoneInfo = bean.getStaticZoneInfoByName(entry.getValue());
            if (zoneInfo != null) {
                zoneNameMap.put(entry.getKey(), zoneInfo);
            }
        }
        return zoneNameMap;
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200)
    public List<YuntiStategyZoneDO> getYunTiStategyZoneDOList() {
        return yuntiDBHelper.getAll(YuntiStategyZoneDO.class).stream().sorted(Comparator.comparing(YuntiStategyZoneDO::getId)).collect(Collectors.toList());

    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200)
    public Map<String, String> getZone2CampusInfoMap() {
        Map<String, StaticZoneDO> campus2ZoneMap = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        Map<String, String> zone2CampusMap = new HashMap<>(campus2ZoneMap.size());

        // 只取第一个
        campus2ZoneMap.entrySet().stream().forEach(o -> {
            if (zone2CampusMap.containsKey(o.getValue().getZoneName())) {
                return;
            }

            zone2CampusMap.put(o.getValue().getZoneName(), o.getKey());
        });

        return zone2CampusMap;
    }

    @Override
    public String getZoneNameByCampusAndBizType(String campusName) {
        Map<String, StaticZoneDO> map = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        StaticZoneDO staticZoneDO = map.get(campusName);
        return staticZoneDO == null ? "" : staticZoneDO.getZoneName();
    }

    @Override
    public String getCampusNameByZoneName(String zoneName) {
        Map<String, String> map = SpringUtil.getBean(DictServiceImpl.class).getZone2CampusInfoMap();
        String campusName = map.get(zoneName);
        return campusName == null ? "" : campusName;
    }

    @Override
    public UserPersonalConfigResp getUserPersonalConfig(@NotNull UserPersonalConfigReq req) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("rtx = ?", req.getRtx());
        whereSQL.and("biz_id = ?", req.getBizId());
        UserPersonalConfig one = demandDBHelper.getOne(UserPersonalConfig.class, whereSQL.getSQL(),
                whereSQL.getParams());
        UserPersonalConfigResp resp = new UserPersonalConfigResp();
        resp.setRtx(req.getRtx());
        resp.setBizId(req.getBizId());
        resp.setData(one != null ? one.getData() : "");
        return resp;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void saveUserPersonalConfig(UserPersonalConfigReq req) {
        if (req.getData() == null) {
            throw new WrongWebParameterException("参数data校验失败");
        }
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("rtx = ?", req.getRtx());
        whereSQL.and("biz_id = ?", req.getBizId());
        UserPersonalConfig one = demandDBHelper.getOne(UserPersonalConfig.class, whereSQL.getSQL(),
                whereSQL.getParams());
        if (one == null) {
            one = new UserPersonalConfig();
            one.setRtx(req.getRtx());
            one.setBizId(req.getBizId());
        }
        one.setData(req.getData());
        demandDBHelper.insertOrUpdate(one);
    }

    @Override
    public List<CrpBizTimeConfigDO> queryBizTimeConfigByGroup(String group, Boolean isFilterNullTime) {
        WhereSQL whereSQL = new WhereSQL();
        if (StringUtils.isNotBlank(group)) {
            whereSQL.and("biz_group = ?", group);
        }
        if (isFilterNullTime) {
            whereSQL.and("time is not null");
        }
        whereSQL.addOrderBy("sort");
        return demandDBHelper.getAll(CrpBizTimeConfigDO.class, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public void updateBizTimeConfig(UpdateBizTimeConfigReq req) {
        requireRole(IndustryDemandAuthRoleEnum.VERSION_OWNER);
        demandDBHelper.update(req.getData());
    }

    public void requireRole(IndustryDemandAuthRoleEnum role) {
        if (role == null) {
            return;
        }
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        if (userNameWithSystem.equals("system")) {
            return;
        }
        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where role=?", role.getCode());
        if (!ListUtils.toSet(auths, o -> o.getUserName()).contains(userNameWithSystem)) {
            throw new BizException("您没有" + role.getName() + "的角色权限，无法进行操作，请联系管理员开通权限");
        }
    }


    @Override
    public Date getLocalDateByBizTimeWeek(CrpCommonHolidayWeekDO currentWeek, CrpBizTimeConfigDO crpBizTimeConfigDO) {
        LocalDate start = LocalDate.parse(currentWeek.getStart());
        // 如果是下周则加7天, 在加上周几, 再减去start 是周一
        Integer plusDay = (crpBizTimeConfigDO.getNextWeek() ? 7 : 0) + crpBizTimeConfigDO.getDayOfWeek() - 1;
        LocalDate localDate = start.plusDays(plusDay);
        return cloud.demand.lab.common.utils.DateUtils.combineLocalDateAndTime(localDate,
                crpBizTimeConfigDO.getTime());
    }

    @Override
    public void refreshHolidayWeek(Integer year) {

        List<CrpCommonHolidayWeekDO> all = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class, "where year = ?", year);
        List<CrpCommonHolidayWeekDO> isHolidayList = all.stream().filter(v -> v.getIsHoliday() == Boolean.TRUE)
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(isHolidayList)) {
            // 如果存在节假日 代表已经刷过了直接返回。
            return;
        }

        // https://km.woa.com/articles/show/360860?kmref=search&from_page=1&no=1
        String url = "http://hrc.oa.com/v1.1/pages/datastation/DataHandler2.aspx?systemName=HRHoliday&moduel=HolidayCalendar";
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        String responseBody = response.getBody();

        List<HolidayDate> holidayDateList = new ArrayList<>();
        String[] lines = responseBody.split("\n");
        System.arraycopy(lines, 1, lines, 0, lines.length - 1);
        for (String line : lines) {
            String[] fields = line.substring(2, line.length() - 3).split("\",\"");
            LocalDate date = LocalDate.parse(fields[0]);
            if (date.getYear() != year || fields[4].equals("周末")) {
                // 如果不是今年 或者 为周末过滤掉， 只取节假日。
                continue;
            }
            if (fields[4].equals("")) {
                fields[4] = "节假日补班";
            }
            HolidayDate holidayDate = new HolidayDate();
            holidayDate.setDate(date);
            holidayDate.setDetail(fields[4]);
            holidayDateList.add(holidayDate);
        }

        for (HolidayDate holidayDate : holidayDateList) {
            CrpCommonHolidayWeekDO crpCommonHolidayWeekDO = binarySearchHolidayWeek(all,
                    holidayDate.getDate().toString());
            if (ObjectUtils.isEmpty(crpCommonHolidayWeekDO)) {
                continue;
            }
            crpCommonHolidayWeekDO.setIsHoliday(Boolean.TRUE);
            if (StringUtils.isBlank(crpCommonHolidayWeekDO.getReamrk())) {
                crpCommonHolidayWeekDO.setReamrk(holidayDate.getDetail());
            } else {
                Set<String> remarkSet = new HashSet<>(Arrays.asList(crpCommonHolidayWeekDO.getReamrk().split(";")));
                remarkSet.add(holidayDate.getDetail());
                crpCommonHolidayWeekDO.setReamrk(Strings.join(";", remarkSet));
            }
        }
        demandDBHelper.update(all);
    }

    @Override
    public CrpCommonHolidayWeekDO binarySearchHolidayWeek(List<CrpCommonHolidayWeekDO> list, String date) {
        int left = 0;
        int right = list.size() - 1;
        while (left <= right) {
            int mid = left + (right - left) / 2;
            CrpCommonHolidayWeekDO midDO = list.get(mid);
            if (midDO.contains(date)) {
                return midDO;
            } else if (midDO.isBefore(date)) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
        return null;
    }

    /**
     * crp_event_notice_config
     * 为避免方法报错阻塞正常流程，此方法内部捕获异常，通知对应研发
     *
     * @param eventCode     事件Code 必填
     * @param noticeTitle   若为空，则直接用配置表的
     * @param noticeContent 若为空，则直接用配置表的
     */
    @Override
    public void eventNotice(String eventCode, String noticeTitle, String noticeContent) {
        eventNotice(eventCode, noticeTitle, noticeContent, null, null);
    }

    @Override
    public void eventNotice(String eventCode, String noticeTitle, String noticeContent,
                            Map<String, Object> templateParams, String users) {
        try {
            CrpEventNoticeConfigDO one = getAndCheckNoticeConfigByEventCode(eventCode);
            if (one.getTurnOff()) {
                // 通知关闭
                return;
            }
            List<String> user = getNoticeUsersByConfig(one, users);
            if (ListUtils.isEmpty(user)) {
                return;
            }
            String join = Strings.join(";", user.stream().distinct().collect(Collectors.toList()));
            String content = StringUtils.isNotBlank(noticeContent) ? noticeContent : one.getNoticeContent();
            if (ListUtils.isNotEmpty(templateParams)) {
                content = StrUtil.format(content, templateParams);
            }
            boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
            if (isTest) {
                // 如果是测试环境替换测试环境url
                if (content.contains("https://crp")) {
                    content = content.replace("https://crp", "https://exp-crp");
                }
            }
            alert.send(one.getNoticeWay(), join,
                    StringUtils.isNotBlank(noticeTitle) ? noticeTitle : one.getNoticeTitle(),
                    content
            );
        } catch (Exception e) {
            alert.sendRtx("oliverychen;jackycjchen", "事件通知失败，eventCode: " + eventCode, e.getMessage());
        }
    }

    @Override
    public void eventNotice(String eventCode, String noticeTitle, String noticeContent, String users) {
        eventNotice(eventCode, noticeTitle, noticeContent, null, users);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<IndustryDemandIndustryWarZoneDictDO> queryIndustryWarZoneCustomerConfig() {
        return demandDBHelper.getAll(IndustryDemandIndustryWarZoneDictDO.class);
    }

    @Override
    public IndustryDemandIndustryWarZoneDictDO queryWarZoneConfigByCustomerShortName(String industryDept,
                                                                                     String customerName) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqualIfValueNotEmpty(IndustryDemandIndustryWarZoneDictDO::getIndustry, industryDept);
        whereContent.andEqualIfValueNotEmpty(IndustryDemandIndustryWarZoneDictDO::getCustomerName, customerName);
        return demandDBHelper.getOne(IndustryDemandIndustryWarZoneDictDO.class, whereContent.getSql(),
                whereContent.getParams());
    }

    @Override
    public List<IndustryDemandIndustryWarZoneDictDO> queryEnableIndustryWarZoneCustomerConfig() {
        return demandDBHelper.getAll(IndustryDemandIndustryWarZoneDictDO.class, "where is_enable = 1");
    }

    @HiSpeedCache(expireSecond = 600)
    @Override
    public Map<String, List<String>> getUnCustomerShortName2Name() {
        List<IndustryDemandIndustryWarZoneDictDO> data = queryEnableIndustryWarZoneCustomerConfig();
        Map<String, List<String>> ret = new HashMap<>();
        data.forEach(item -> ret.computeIfAbsent(item.getCommonCustomerName(), k -> new ArrayList<>()).add(item.getCustomerName()));
        return ret;
    }

    @Override
    public void insertOrUpdateIndustryWarZoneCustomerConfig(
            List<IndustryDemandIndustryWarZoneDictDO> insertOrUpdateList) {
        demandDBHelper.insertOrUpdate(insertOrUpdateList);
        // 再去更新行业用户权限
        updateIndustryDemandAuth();
    }

    @Override
    public void deletedIndustryWarZoneCustomerConfig(List<Long> ids) {
        demandDBHelper.executeRaw("update industry_demand_industry_war_zone_dict "
                + "set deleted = 1 where id in (?)", ids);
        // 再去更新行业用户权限
        updateIndustryDemandAuth();
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, IndustryDemandIndustryWarZoneDictDO> queryCustomerMap() {
        List<IndustryDemandIndustryWarZoneDictDO> all = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class, "where customer_name is not null and customer_name != ''");
        return all.stream().collect(
                Collectors.toMap(IndustryDemandIndustryWarZoneDictDO::getCustomerName, v -> v, (v1, v2) -> v1));
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public Map<String, List<IndustryDemandIndustryWarZoneDictDO>> queryCommonCustomerMap() {
        List<IndustryDemandIndustryWarZoneDictDO> all = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class, "where customer_name is not null and customer_name != ''");
        return all.stream()
                .collect(Collectors.groupingBy(IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName));
    }

    @Override
    public List<String> queryAllCustomerShortName(String commonCustomerName) {
        DictService dictService = SpringUtil.getBean(DictService.class);
        Map<String, List<IndustryDemandIndustryWarZoneDictDO>> map = dictService.queryCommonCustomerMap();
        List<IndustryDemandIndustryWarZoneDictDO> all = map.get(commonCustomerName);
        if (ListUtils.isEmpty(all)) {
            return Arrays.asList(commonCustomerName);
        }
        return all.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).distinct()
                .collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<String> queryMainZone() {
        List<InventoryHealthMainZoneNameConfigDO> result = demandDBHelper.getAll(
                InventoryHealthMainZoneNameConfigDO.class,
                "where date = ? and type_name = ? ", LocalDate.now(), InventoryHealthZoneType.PRINCIPAL.getName());
        if (ListUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result.stream().map(InventoryHealthMainZoneNameConfigDO::getZone).distinct()
                .collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600,keyScript = "args[0]")
    public InventoryHealthMainZoneNameConfigDO queryMainZoneByRegionName(String regionName) {
        LocalDate date = demandDBHelper.getRawOne(LocalDate.class, "select max(`date`) from inventory_health_main_zone_name_config where deleted = 0");
        List<InventoryHealthMainZoneNameConfigDO> result = demandDBHelper.getAll(
                InventoryHealthMainZoneNameConfigDO.class,
                "where date = ? and region_name = ? ", date, regionName);
        if (ListUtils.isEmpty(result)) {
            return null;
        }
        List<String> types = ListUtils.newList("主力可用区", "辅助可用区", "待收敛可用区", "已收敛可用区", "特殊专区", "其他可用区");
        result.sort((o1, o2) -> {
            Integer o1TypeIndex = types.indexOf(o1.getTypeName());
            Integer o2TypeIndex = types.indexOf(o2.getTypeName());
            return o1TypeIndex.compareTo(o2TypeIndex);
        });
        return result.get(0);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<String> queryMainInstanceType() {
        List<InventoryHealthMainInstanceTypeConfigDO> result = demandDBHelper.getAll(
                InventoryHealthMainInstanceTypeConfigDO.class,
                "where date = ? and type1 = ?", LocalDate.now(), InventoryHealthInstanceFamilyType.PRINCIPAL.getCode());
        if (ListUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result.stream().map(InventoryHealthMainInstanceTypeConfigDO::getInstanceType).distinct()
                .collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
    public List<String> queryRegionByCustomhouseTitle(String customhouseTitle) {
        List<String> result = cdCommonDbHelper.getAll(String.class,
                "select distinct api_region from static_zone where customhouse_title = ?", customhouseTitle);
        if (ListUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800)
    @Override
    public List<SoeRegionNameCountryDO> getRegion2Country() {
        String sql = "select distinct region,region_name ,country_name,customhouse_title from bas_soe_region_name_country";
        return demandDBHelper.getRaw(SoeRegionNameCountryDO.class, sql);
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800)
    @Override
    public Map<String, String> getRegion2CountryMapping() {
        Map<String, String> region2CountryMap = getRegion2Country().stream().collect(Collectors.toMap(SoeRegionNameCountryDO::getRegion, SoeRegionNameCountryDO::getCountryName));
        Map<String, StaticZoneDO> staticZoneDOMap = cvmPlanService.getRegionMap();

        Map<String, String> ret = new HashMap<>();
        for (Map.Entry<String, StaticZoneDO> entry : staticZoneDOMap.entrySet()) {
            ret.put(entry.getValue().getApiRegion(), region2CountryMap.getOrDefault(entry.getKey(), Constant.EMPTY_VALUE));
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800)
    @Override
    public Map<String, String> getRegionName2CountryMapping() {
        return getRegion2Country().stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName, SoeRegionNameCountryDO::getCountryName));
    }

    @HiSpeedCache(expireSecond = 600)
    @Override
    public Map<String, List<String>> getCountry2RegionMapping() {
        return getRegion2Country().stream().collect(Collectors.groupingBy(
                SoeRegionNameCountryDO::getCountryName,
                Collectors.mapping(SoeRegionNameCountryDO::getRegionName, Collectors.toList())
        ));
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1800)
    @Override
    public List<PaasProductUinWhiteListDO> getPaasProductUinMapping() {
        return demandDBHelper.getAll(PaasProductUinWhiteListDO.class);
    }

    @Override
    public UserPermissionDto getPermissionByUserAndRole(String roleCode, String user) {
        IndustryDemandAuthDO auth = demandDBHelper.getOne(IndustryDemandAuthDO.class, "where user_name=? and role=?",
                user, roleCode);
        if (org.springframework.util.ObjectUtils.isEmpty(auth)) {
            return null;
        }
        return UserPermissionDto.form(auth);
    }

    @Override
    public List<String> queryInstanceType(String zoneName) {
        String postSql = "";
        if (Strings.isNotBlank(zoneName)) {
            postSql = "and zone_name='" + zoneName + "'";
        }
        String sql = "select distinct instance_type\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 " + postSql;
        return demandDBHelper.getRaw(String.class, sql).stream().filter((o) -> {
            String gpuPrefix = DynamicProperties.getGpuPrefix();
            String[] split = gpuPrefix.trim().split(";");
            for (String s : split) {
                if (o.startsWith(s)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
    }

    public void updateIndustryDemandAuth() {
        List<IndustryDemandAuthDO> all = demandDBHelper.getAll(IndustryDemandAuthDO.class, "where "
                + "(is_all_industry = 1 or is_all_war_zone = 1 or is_all_customer = 1)");
        List<IndustryDemandIndustryWarZoneDictDO> industryDemandIndustryWarZoneDictDOS = queryEnableIndustryWarZoneCustomerConfig();
        industryDemandIndustryWarZoneDictDOS = industryDemandIndustryWarZoneDictDOS.stream()
                .filter(IndustryDemandIndustryWarZoneDictDO::getIsEnable).collect(Collectors.toList());
        for (IndustryDemandAuthDO industryDemandAuthDO : all) {
            updateBizAllAuth(industryDemandAuthDO, industryDemandIndustryWarZoneDictDOS);
        }
        demandDBHelper.update(all);
    }

    public void updateBizAllAuth(IndustryDemandAuthDO industryDemandAuthDO
            , List<IndustryDemandIndustryWarZoneDictDO> industryDemandIndustryWarZoneDictDOS) {
        if (industryDemandAuthDO.getIsAllIndustry()) {
            // 全行业
            Set<String> allIndustry = industryDemandIndustryWarZoneDictDOS.stream()
                    .map(IndustryDemandIndustryWarZoneDictDO::getIndustry).collect(
                            Collectors.toSet());
            // 取原有的并集
            allIndustry.addAll(new ArrayList<>(Arrays.asList(industryDemandAuthDO.getIndustry().split(";"))));
            industryDemandAuthDO.setIndustry(String.join(";", allIndustry));

        }
        if (industryDemandAuthDO.getIsAllWarZone()) {
            // 全战区
            List<String> industry = new ArrayList<>(Arrays.asList(industryDemandAuthDO.getIndustry().split(";")));

            // 用户有的行业下的全战区
            Set<String> allWarZone = industryDemandIndustryWarZoneDictDOS.stream()
                    .filter(v -> industry.contains(v.getIndustry()))
                    .map(IndustryDemandIndustryWarZoneDictDO::getWarZoneName).collect(
                            Collectors.toSet());

            // 取原有的并集
            allWarZone.addAll(new ArrayList<>(Arrays.asList(industryDemandAuthDO.getWarZoneName().split(";"))));
            industryDemandAuthDO.setWarZoneName(String.join(";", allWarZone));
        }
        if (industryDemandAuthDO.getIsAllCustomer()) {
            // 全客户
            List<String> warZone = new ArrayList<>(Arrays.asList(industryDemandAuthDO.getWarZoneName().split(";")));

            // 用户有的战区下的全客户
            Set<String> allCustomer = industryDemandIndustryWarZoneDictDOS.stream()
                    .filter(v -> warZone.contains(v.getWarZoneName()))
                    .map(IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName).collect(
                            Collectors.toSet());

            // 取原有的并集
            allCustomer.addAll(new ArrayList<>(Arrays.asList(industryDemandAuthDO.getCommonCustomerName().split(";"))));
            industryDemandAuthDO.setCommonCustomerName(String.join(";", allCustomer));
        }
    }


    private CrpEventNoticeConfigDO getAndCheckNoticeConfigByEventCode(String eventCode) {
        if (StringUtils.isBlank(eventCode)) {
            throw new BizException("eventCode不能为空");
        }
        CrpEventNoticeConfigDO one = demandDBHelper.getOne(CrpEventNoticeConfigDO.class, "where event_code = ?",
                eventCode);
        if (one == null) {
            throw new BizException(eventCode + "通知配置缺失");
        }
        return one;
    }

    private List<String> getNoticeUsersByConfig(CrpEventNoticeConfigDO one, String users) {
        Set<String> user = new HashSet<>();
        // 跟配置的通知用户取并集
        if (StringUtils.isNotBlank(one.getNoticeUser())) {
            user.addAll(Arrays.asList(one.getNoticeUser().split(";")));
        }
        // 跟配置的通知角色下的用户取并集
        if (StringUtils.isNotBlank(one.getNoticeRole())) {
            List<String> role = Arrays.asList(one.getNoticeRole().split(";"));
            List<IndustryDemandAuthDO> userList = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                    "where role in (?)",
                    role);
            if (ListUtils.isNotEmpty(userList)) {
                user.addAll(userList.stream().map(IndustryDemandAuthDO::getUserName)
                        .collect(Collectors.toList()));
            }
        }
        // 跟入参的通知用户取并集
        if (StringUtils.isNotBlank(users)) {
            List<String> inputUsers = StrUtil.splitTrim(users, ';');
            if (ListUtils.isNotEmpty(inputUsers)) {
                user.addAll(inputUsers);
            }
        }
        boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
        if (isTest) {
            // 最后如果是测试环境，与测试环境配置通知用户取交集，防止在测试环境时发送给生产环境业务人员
            if (StringUtils.isNotBlank(one.getTestNoticeUser())) {
                List<String> testUsers = StrUtil.splitTrim(one.getTestNoticeUser(), ';');
                if (ListUtils.isNotEmpty(testUsers)) {
                    user.retainAll(testUsers);
                }
            }
        }
        return new ArrayList<>(user);
    }


    @Override
    public Boolean checkIsAdmin(String userName) {
        if (userName.equals("system")) {
            return true;
        }
        List<IndustryDemandAuthDO> raw = demandDBHelper.getRaw(
                IndustryDemandAuthDO.class,
                "select * from industry_demand_auth where user_name = ? and role = 'ADMIN' and deleted = 0", userName);
        if (CollectionUtils.isEmpty(raw)) {
            return false;
        }
        return true;
    }

    /**
     * cos冗余度
     */
    @Data
    private static class CosRedundancyDTO {

        // 国内冗余度，yyyy-MM-dd日期 -> 冗余度
        private Map<Date, BigDecimal> cn;
        // 境外冗余度，yyyy-MM-dd日期 -> 冗余度
        private Map<Date, BigDecimal> oversea;
        // 国内平均冗余度
        private BigDecimal cnAvg;
        // 境外平均冗余度
        private BigDecimal overseaAvg;
    }

//    @Override
//    @Deprecated
//    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600)
//    public Map<String, String> getDeviceType2InstanceTypeMap() {
//        String deviceSql = "select distinct device_type, instance_type from yunxiao_instance_config;";
//        List<DeviceInstanceTypeDO> deviceType2InstanceType = demandDBHelper.getRaw(DeviceInstanceTypeDO.class, deviceSql);
//        Map<String, String> deviceType2InstanceTypeMap = new HashMap<>();
//
//        for (DeviceInstanceTypeDO deviceType2InstanceTypeItem : deviceType2InstanceType) {
//            deviceType2InstanceTypeMap.put(deviceType2InstanceTypeItem.getDeviceType(), deviceType2InstanceTypeItem.getInstanceType());
//        }
//        return deviceType2InstanceTypeMap;
//    }

    @Data
    static public class QueryCampus2ZoneIdDTO {

        @JsonProperty("code")
        Integer code;
        String message;
        @JsonProperty("data")
        Data data;

        @lombok.Data
        static public class Data {

            @JsonProperty("items")
            List<Item> items;

            @lombok.Data
            static public class Item {

                @JsonProperty("zone_id")
                Long zoneId;
                @JsonProperty("campus")
                List<String> campus;
                @JsonProperty("zone_en")
                String zoneEn;
            }
        }
    }

    @Data
    static public class QueryModule2ZoneIdDTO {

        @JsonProperty("code")
        Integer code;
        String message;
        @JsonProperty("data")
        List<Item> data;

        @lombok.Data
        static public class Item {

            @JsonProperty("zone_id")
            Long zoneId;
            @JsonProperty("module")
            String module;
        }
    }
}
