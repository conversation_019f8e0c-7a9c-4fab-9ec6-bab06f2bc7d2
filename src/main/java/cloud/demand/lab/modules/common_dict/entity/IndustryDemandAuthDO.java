package cloud.demand.lab.modules.common_dict.entity;

import cloud.demand.lab.common.entity.BaseUserDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;
import org.nutz.lang.Strings;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Table("industry_demand_auth")
public class IndustryDemandAuthDO extends BaseUserDO {

    /**
     * 用户名<br/>Column: [user_name]
     */
    @Column(value = "user_name")
    private String userName;

    /**
     * 角色<br/>Column:[role]
     *
     * @see cloud.demand.lab.modules.auth.IndustryDemandAuthRoleEnum
     */
    @Column(value = "role")
    private String role;

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry", insertValueScript = "''")
    private String industry;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 中心名称<br/>Column: [center_name]
     */
    @Column(value = "center_name")
    private String centerName;

    /**
     * 战区<br/>Column: [war_zone_name]
     */
    @Column(value = "war_zone_name", insertValueScript = "''")
    private String warZoneName;


    /**
     * 通用客户简称<br/>Column: [common_customer_name]
     */
    @Column(value = "common_customer_name", insertValueScript = "''")
    private String commonCustomerName;

    /**
     * 是否全行业
     */
    @Column(value = "is_all_industry")
    private Boolean isAllIndustry;

    /**
     * 是否全战区
     */
    @Column(value = "is_all_war_zone")
    private Boolean isAllWarZone;

    /**
     * 是否全客户
     */
    @Column(value = "is_all_customer")
    private Boolean isAllCustomer;

    /**
     * 是否全产品
     */
    @Column(value = "is_all_product")
    private Boolean isAllProduct;

    /**
     * 子产品<br/>Column: [sub_product]
     */
    @Column(value = "sub_product", insertValueScript = "''")
    private String subProduct;

    /**
     * 是否全部子产品
     */
    @Column(value = "is_all_sub_product")
    private Boolean isAllSubProduct;


    /**
     * region<br/>Column: [region]
     */
    @Column(value = "region", insertValueScript = "''")
    private String region;


    /**
     * 是否全地域<br/>Column: [is_all_region]
     */
    @Column(value = "is_all_region")
    private Boolean isAllRegion;

    public List<String> industryDeptListGet() {
        if (Strings.isBlank(this.industry)) {
            return new ArrayList<>();
        }
        String[] array = this.industry.split(";");
        return ListUtils.newArrayList(array);
    }

    public List<String> productListGet() {
        if (Strings.isBlank(this.product)) {
            return new ArrayList<>();
        }
        String[] array = this.product.split(";");
        return ListUtils.newArrayList(array);
    }

    public List<String> warZoneNameListGet() {
        if (Strings.isBlank(this.warZoneName)) {
            return new ArrayList<>();
        }
        String[] array = this.warZoneName.split(";");
        return ListUtils.newArrayList(array);
    }

    public List<String> commonCustomerNameListGet() {
        if (Strings.isBlank(this.commonCustomerName)) {
            return new ArrayList<>();
        }
        String[] array = this.commonCustomerName.split(";");
        return ListUtils.newArrayList(array);
    }

    public List<String> subProductListGet() {
        if (Strings.isBlank(this.subProduct)) {
            return new ArrayList<>();
        }
        String[] array = this.subProduct.split(";");
        return ListUtils.newArrayList(array);
    }
}