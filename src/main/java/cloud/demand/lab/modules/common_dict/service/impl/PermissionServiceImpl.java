package cloud.demand.lab.modules.common_dict.service.impl;

import cloud.demand.lab.modules.common_dict.DO.UserPermissionDTO;
import cloud.demand.lab.modules.common_dict.entity.IndustryDemandAuthDO;
import cloud.demand.lab.modules.common_dict.service.PermissionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class PermissionServiceImpl implements PermissionService {

    @Resource
    private DBHelper demandDBHelper;


    @Override
    public List<UserPermissionDTO> getPermissionByUserAndRoles(List<String> roleCodes, String user) {
        List<IndustryDemandAuthDO> auth = demandDBHelper.getAll(IndustryDemandAuthDO.class, "where user_name=? and role in (?)",
                user, roleCodes);
        if (ObjectUtils.isEmpty(auth)) {
            return null;
        }
        return ListUtils.transform(auth,UserPermissionDTO::form);
    }

    @Override
    public Boolean checkIsAdmin(String userName) {
        // LoginUtils.getUserNameWithSystem() 【null，system，no】一个意思，表示不鉴权处理
        if (userName == null || userName.equals("system") || userName.equals("no")) {
            return true;
        }
        List<IndustryDemandAuthDO> raw = demandDBHelper.getRaw(IndustryDemandAuthDO.class,
                "select * from industry_demand_auth where user_name = ? and role = 'ADMIN' and deleted = 0", userName);
        if (CollectionUtils.isEmpty(raw)) {
            return false;
        }
        return true;
    }
}
