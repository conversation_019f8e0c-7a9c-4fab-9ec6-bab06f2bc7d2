package cloud.demand.lab.modules.common_dict.service;

import cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.lab.modules.common_dict.DO.ReportConfigGpuTypeDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticGinsfamilyDO;
import cloud.demand.lab.modules.common_dict.DO.StaticGinstypeDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CvmPlanService {


    /**
     * instanceModel info
     * @param instanceModel ins
     * @return info
     */
    StaticGinstypeDO getInstanceModelInfo(String instanceModel);


    /**
     * 根据实例类型获取字典表相关信息
     */
    StaticGinsfamilyDO getInstanceTypeInfo(String instanceType);

    /**
     * getAllInstanceTypes
     * @return
     */
    Map<String, StaticGinstypeDO> getAllInstanceTypes();

    /**
     * 获取腾讯云实例类型字段表信息
     * @return Map
     *      k:实例类型(如:S5), v:该实例类型的其他相关信息
     */
    Map<String, StaticGinsfamilyDO> getAllInstanceFamily();

    /**
     * 查询从instanceType （例如S5） 到 instanceFamily （例如计算型） 的映射
     * @return
     */
    Map<String, String> getInstanceType2InstanceFamily();

    /**
     * CvmPlan 的可用区数据
     * @return
     */
    Map<Long, StaticZoneDO> getZoneIdMap();

    Map<String, StaticZoneDO> getRegionMap();

    /**
     * name 到可用区信息
     * @return map
     */
    Map<String, StaticZoneDO> getZoneNameMap();

    /**
     * 可用区信息
     * @param zoneName name
     * @return info
     */
    StaticZoneDO getZoneInfo(String zoneName);

    /**
     * getZoneInfo
     * @param zoneId zoneId
     * @return StaticZoneDO
     */
    StaticZoneDO getZoneInfo(Long zoneId);


    /**
     * 通过实例规格获取 GPU CardType
     * @param instanceType string
     * @return string
     */
    String getGpuCardTypeByInstanceType(String instanceType);

    /**
     * 实例规格到物理机
     * @return map
     */
    Map<String, String> queryInstanceType2DeviceTypeMap();

    /**
     * 物理机 -> gpu 信息
     * @return map
     */
    Map<String, ReportConfigGpuTypeDO> loadGpuCardConfig();

    /**
     * 获取地域到国家名的映射信息
     * 全量缓存
     */
    Map<String, SoeRegionNameCountryDO> getRegionNameInfoMap();

}
