package cloud.demand.lab.modules.common_dict.DO;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@Table("yunxiao_instance_config")
@AllArgsConstructor
@NoArgsConstructor
public class YunxiaoInstanceConfigDO {

    /**
     * 主键<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例类型名字<br/>Column: [instance_type_name]
     */
    @Column(value = "instance_type_name")
    private String instanceTypeName;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * 设备类型<br/>Column: [device_type]
     */
    @Column(value = "device_type")
    private String deviceType;

    /**
     * 设备类型逻辑核<br/>Column: [device_type_logic_core_num]
     */
    @Column(value = "device_type_logic_core_num")
    private Integer deviceTypeLogicCoreNum;

    public String key() {
        return String.join("@", instanceType, instanceModel, instanceTypeName, deviceType);
    }

}