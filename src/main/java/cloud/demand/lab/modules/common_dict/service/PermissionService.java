package cloud.demand.lab.modules.common_dict.service;

import cloud.demand.lab.modules.common_dict.DO.UserPermissionDTO;

import java.util.List;

/**
 * 用户权限
 *
 *
 */
public interface PermissionService {


    List<UserPermissionDTO> getPermissionByUserAndRoles(List<String> roleCodes, String user);


    /**
     * 校验是否是管理员
     *
     * @param userName
     * @return
     */
    Boolean checkIsAdmin(String userName);

}
