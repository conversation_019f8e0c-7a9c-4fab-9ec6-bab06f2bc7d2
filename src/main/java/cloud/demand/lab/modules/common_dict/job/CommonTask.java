package cloud.demand.lab.modules.common_dict.job;

import cloud.demand.lab.modules.common_dict.service.MetricSynchronizationService;
import com.pugwoo.wooutils.redis.Synchronized;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CommonTask {

    @Resource
    private MetricSynchronizationService metricSynchronizationService;


    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticZone() {
        metricSynchronizationService.synchronizeMetricStaticZone();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticCvmType() {
        metricSynchronizationService.synchronizeMetricStaticCvmType();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticGinsFamily() {
        metricSynchronizationService.synchronizeMetricStaticGinsFamily();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticGinsType() {
        metricSynchronizationService.synchronizeMetricStaticGinsType();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticDeliver() {
        metricSynchronizationService.synchronizeMetricStaticDeliver();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticModule() {
        metricSynchronizationService.synchronizeMetricStaticModule();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticHostType() {
        metricSynchronizationService.synchronizeMetricStaticHostType();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void synchronizeMetricStaticPrincipalGinsFamily() {
        metricSynchronizationService.synchronizeMetricStaticPrincipalGinsFamily();
    }

}
