package cloud.demand.lab.modules.common_dict.valid;

import java.util.Arrays;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 检查用户传入的字符串值是否属于既定的xxxEnum.class.getEnumConstants()中
 */
public class EnumValidator implements ConstraintValidator<EnumValid, String> {
    private Class<?> enumClass;

    @Override
    public void initialize(EnumValid constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()){
            value = "";
        }
        try {
            Enum.valueOf((Class<Enum>) enumClass, value);
            return true;
        } catch (IllegalArgumentException e) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                    "Invalid enum value, should be one of " + Arrays.toString(enumClass.getEnumConstants()))
                    .addConstraintViolation();
            return false;
        }
    }

}
