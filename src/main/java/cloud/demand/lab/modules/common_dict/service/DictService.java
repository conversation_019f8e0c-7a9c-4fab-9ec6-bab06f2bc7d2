package cloud.demand.lab.modules.common_dict.service;

import cloud.demand.lab.common.entity.UserPermissionDto;
import cloud.demand.lab.modules.common_dict.DO.ErpRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.DO.ObsBizInfo;
import cloud.demand.lab.modules.common_dict.DO.PaasProductUinWhiteListDO;
import cloud.demand.lab.modules.common_dict.DO.ProductInfoDTO;
import cloud.demand.lab.modules.common_dict.DO.ReportConfigGpuTypeDO;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.entity.PplGpuRegionZoneDO;
import cloud.demand.lab.modules.common_dict.model.req.UpdateBizTimeConfigReq;
import cloud.demand.lab.modules.common_dict.model.req.UserPersonalConfigReq;
import cloud.demand.lab.modules.common_dict.model.rsp.UserPersonalConfigResp;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpBizTimeConfigDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.CrpCommonHolidayWeekDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.ServerPartsExtendedInfoDO;
import cloud.demand.lab.modules.operation_view.entity.plan.StaticStockPrincipalHosttypeDO;
import cloud.demand.lab.modules.operation_view.entity.resource.BasCmdbCityDO;
import cloud.demand.lab.modules.operation_view.entity.resource.BasStrategyDeviceVersionNettypeDO;
import cloud.demand.lab.modules.operation_view.entity.resource.ServerPartsCompositionVO;
import cloud.demand.lab.modules.operation_view.entity.web.dict.QueryCampusAndBizType2ZoneDTO;
import cloud.demand.lab.modules.operation_view.entity.web.dict.QueryCampusAndBizType2ZonePageDTO;
import cloud.demand.lab.modules.operation_view.entity.yunti.BasObsCloudCvmTypeDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

public interface DictService {


    /**
     * 获取腾讯云region的映射关系
     */
    Map<String, String> getRegionNameMap();

    /**
     * 获取腾讯云， regionName 到 国家的映射
     *
     * @return map
     */
    Map<String, String> getRegion2CustomhouseTitleMap();

    /**
     * 获取zone_name到国家的映射
     */
    Map<String, String> getZoneToCountryMap();

    /**
     * 根据CustomhouseTitle 获取regionName
     *
     * @return 地域集合
     */
    List<String> getRegionNameByCustomhouseTitle(String customhouseTitle);

    /**
     * @return map
     */
    Map<String, String> getRegionMap();

    /**
     * 获取腾讯云的地域列表
     */
    List<String> queryRegionNameList();

    /**
     * 获取腾讯云zone的映射关系
     */
    Map<String, String> getZoneNameMap();


    @HiSpeedCache(expireSecond = 600)
    Map<String, String> getZoneName2RegionName();

    Map<String, String> getZoneMap();

    /**
     * 获取腾讯云的可用区列表
     */
    List<String> queryZoneNameList();

    /**
     * 获取腾讯云的可用区列表 by regionName
     */
    List<String> queryZoneNameList(String regionName);

    /**
     * 获取腾讯云的地域列表 by regionName
     */
    List<String> queryRegionNameList(String areaName);

    /**
     * 获取Plan系统中的全量zone信息
     */
    Map<Long, StaticZoneDO> getAllPlanZoneInfos();

    /**
     * 获取Plan系统中的全量zone信息, group by zone_name
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByName();

    /**
     * 获取Plan系统中的全量zone信息, group by regin_name
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegionName();

    /**
     * 获取Plan系统中的全量zone信息, group by regin
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegion();

    /**
     * 获取Plan系统中的全量zone信息, group by zone
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByCode();

    /**
     * 通过zoneid获取对应zone对象的基础信息
     */
    StaticZoneDO getStaticZoneInfoById(Long zoneId);

    /**
     * 通过zone_name获取对应zone对象的基础信息
     */
    StaticZoneDO getStaticZoneInfoByName(String zoneName);

    /**
     * 通过campus查询zone信息
     */
    StaticZoneDO getStaticZoneInfoByCampus(String campus);

    /**
     * 查询全部机型可用区信息
     */
    List<StaticZoneDO> getAllZoneInfos();

    /**
     * 通过module查询zone信息
     */
    StaticZoneDO getStaticZoneInfoByModule(String moduleName);

    /**
     * 获取母机机型 -> 逻辑核心数的Map
     */
    Map<String, Integer> getDeviceLogicCpuCore();

    Map<String, String> getDeviceFamilyMap();

    /**
     * 获得母机机型的核心数，支持指定是否前缀匹配
     *
     * @param matchPrefix 是否开启前缀匹配，前缀匹配采用最长前缀匹配
     * @return 机型不存在返回0
     */
    Integer getDeviceLogicCpuCore(String deviceType, boolean matchPrefix);

    /**
     * 查询指定母机的gpucard数
     */
    Integer getDeviceGpuCard(String deviceType);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，用DEFAULT默认机型
     * 单位转化成pb
     */
    BigDecimal getDeviceCbsStore(String deviceType);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，取0
     * 单位转化成pb
     */
    BigDecimal getDeviceCosStore(String deviceType, Date date, boolean isCN);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，取0
     * 单位转化成pb
     */
    BigDecimal getDeviceCosStore(String planProduct, String deviceType);

    /**
     * 查询CDB产品指定物理机的最大可售数量，如果不存在该机型，取0
     * 单位转化成 tb
     */
    BigDecimal getDeviceCdbStore(String deviceType);

    /**
     * 获取erp城市相关信息的map，其中k是cityName
     */
    Map<String, BasCmdbCityDO> getBasCmdbCityInfoMap();

    /**
     * 查询CDB产品指定物理机的单机内存，如果不存在该机型，取0
     * 单位转化成 tb
     */
    BigDecimal getDeviceCdbMemCap(String deviceType);

    /**
     * 查询CRS产品指定物理机的物理容量，若不存在，取0
     * 单位 gb
     */
    BigDecimal getDeviceCrsMemCap(String deviceType);

    /**
     * 获取Cmongo产品指定物理机的最大可用容量，单位gb
     */
    BigDecimal getCmongoDeviceMem(String deviceType);

    /**
     * 查询Plan系统中指定母机的逻辑核心数
     */
    Integer getPlanDeviceLogicCore(String deviceType);

    /**
     * 查询服务器处理器的类型
     *
     * @param deviceType 详见ComputeTypeEnum
     */
    String getComputeType(String deviceType);

    /**
     * 查询cvm的处理器类型
     */
    String getCvmComputeType(String instanceType);

    /**
     * 查询cvm机型的cpu核心数；不存在的返回0
     */
    Integer getInstanceCoreNum(String instanceModel);

    /**
     * 查询全部物理机和部件映射表数据
     */
    Map<String, ServerPartsCompositionVO> getAllServerPartsCompositions();

    /**
     * 通过物理机机型查询物理机和部件的映射关系
     */
    ServerPartsCompositionVO getServerPartsCompositionByDeviceType(String deviceType);

    /**
     * 查询全部网络类型信息
     */
    Map<String, BasStrategyDeviceVersionNettypeDO> getAllNetTypes();

    /**
     * 通过物理机机型查询对应的网络类型信息
     */
    BasStrategyDeviceVersionNettypeDO getNetTypeByDeviceType(String deviceType);

    /**
     * 查询全部Csig设备扩展信息
     */
    Map<String, CloudDemandCsigDeviceExtendInfoDO> getAllCsigDeviceExtendInfos();

    /**
     * 通过物理机机型查询对应的Csig设备扩展信息
     */
    CloudDemandCsigDeviceExtendInfoDO getCsigDeviceExtendInfoByDeviceType(String deviceType);

    /**
     * CSIG 设备类型与实例类型的映射表获取
     *
     * @return
     */
    Map<String, String> getCsigDeviceTypeToInstanceTypeMap();

    String getCsigInstanceTypeByDeviceType(String deviceType);

    Map<String, List<String>> getCsigInstanceTypeToDeviceTypeMap();

    List<String> getCsigDeviceTypeByInstanceType(String instanceType);

    List<String> getCsigDeviceTypeByInstanceType(List<String> instanceType);


    /**
     * 通过物理机机型查询对应的机型族信息(一对一)
     *
     * @param deviceType
     * @return
     */
    String getDeviceFamilyByDeviceType(String deviceType);

    /**
     * 通过机型族反查出任意物理机机型(多对一)
     *
     * @param deviceFamily
     * @return
     */
    String getDeviceTypeByDeviceFamily(String deviceFamily);

    /**
     * 获取全量GPU卡类配置信息
     */
    Map<String, ReportConfigGpuTypeDO> loadGpuCardConfig();


    /**
     * 查询 csig 的规划产品 id
     *
     * @return
     */
    List<Integer> getCsigDeptId();

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    Map<String, ObsBizInfo> getBizInfoByPlanProductName();

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    Map<String, ProductInfoDTO> getProductInfoByProductName();


    Map<String, BasObsCloudCvmTypeDO> queryInstanceModelInfos();

    /** 实例类型 --> 实例规格 */
    Map<String, List<String>> queryTypeToInstanceModel();

    /** 获取行业维度客户信息，行业 + 战区 + 客户 + 通用客户简称 */
    List<IndustryCustomerInfo> getIndustryCustomerInfoList();

    /** 战区 --> 通用客户简称 */
    Map<String, List<String>> queryWarZoneToCustomerShortName();

    Map<String, String> queryInstanceTypeToGroup();

    Map<String, List<String>> queryGroupToInstanceType();

    String getInstanceGroupByInstanceType(String instanceType);

    List<String> getNewGenerationInstanceType();

    /**
     * 根据ERP Campus映射到腾讯云的可用区信息
     */
    Map<String, Long> getZoneIdByCampusName();

    /**
     * 根据ERP zone映射到腾讯云的campus信息
     */
    Map<String, String> getCampusByZone();

    /**
     * 根据ERP ModuleName映射到腾讯云的可用区信息
     */
    Map<String, Long> getZoneIdByModuleName();

    /**
     * 查询产品的库存成本单价
     */
    Map<String, BigDecimal> getProductUnitPrice();

    /**
     * 获取所有好料机型
     */
    Map<String, StaticStockPrincipalHosttypeDO> getAllGoodDeviceType();

    /**
     * 获取全部腾讯云地域信息
     */
    List<TxyRegionInfoDTO> getAllTxyRegionInfo();

    /** 获取 ppl 可用区卡型配置表 */
    public List<PplGpuRegionZoneDO> queryGpuInstanceList();

    /**
     * 获取全部腾讯云地域信息(zoneId --> 地域信息)
     */
    Map<Integer, TxyRegionInfoDTO> getAllTxyRegionInfoMap();

    @Data
    class TxyRegionInfoWithZoneIdDTO extends TxyRegionInfoDTO {
        @Column("zoneid")
        private Integer zoneId;
        public TxyRegionInfoDTO transform(){
            TxyRegionInfoDTO ret = new TxyRegionInfoDTO();
            ret.setCountry(this.getCountry());
            ret.setRegionName(this.getRegionName());
            ret.setZoneName(this.getZoneName());
            ret.setCustomhouseTitle(this.getCustomhouseTitle());
            ret.setAreaName(this.getAreaName());
            return ret;
        }
    }

    /**
     * 区域 --> 地域
     * */
    Map<String, List<String>> getAreaNameToRegionName();

    /**
     * 城市 -> 地区名称 ，用来判断是境内还是境外
     * eg: 北京 -> 中国内地
     * 加拿大 -> 加拿大
     */
    String getCountryChineseByCityName(String CityName);

    /**
     * 获取Erp地域相关的信息，key是CampusName
     */
    Map<String, ErpRegionInfoDTO> getErpRegionInfo();

    /**
     * weekInfo
     *
     * @return list
     */
    List<ResPlanHolidayWeekDO> getAllHolidayWeekInfos();

    /**
     * 通过一个基准的节假周配置，获取前/后间隔为offset的节假周
     *
     * @param base 基准节假周
     * @param isAfter 是否落后节假周，否则为领先
     * @param offset 间隔
     * @return 目标节假周
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByBaseAndOffset(ResPlanHolidayWeekDO base, boolean isAfter, int offset);

    /**
     * 根据指定年+周确定唯一的节假周DO
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByYearWeek(int year, int week);

    /**
     * 根据指定年+月确定当月的节假周DO列表
     */
    List<ResPlanHolidayWeekDO> getHolidayWeekInfoByYearMonth(int year, int month);

    /**
     * 获取指定时间的节假周信息
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByDate(String dateStr);

    /**
     * 获取指定时间的节假周信息，批量
     *
     * @param dateStrs 日期字符串，格式为yyyy-MM-dd
     * @return 返回的map的key是对应的dateStr
     */
    Map<String, ResPlanHolidayWeekDO> getHolidayWeekInfoByDates(List<String> dateStrs);


    /**
     * 提供给外部系统使用的设备类型字典接口
     *
     * @return 设备类型列表
     */
    PageData<ServerPartsExtendedInfoDO> queryDeviceTypeInfoList(int pageNum, int pageSize, Boolean defaultFlag);


    /**
     * 通过传入的产品类型获取需求视图的产品分类，用category5、和全年需求执行报表的逻辑一致
     */
    List<String> queryPlanProductByDemandCategory(String productType);

    /**
     * 当某个地域下的可用区为【随机可用区】时调用
     * 获取某个地域下的默认可用区名称
     */
    String queryDefaultZoneNameByRegionName(String RegionName);

    /**
     * 从账号宽表中提取行业部门（含空值）
     *
     * @return
     */
    List<String> queryIndustryDeptFromAccountInfo();

    /**
     * 根据ERP Campus映射到腾讯云的可用区全量信息
     * 对应页面链接：https://tcres.woa.com/v3/resource/publicCloudSeat/config中【机房业务类型映射表】tab
     */
    QueryCampusAndBizType2ZoneDTO getCampusAndBizType2ZoneInfos();


    /**
     * https://tcres.woa.com/v3/resource/publicCloudSeat/index
     */
    QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage();

    /**
     * https://tcres.woa.com/v3/resource/publicCloudSeat/index
     * 参数 mod_band_type_name 改成 100G
     */
    QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage100();

    Map<String, String> getIdcDataCampus2ZoneName();

    /**
     * 通过getCampusAndBizType2ZoneInfos接口返回一个campusName -> zoneInfo的映射接口
     * 通过【物理机Campus + Module业务类型】来唯一确认【腾讯云虚拟机可用区】信息
     * 对于相同Campus的多个业务类型，按照：【腾讯云-公有云-通用】-> 【腾讯云-公有云-黑石专用】-> 【其他】的优先级获取对应zone数据
     *
     * @return Map  k:上海-花桥 v:上海五区的可用区信息
     */
    Map<String, StaticZoneDO> getCampus2ZoneInfoMap();

    List<YuntiStategyZoneDO> getYunTiStategyZoneDOList();

    /**
     * getCampus2ZoneInfoMap，逆向映射，因为 getCampus2ZoneInfoMap 会保证一对一映射，所以可以反查
     *
     * @return
     */
    Map<String, String> getZone2CampusInfoMap();

    /**
     * 调用getCampus2ZoneInfoMap接口根据CampusName找ZoneName
     *
     * @return Map k:上海-花桥 v:上海五区
     */
    String getZoneNameByCampusAndBizType(String campusName);

    /**
     * 调用getCampus2ZoneInfoMap接口根据ZoneName找CampusName
     *
     * @param zoneName
     * @return
     */
    String getCampusNameByZoneName(String zoneName);

//    /**
//     * 物理机类型到实例类型的 1:1 映射
//     * @return
//     */
//    Map<String, String> getDeviceType2InstanceTypeMap();

    UserPersonalConfigResp getUserPersonalConfig(UserPersonalConfigReq req);

    void saveUserPersonalConfig(UserPersonalConfigReq req);

    List<CrpBizTimeConfigDO> queryBizTimeConfigByGroup(String group, Boolean isFilterNullTime);

    void updateBizTimeConfig(UpdateBizTimeConfigReq req);

    Date getLocalDateByBizTimeWeek(CrpCommonHolidayWeekDO currentWeek, CrpBizTimeConfigDO crpBizTimeConfigDO);

    void refreshHolidayWeek(Integer year);

    CrpCommonHolidayWeekDO binarySearchHolidayWeek(List<CrpCommonHolidayWeekDO> list, String date);

    void eventNotice(String eventCode, String noticeTitle, String noticeContent);

    /**
     * crp_event_notice_config
     * 为避免方法报错阻塞正常流程，此方法内部捕获异常，通知对应研发 <br/><br/>
     * 实际通知的用户，会取方法入参用户和配置表中用户和角色的并集并去重 <br/><br/>
     * 入参或配置的通知内容，会使用模版参数来解析替换到通知内容中 <br/>
     * 例，通知内容为 <br/>
     * 您当前有客户【{customerName}】的{size}条PPL尚未提交 <br/>
     * 模版参数为
     * <blockquote><pre>
     *     Map&lt;String, Object&gt; templateParams = new HashMap&lt;&gt;();
     *     templateParams.put("size", 1);
     *     templateParams.put("customerName", "拼多多");
     * </pre></blockquote>
     * 则实际通知的内容为 <br/>
     * 您当前有客户【拼多多】的1条PPL尚未提交 <br/>
     *
     * @param eventCode 事件Code 必填
     * @param noticeTitle 若为空，则直接用配置表的
     * @param noticeContent 若为空，则直接用配置表的
     * @param templateParams 通知内容的模版参数
     * @param users 需要通知的用户，多个用户用 {@code ; } 隔开
     */
    void eventNotice(String eventCode, String noticeTitle, String noticeContent,
            Map<String, Object> templateParams, String users);

    void eventNotice(String eventCode, String noticeTitle, String noticeContent, String users);


    Boolean checkIsAdmin(String userName);


    List<IndustryDemandIndustryWarZoneDictDO> queryIndustryWarZoneCustomerConfig();

    IndustryDemandIndustryWarZoneDictDO queryWarZoneConfigByCustomerShortName(String industryDept,
            String customerName);

    List<IndustryDemandIndustryWarZoneDictDO> queryEnableIndustryWarZoneCustomerConfig();

    Map<String,List<String>> getUnCustomerShortName2Name();

    void insertOrUpdateIndustryWarZoneCustomerConfig(List<IndustryDemandIndustryWarZoneDictDO> insertOrUpdateList);

    void deletedIndustryWarZoneCustomerConfig(List<Long> ids);

    Map<String, IndustryDemandIndustryWarZoneDictDO> queryCustomerMap();

    /**
     * 获取所有的通用客户简称 映射 客户简称的Map
     *
     * @return
     */
    Map<String, List<IndustryDemandIndustryWarZoneDictDO>> queryCommonCustomerMap();

    /**
     * 获取通用客户简称下 所有的客户简称， 没有则返回本身
     *
     * @param commonCustomerName
     * @return
     */
    List<String> queryAllCustomerShortName(String commonCustomerName);

    /**
     * 查询主力园区
     */
    List<String> queryMainZone();

    InventoryHealthMainZoneNameConfigDO queryMainZoneByRegionName(String regionName);

    /**
     * 查询主力机型
     */
    List<String> queryMainInstanceType();

    List<String> queryRegionByCustomhouseTitle(String customhouseTitle);

    List<SoeRegionNameCountryDO> getRegion2Country();

    Map<String,String> getRegion2CountryMapping();

    Map<String, String> getRegionName2CountryMapping();

    /** 国家 --> 地域 */
    Map<String,List<String>> getCountry2RegionMapping();

    List<PaasProductUinWhiteListDO> getPaasProductUinMapping();

    /** 鉴权 */
    UserPermissionDto getPermissionByUserAndRole(String roleCode, String user);

    List<String> queryInstanceType(String zoneName);

    @Data
    static class IndustryCustomerInfo {
        @Column("industry_dept")
        private String industryDept;
        @Column("war_zone")
        private String warZone;
        @Column("customer_short_name")
        private String customerShortName;
        private String unCustomerShortName;
    }
}
