package cloud.demand.lab.modules.common_dict.DO;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import java.time.LocalDateTime;

@Data
@ToString
@Table("static_zone")
public class StaticZoneDO {

    /** 可用区id<br/>Column: [zoneid] */
    @Column(value = "zoneid", isKey = true)
    private Long zoneid;

    /** 可用区英文标识<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 可用区名称（中文）<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 可用区名称（英文）<br/>Column: [zone_name_en] */
    @Column(value = "zone_name_en")
    private String zoneNameEn;

    /** 可用区名称（日文）<br/>Column: [zone_name_jp] */
    @Column(value = "zone_name_jp")
    private String zoneNameJp;

    /** 可用区名称（韩文）<br/>Column: [zone_name_ko] */
    @Column(value = "zone_name_ko")
    private String zoneNameKo;

    /** 统计时间<br/>Column: [white_zone] */
    @Column(value = "white_zone")
    private String whiteZone;

    /** 销售类型<br/>Column: [sold_object] */
    @Column(value = "sold_object")
    private String soldObject;

    /** 业务类型<br/>Column: [biz_type_list] */
    @Column(value = "biz_type_list")
    private String bizTypeList;

    /** 可用去类型<br/>Column: [zone_type] */
    @Column(value = "zone_type")
    private String zoneType;

    /** 生命周期状态<br/>Column: [life_cycle] */
    @Column(value = "life_cycle")
    private String lifeCycle;

    /** 资源组维护的自增长代理ID，于业务主键原则，未采用此id作为主键。<br/>Column: [region_id] */
    @Column(value = "region_id")
    private Long regionId;

    /** API地域名称<br/>Column: [api_region] */
    @Column(value = "api_region")
    private String apiRegion;

    /** 地域中文名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 地域英文名称<br/>Column: [region_name_en] */
    @Column(value = "region_name_en")
    private String regionNameEn;

    /** 地域日文名称<br/>Column: [region_name_jp] */
    @Column(value = "region_name_jp")
    private String regionNameJp;

    /** 地域韩文名称<br/>Column: [region_name_ko] */
    @Column(value = "region_name_ko")
    private String regionNameKo;

    /** 内部域名样式<br/>Column: [inner_domain_name] */
    @Column(value = "inner_domain_name")
    private String innerDomainName;

    /** 外部域名样式<br/>Column: [outer_domain_name] */
    @Column(value = "outer_domain_name")
    private String outerDomainName;

    /** 地区域标识<br/>Column: [areaid] */
    @Column(value = "areaid")
    private Long areaid;

    /** 地区中文名称。<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地区英文名称。<br/>Column: [area_name_en] */
    @Column(value = "area_name_en")
    private String areaNameEn;

    /** 地区日文名称。<br/>Column: [area_name_jp] */
    @Column(value = "area_name_jp")
    private String areaNameJp;

    /** 地区韩文名称。<br/>Column: [area_name_ko] */
    @Column(value = "area_name_ko")
    private String areaNameKo;

    /** 地区所在关境属id，关联customhouse相关表。<br/>Column: [customhouse_id] */
    @Column(value = "customhouse_id")
    private Long customhouseId;

    /** 关境属性中文名，例如“境内”“境外”<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 关境属性中文全，例如“中国海关关税境内”、“中国海关关税境外”<br/>Column: [customhouse_fullname] */
    @Column(value = "customhouse_fullname")
    private String customhouseFullname;

    /** 统计时间，参考《规范数据管规范》<br/>Column: [stattime] */
    @Column(value = "stattime")
    private LocalDateTime stattime;

    /** 同步时间，参见《指标数据管规范》<br/>Column: [synctime] */
    @Column(value = "synctime")
    private LocalDateTime synctime;


}
