package cloud.demand.lab.modules.common_dict.DO;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.modules.forecast.DO.PplForecastInputDetailDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/** 地域到国家映射 */
@Data
@Table("bas_soe_region_name_country")
public class SoeRegionNameCountryDO {

    public static DBHelperWithClz<SoeRegionNameCountryDO> db(){
        return DBHelperWithClz.create(DBList.demandDBHelper);
    }


    /** 地域 */
    @Column("region")
    private String region;

    /** 地域 */
    @Column("region_name")
    private String regionName;

    /** 国家 */
    @Column("country_name")
    private String countryName;

    /** 国内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

}
