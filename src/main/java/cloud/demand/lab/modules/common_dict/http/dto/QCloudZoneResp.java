package cloud.demand.lab.modules.common_dict.http.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/27 22:11
 */
@Data
public class QCloudZoneResp {

    @JsonProperty("code")
    private String code;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("data")
    List<Item> data;

    @Data
    public static class Item{

        @JsonProperty("qcloud_zone_campus")
        private List<String> campusName;

        @JsonProperty("qcloud_zone_business_type")
        private List<String> businessType;

        @JsonProperty("qcloud_zone_name")
        private String zoneName;

        @JsonProperty("qcloud_zone_id")
        private String zoneId;
    }
}
