package cloud.demand.lab.modules.common_dict.DO;

import cloud.demand.lab.modules.operation_view.entity.plan.StaticStockPrincipalHosttypeDO;
import java.util.Objects;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("static_principal_ginsfamily")
public class StaticPrincipalGinsfamilyDO {

    @Column(value = "ginsfamily", isKey = true)
    private String ginsfamily;

    @Override
    public boolean equals(Object o) {
        // 如果是同一个对象，直接返回true
        if (this == o) {
            return true;
        }
        // 如果对象为null或类型不匹配，返回false
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StaticPrincipalGinsfamilyDO o1 = (StaticPrincipalGinsfamilyDO) o;

        return Objects.equals(ginsfamily, o1.getGinsfamily());
    }


    @Override
    public int hashCode() {
        return Objects.hash(ginsfamily);
    }

}
