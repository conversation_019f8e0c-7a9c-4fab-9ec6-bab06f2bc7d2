package cloud.demand.lab.modules.common_dict.enums;

import java.util.List;
import java.util.Objects;
import lombok.Getter;
import org.nutz.lang.Lang;

/**
 * 产品类型枚举对象
 */
@Getter
public enum ProductTypeEnum {

    CVM("CVM", "腾讯云CVM"),
    GPU("GPU", ""),
    METAL("裸金属", ""),
    CBS("CBS", "腾讯云CBS"),
    COS("COS", ""),
    CDB("CDB", ""),
    NETWORK("网络", ""),
    CRS("CRS", ""),
    CMONGO("CMONGO", "");

    private final String code;
    private final String name;

    ProductTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductTypeEnum getByCode(String code) {
        for (ProductTypeEnum e : ProductTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static List<String> getAllProductType() {
        List<String> result = Lang.list();
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            result.add(value.getCode());
        }
        return result;
    }
}
