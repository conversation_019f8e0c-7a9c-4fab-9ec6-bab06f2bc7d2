package cloud.demand.lab.modules.gpu_report.service;

import com.pugwoo.dbhelper.annotation.Column;
import java.util.List;
import lombok.Data;

public interface GpuDictService {

    /** 获取 ppl 转订单的 ppl-id */
    public List<String> getSourcePpl(String orderCategory);

    public boolean hasGpuPermission();

    public List<String> getPplYearMonthRange(String yearMonth);

    @Data
    class YearMonthRange {
        @Column("start_year_month")
        private String startYearMonth;
        @Column("end_year_month")
        private String endYearMonth;
    }
}
