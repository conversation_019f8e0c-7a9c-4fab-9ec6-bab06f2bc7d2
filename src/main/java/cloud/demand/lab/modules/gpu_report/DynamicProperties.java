package cloud.demand.lab.modules.gpu_report;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;
import java.util.function.Supplier;
import yunti.boot.config.DynamicProperty;

public class DynamicProperties {
    private static final Supplier<Map<String, String>> gpuInventoryGpuCardTypeMap =
            DynamicProperty.create("app.config.gpu_inventory.gpu_card_type.map", "{}",
                    e -> {
                        ObjectMapper objectMapper = new ObjectMapper();
                        return objectMapper.readValue(e, new TypeReference<Map<String, String>>() {
                        });
                    });

    /** gpu 在线库存 gpu 卡型映射 */
    public static Map<String,String> getGpuInventoryGpuCardTypeMap(){

        return gpuInventoryGpuCardTypeMap.get();
    }
}
