package cloud.demand.lab.modules.gpu_report.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * 订单来源枚举类
 */
@Getter
public enum OrderSourceEnum {

    PPL_TRANSFORM("PPL_TRANSFORM", "PPL转单"),

    URGENT_ORDER("URGENT_ORDER", "紧急订单"),

    ;

    private final String code;

    private final String name;

    OrderSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderSourceEnum getByCode(String code) {
        for (OrderSourceEnum e : OrderSourceEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        OrderSourceEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
