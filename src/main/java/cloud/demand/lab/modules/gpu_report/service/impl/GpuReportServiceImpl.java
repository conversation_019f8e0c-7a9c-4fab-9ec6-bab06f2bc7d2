package cloud.demand.lab.modules.gpu_report.service.impl;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.gpu_report.dto.GpuDetailReportReq;
import cloud.demand.lab.modules.gpu_report.dto.GpuOrderDetailResp;
import cloud.demand.lab.modules.gpu_report.dto.GpuRankReq;
import cloud.demand.lab.modules.gpu_report.dto.GpuRankResp;
import cloud.demand.lab.modules.gpu_report.dto.GpuReportReq;
import cloud.demand.lab.modules.gpu_report.dto.GpuSummaryResp;
import cloud.demand.lab.modules.gpu_report.dto.GpuSummaryResp.Item;
import cloud.demand.lab.modules.gpu_report.enums.GpuIndexEnum;
import cloud.demand.lab.modules.gpu_report.enums.GpuItemFieldEnum;
import cloud.demand.lab.modules.gpu_report.enums.GpuQueryProcessEnum;
import cloud.demand.lab.modules.gpu_report.model.excel.GpuOrderDemandModel;
import cloud.demand.lab.modules.gpu_report.model.excel.GpuSummaryModel;
import cloud.demand.lab.modules.gpu_report.service.GpuReportService;
import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import cloud.demand.lab.modules.operation_view.entity.web.common.StreamDownloadBean;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.google.common.base.Objects;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import report.utils.bean.QueryProcessFactory;
import report.utils.model.item.ReportItem;
import report.utils.model.item.ReportMapIndexItem;
import report.utils.model.process.QueryProcess;
import report.utils.model.process.QueryProcessInstance;
import report.utils.model.step.IIndexData;
import report.utils.utils.ReportCommonUtils;
import report.utils.utils.ReportExcelUtils;
import yunti.boot.exception.BizException;

/**
 * Gpu专项
 */
@Service
@Slf4j(topic = "GPU专项")
public class GpuReportServiceImpl implements GpuReportService {

    @Resource
    private QueryProcessFactory factory;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DictService dictService;


    @Override
    public List<ReportMapIndexItem> queryGpuReport(GpuReportReq req) {
        // 1.校验 dims
        req.check(new HashSet<>(GpuItemFieldEnum.getFields()));
        // 2. 鉴权
        if (!req.auth()){
            return ListUtils.newList();
        }
        req.fill();
        // 2. 构建查询流程
        QueryProcess process = factory.getProcess(GpuQueryProcessEnum.GPU_REPORT.getName());
        // 3. 执行查询流程
        List<ReportMapIndexItem> ret = (List<ReportMapIndexItem>) process.process(ImmutableMap.of("req", req));
        // step 4：排序 + 分页
        ret = ReportMapIndexItem.sortAndLimit(ret, req.getOrderByDim(), req.getOrderByField(), req.getPage());
        return ret;
    }

    @HiSpeedCache(expireSecond = 1800, keyScript = "args[0]")
    @Override
    public Map<String, List<String>> getWarZone2CustomerShortName(String statTime) {
        Map<String, List<String>> map = dictService.queryWarZoneToCustomerShortName();
        Set<String> gridCustomerShortName = new HashSet<>(getGridCustomerShortName(statTime));
        Map<String, List<String>> ret = new HashMap<>();
        map.forEach((k, v) -> {
            List<String> collect = v.stream().filter(gridCustomerShortName::contains).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(collect)) {
                ret.put(k, collect);
            }
        });
        return ret;
    }

    @Override
    public GpuSummaryResp getGpuSummary(GpuReportReq req) {
        req.setIndexes(ListUtils.newList( // 翻转一下，这里入参是需要跳过的指标
                GpuIndexEnum.TOTAL_DEMAND.getName(),  // 总需求
                GpuIndexEnum.WITHHOLDING_SCALE.getName(), // 预扣已购买
                GpuIndexEnum.WITHHOLDING_NOT_SCALE.getName(),  // 预扣未购买
                GpuIndexEnum.WAIT_SATISFACTION.getName()));  // 待满足
        req.configWithQueryAll(); // 不分页
        req.setDims(ListUtils.newList(GpuItemFieldEnum.yearMonth.name())); // 只要年月维度
        List<ReportMapIndexItem> reportMapIndexItems = queryGpuReport(req);
        GpuSummaryResp ret = new GpuSummaryResp();
        for (ReportMapIndexItem indexItem : reportMapIndexItems) {
            Map<String, Object> dimMap = indexItem.getDimMap();
            String yearMonth = (String) dimMap.get(GpuItemFieldEnum.yearMonth.name()); // 年月
            Map<String, ReportItem> total = indexItem.getTotal();
            ReportItem orderDemand = total.get(GpuIndexEnum.TOTAL_DEMAND.getName()); // 总需求
            ReportItem withholdingScale = total.get(GpuIndexEnum.WITHHOLDING_SCALE.getName()); // 预扣已购买
            ReportItem withholdingNotScale = total.get(GpuIndexEnum.WITHHOLDING_NOT_SCALE.getName()); // 预扣未购买
            ReportItem waitSatisfaction = total.get(GpuIndexEnum.WAIT_SATISFACTION.getName()); // 待满足
            ret.addItem(yearMonth,
                    GpuUtils.getBigDecimal(orderDemand, ReportItem::getNumber),
                    GpuUtils.getBigDecimal(withholdingScale, ReportItem::getNumber),
                    GpuUtils.getBigDecimal(withholdingNotScale, ReportItem::getNumber),
                    GpuUtils.getBigDecimal(waitSatisfaction, ReportItem::getNumber));
        }
        // 按年月降序
        if (ListUtils.isNotEmpty(ret.getItems())) {
            ret.getItems().sort(Comparator.comparing(Item::getYearMonth));
        }
        return ret;
    }

    @Override
    public GpuRankResp getGpuRank(GpuRankReq req) {
        List<String> dims = req.getDims();
        List<String> fields = req.getFields();
        // 1. 参数校验
        if (ListUtils.isNotEmpty(fields)) {
            throw new BizException("排行不支持字段");
        }
        if (ListUtils.isEmpty(dims)) {
            throw new BizException("排行不支持空维度");
        }
        if (dims.size() != 1) {
            throw new BizException("排行只支持单维度");
        }
        List<String> indexes = req.getIndexes();
        if (ListUtils.isEmpty(indexes)) {
            throw new BizException("排行不支持空指标");
        }
        if (indexes.size() != 1) {
            throw new BizException("排行只支持单指标");
        }
        req.configWithQueryAll();
        String indexName = indexes.get(0); // 指标名，例子：缺口
        String dim = dims.get(0); // 维度，例子：yearMonth
        List<ReportMapIndexItem> items = queryGpuReport(req);
        // 2. 构建排行
        GpuRankResp ret = new GpuRankResp();
        String indexField = req.getIndexField();
        int index = Objects.equal(indexField, GpuItemFieldEnum.cpuNumber.name()) ? 0 : 1; // cpu 是 0，gpu 是 1
        items.forEach(item -> {
            Map<String, Object> dimMap = item.getDimMap();
            String title = (String) dimMap.get(dim);
            Map<String, ReportItem> total = item.getTotal();
            ReportItem reportItem = total.get(indexName);
            ret.addItem(title, GpuUtils.getBigDecimal(reportItem, ReportItem::getNumber), index);
        });
        ret.sortAndFillRatio(req.getTop());
        return ret;
    }

    @Override
    public ResponseEntity<InputStreamResource> export(GpuReportReq req) {
        // 全维度
        req.setDims(GpuItemFieldEnum.getFields());
        req.configWithQueryAll();
        req.fill();
        GpuSummaryModel model = new GpuSummaryModel();
        // 鉴权通过
        if (req.auth()){
            // 2. 构建查询流程
            QueryProcess process = factory.getProcess(GpuQueryProcessEnum.GPU_REPORT_EXPORT.getName());
            QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
            // step3：构建导出 model
            Map<String, List<IIndexData>> stepCacheData = instance.getStepCacheData();
            ReportExcelUtils.setModel(stepCacheData, model, req.getIndexes());
            List<GpuOrderDemandModel> total = new ArrayList<>();
            if (ListUtils.isNotEmpty(model.getOrderDemand())) {
                total.addAll(ListUtils.transform(model.getOrderDemand(), GpuOrderDemandModel::fromOrder));
            }
            if (ListUtils.isNotEmpty(model.getPplDemand())) {
                total.addAll(ListUtils.transform(model.getPplDemand(), GpuOrderDemandModel::fromPPl));
            }
            model.setTotalDemand(total);
            model.clean(); // 清洗
        }

        ByteArrayInputStream in = ReportExcelUtils.toExcel(model, GpuSummaryModel.class, "供需分析报表");
        String filename =
                "供需分析报表" + "-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8)
                        + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public GpuOrderDetailResp getGpuOrderDetail(GpuDetailReportReq req) {
        req.configWithQueryAll();
        String fromIndex = req.getIndex(); // 下钻进来的指标
        boolean isOnlyPpl = Objects.equal(fromIndex, GpuIndexEnum.PPL_DEMAND.getName());
        boolean isOnlyOrder = ListUtils.newList(
                GpuIndexEnum.ORDER_DEMAND.getName(),
                GpuIndexEnum.WITHHOLDING.getName(),
                GpuIndexEnum.WITHHOLDING_SCALE.getName(),
                GpuIndexEnum.WITHHOLDING_NOT_SCALE.getName()).contains(fromIndex); // 订单和预扣只看订单
        // 主 key 匹配维度
        req.setDims(ListUtils.newList(
                GpuItemFieldEnum.yearMonth.name(),
                GpuItemFieldEnum.orderNumber.name(),
                GpuItemFieldEnum.zoneName.name(),
                GpuItemFieldEnum.instanceType.name()));
        // 非主 key 匹配字段
        req.setFields(ListUtils.subtract(GpuItemFieldEnum.getOrderDemandDetailFields(), req.getDims()));
        List<String> indexes = ListUtils.newList(
                GpuIndexEnum.ORDER_DEMAND.getName(), // 订单需求
                GpuIndexEnum.PPL_DEMAND.getName(), // PPL未转单
                GpuIndexEnum.WITHHOLDING.getName(), // 已预扣量
                GpuIndexEnum.WITHHOLDING_SCALE.getName() // 已预扣量已购买
        );
        if (isOnlyOrder){
            indexes = ListUtils.newList(
                    GpuIndexEnum.ORDER_DEMAND.getName(), // 订单需求
                    GpuIndexEnum.WITHHOLDING.getName(), // 已预扣量
                    GpuIndexEnum.WITHHOLDING_SCALE.getName() // 已预扣量已购买
            );
        }else if (isOnlyPpl){
            indexes = ListUtils.newList(
                    GpuIndexEnum.PPL_DEMAND.getName()); // PPL未转单
        }
        req.setIndexes(indexes);
        List<ReportMapIndexItem> items = queryGpuReport(req);
        GpuOrderDetailResp ret = new GpuOrderDetailResp();
        GpuItemFieldEnum[] dimArr = GpuItemFieldEnum.getEnum(req.getDims());
        GpuItemFieldEnum[] fieldArr = GpuItemFieldEnum.getEnum(req.getFields());
        Map<String, BiConsumer<Object, Object>> dimFuncMap = Arrays.stream(dimArr).collect(Collectors.toMap(Enum::name,
                GpuItemFieldEnum::getFieldSetter)); // 维度（行）
        Map<String, BiConsumer<Object, Object>> fieldFuncMap = Arrays.stream(fieldArr)
                .collect(Collectors.toMap(Enum::name,
                        GpuItemFieldEnum::getFieldSetter)); // 字段（列）

        for (ReportMapIndexItem item : items) {
            Map<String, Object> dimMap = item.getDimMap();
            Map<String, List<ReportItem>> data = item.getData();
            List<ReportItem> orderDemandList = isOnlyPpl ? null : data.get(GpuIndexEnum.ORDER_DEMAND.getName()); // 订单需求
            List<ReportItem> pplDemandList = isOnlyOrder ? null : data.get(GpuIndexEnum.PPL_DEMAND.getName()); // PPL未转单
            List<ReportItem> withholdingList = isOnlyPpl ? null : data.get(GpuIndexEnum.WITHHOLDING.getName()); // 已预扣量
            List<ReportItem> withholdingScaleList = isOnlyPpl ? null : data.get(GpuIndexEnum.WITHHOLDING_SCALE.getName()); // 已预扣已购买
            Map<String, ReportItem> withholdingScaleMap =
                    ListUtils.isNotEmpty(withholdingScaleList) ? ListUtils.toMap(withholdingScaleList,
                            ReportItem::getLabel,
                            Function.identity()) : new HashMap<>();
            List<GpuOrderDetailResp.Item> resItemList = new ArrayList<>();
            // 订单
            if (ListUtils.isNotEmpty(orderDemandList)) {
                for (ReportItem reportItem : orderDemandList) {
                    BigDecimal gpuNum = ReportCommonUtils.getBigDecimal(reportItem, ReportItem::getNumber);
                    if (gpuNum == null || gpuNum.compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    GpuOrderDetailResp.Item resItem = new GpuOrderDetailResp.Item();
                    dimFuncMap.forEach((k, v) -> v.accept(resItem, dimMap.get(k)));
                    Map<String, Object> fields = reportItem.getFields();
                    fieldFuncMap.forEach((k, v) -> v.accept(resItem, fields.get(k)));
                    resItem.setOrderDemandNum(gpuNum);
                    resItem.setIndex(GpuIndexEnum.ORDER_DEMAND.getName());
                    resItemList.add(resItem);
                }
                // 按起始购买时间升序
                resItemList.sort(Comparator.comparing(GpuOrderDetailResp.Item::getBeginBuyDate));
                if (ListUtils.isNotEmpty(withholdingList)) {
                    // 匹配预扣
                    int index = 0; // 取第一个，后续优化考虑超过总需求的部分分给后续 index
                    for (ReportItem reportItem : withholdingList) {
                        BigDecimal withholdingGpuNum = ReportCommonUtils.getBigDecimal(reportItem,
                                ReportItem::getNumber);
                        if (withholdingGpuNum == null || withholdingGpuNum.compareTo(BigDecimal.ZERO) == 0){
                            continue;
                        }
                        ReportItem scaleReport = withholdingScaleMap.get(reportItem.getLabel());
                        GpuOrderDetailResp.Item orderDemand = resItemList.get(index);
                        orderDemand.setWithholdingNum(withholdingGpuNum);
                        if (scaleReport != null) {
                            orderDemand.setWithholdingScaleNum(
                                    ReportCommonUtils.getBigDecimal(scaleReport, ReportItem::getNumber));
                        }
                    }
                }
            } else if (ListUtils.isNotEmpty(withholdingList)) {
                for (ReportItem reportItem : withholdingList) {
                    BigDecimal withholdingGpuNum = ReportCommonUtils.getBigDecimal(reportItem, ReportItem::getNumber);
                    if (withholdingGpuNum == null || withholdingGpuNum.compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    GpuOrderDetailResp.Item resItem = new GpuOrderDetailResp.Item();
                    dimFuncMap.forEach((k, v) -> v.accept(resItem, dimMap.get(k)));
                    Map<String, Object> fields = reportItem.getFields();
                    fieldFuncMap.forEach((k, v) -> v.accept(resItem, fields.get(k)));
                    resItem.setWithholdingNum(withholdingGpuNum);
                    ReportItem scaleReport = withholdingScaleMap.get(reportItem.getLabel());
                    if (scaleReport != null) {
                        resItem.setWithholdingScaleNum(
                                ReportCommonUtils.getBigDecimal(scaleReport, ReportItem::getNumber));
                    }
                    resItem.setIndex(GpuIndexEnum.ORDER_DEMAND.getName());
                    resItemList.add(resItem);
                }
            }
            // PPL 未转单
            if (ListUtils.isNotEmpty(pplDemandList)) {
                for (ReportItem reportItem : pplDemandList) {
                    BigDecimal gpuNum = ReportCommonUtils.getBigDecimal(reportItem, ReportItem::getNumber);
                    if (gpuNum == null || gpuNum.compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    GpuOrderDetailResp.Item resItem = new GpuOrderDetailResp.Item();

                    dimFuncMap.forEach((k, v) -> v.accept(resItem, dimMap.get(k)));
                    Map<String, Object> fields = reportItem.getFields();
                    fieldFuncMap.forEach((k, v) -> v.accept(resItem, fields.get(k)));
                    resItem.setOrderDemandNum(gpuNum);
                    resItem.setIndex(GpuIndexEnum.PPL_DEMAND.getName());
                    resItemList.add(resItem);
                }
            }

            if (ListUtils.isNotEmpty(resItemList)) {
                resItemList.forEach(ret::addItem);
            }
        }
        return ret;
    }

    @Override
    public ResponseEntity<InputStreamResource> exportCurView(GpuReportReq req) {
        req.configWithOutPage();
        List<ReportMapIndexItem> reportMapIndexItems = queryGpuReport(req);
        Map<String, String> fieldMap = Arrays.stream(GpuItemFieldEnum.values())
                .collect(Collectors.toMap(GpuItemFieldEnum::name, GpuItemFieldEnum::getName));
        Map<String, Integer> indexOrderMap = Arrays.stream(GpuIndexEnum.values())
                .collect(Collectors.toMap(GpuIndexEnum::getName, GpuIndexEnum::getOrder));
        String product = req.getProduct();
        // CVM 看核，GPU 看卡
        int index = Objects.equal(product, Ppl13weekProductTypeEnum.CVM.getCode()) ? 0 : 1;
        String[] sheetName = new String[]{"核数","卡数"};
        ByteArrayInputStream inputStream = ReportExcelUtils.toExcelView(reportMapIndexItems,fieldMap, indexOrderMap,"供需分析视图", index,sheetName[index]);
        String filename =
                "供需分析视图" + "-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8)
                        + ".xlsx";
        return new StreamDownloadBean(filename, inputStream);
    }


    private List<String> getGridCustomerShortName(String statTime) {
        String sql = "select DISTINCT common_customer_short_name\n"
                + "from std_crp.aws_order_grid_detail_df\n"
                + "where stat_time = ${statTime}\n"
                + "  AND order_category = 'GPU'\n"
                + "  and grid_status in ('idle','occupied','migrate') -- 空闲，占用，迁移中";
        if (StringUtils.isNotBlank(statTime)) {
            sql = sql.replace("${statTime}", "'" + statTime + "'");
        } else {
            sql = sql.replace("${statTime}", "(select max(stat_time) from std_crp.aws_order_grid_detail_df)");
        }
        return ckcldStdCrpDBHelper.getRaw(String.class, sql);
    }
}
