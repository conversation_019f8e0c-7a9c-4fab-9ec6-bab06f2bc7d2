package cloud.demand.lab.modules.gpu_report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import report.utils.model.process.IQueryProcessEnum;

@Getter
@AllArgsConstructor
public enum GpuQueryProcessEnum implements IQueryProcessEnum {

    GPU_REPORT("GPU_REPORT","GPU专项报表"),

    GPU_REPORT_EXPORT("GPU_REPORT_EXPORT","GPU专项报表导出"),

    ;

    private final String name;
    private final String desc;

}
