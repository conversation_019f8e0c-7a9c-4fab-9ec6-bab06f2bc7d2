package cloud.demand.lab.modules.gpu_report.model.db;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

/** GPU在线库存 */
@Data
public class GpuOnlineInventory {
    /** GPU 卡型 */
    @Column("gpu_type")
    private String gpuCardType;
    /** 可用区 id */
    @Column("zone_id")
    private Integer zoneId;
    /** 卡数 */
    @Column("gpu_number")
    private BigDecimal gpuNumber;
}
