package cloud.demand.lab.modules.gpu_report.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import report.utils.entity.BasVersionCommonDO;

/** gpu 采购数据
 * 业务导入的数据，
 * 必填：国家，卡型，供应年月，GPU卡数
 * 非必填：地域
 * 自动生成：境内外，区域
 * */
@Table("gpu_purchase_data")
@Data
public class GpuPurchaseDataDO extends BasVersionCommonDO {
    /** 自增 id<br/>Column: [id] */
    @ExcelIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    @ExcelIgnore
    private String customhouseTitle;

    /** 国家 */
    @Column(value = "country_name")
    @ExcelProperty(value = "国家(必填)", index = 0)
    private String countryName;

    /** 区域 */
    @Column(value = "area_name")
    @ExcelIgnore
    private String areaName;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    @ExcelProperty(value = "行业部门(非必填)", index = 1)
    private String industryDept;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    @ExcelIgnore
    private String warZone;

    /** 通用客户简称<br/>Column: [un_customer_short_name] */
    @Column(value = "un_customer_short_name")
    @ExcelIgnore
    private String unCustomerShortName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    @ExcelProperty(value = "客户简称(非必填)", index = 2)
    private String customerShortName;

    /** 地域 */
    @Column(value = "region_name")
    @ExcelProperty(value = "地域(非必填)", index = 3)
    private String regionName;

    /** 卡型 */
    @Column(value = "gpu_card_type")
    @ExcelProperty(value = "GPU卡型(必填)", index = 4)
    private String gpuCardType;

    /** 年月（供应年月） */
    @Column(value = "year_month")
    @ExcelProperty(value = "供应年月(必填)", index = 5)
    @ContentStyle(dataFormat = 49)
    private String yearMonth;

    /** gpu 卡数 */
    @Column(value = "gpu_number")
    @ExcelProperty(value = "GPU卡数(必填)", index = 6)
    private BigDecimal gpuNumber;
}
