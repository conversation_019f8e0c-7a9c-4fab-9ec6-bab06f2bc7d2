package cloud.demand.lab.modules.gpu_report.store;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.gpu_report.dto.GpuReportReq;
import cloud.demand.lab.modules.gpu_report.enums.GpuIndexEnum;
import cloud.demand.lab.modules.gpu_report.enums.GpuItemFieldEnum;
import cloud.demand.lab.modules.gpu_report.model.GpuItem;
import cloud.demand.lab.modules.gpu_report.model.db.GpuOnlineInventory;
import cloud.demand.lab.modules.gpu_report.service.GpuDictService;
import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.google.common.base.Objects;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import report.utils.anno.StoreRegister;
import report.utils.anno.StoreRegisterClient;
import report.utils.model.store.IDBData;
import report.utils.utils.ReportDateUtils;
import report.utils.utils.WhereUtils;

@Service
@StoreRegisterClient
public class GpuStoreService {

    /** ck */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /** demand */
    @Resource
    private DBHelper demandDBHelper;

    /** gpu 字典 */
    @Resource
    private GpuDictService gpuDictService;

    /** gpu 库存 */
    @Resource
    private DBHelper gpuDBHelper;

    @Resource
    private DictService dictService;


    @StoreRegister(name = "GPU_INIT", desc = "初始化")
    public List<IDBData> init(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req"); // 请求
        return null;
    }



    @StoreRegister(name = "GPU_ORDER_DEMAND", desc = "GPU-订单需求")
    public List<GpuItem> getOrderDemand(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        String sql = ORMUtils.getSql("/sql/gpu/order_demand.sql");
        // 有未满足的指标，则强制加上 match key
        List<String> groupBy = getGroupByWithMatchKey(req);
        Tuple2<String, WhereSQL> tp = buildSql(req, 1, groupBy, sql, null, true, true);
        List<Map> ret = demandDBHelper.getRaw(Map.class, tp._1(), tp._2().getParams());
        return transformMap(ret,groupBy,true);
    }

    @StoreRegister(name = "GPU_PPL_DEMAND", desc = "GPU-未转单PPL")
    public List<GpuItem> getPplDemand(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        List<String> sourcePpl = gpuDictService.getSourcePpl(req.getProduct()); // ppl 转订单的 ppl-id
        WhereSQL extWhereSql = new WhereSQL();
        extWhereSql.andIf(ListUtils.isNotEmpty(sourcePpl), "ppl_order not in (?)", sourcePpl);
        List<String> ymRange = gpuDictService.getPplYearMonthRange(GpuUtils.getYearMonth(YearMonth.now()));
        extWhereSql.and("formatDateTime(begin_buy_date, '%Y-%m') >= ?",ymRange.get(0));
        extWhereSql.and("formatDateTime(begin_buy_date, '%Y-%m') <= ?",ymRange.get(ymRange.size()-1));
        String sql = ORMUtils.getSql("/sql/gpu/ppl_demand.sql");
        List<String> groupBy = getGroupByWithMatchKey(req);
        Tuple2<String, WhereSQL> tp = buildSql(req, 2, groupBy, sql, extWhereSql, true, true);
        List<Map> ret = ckcldStdCrpDBHelper.getRaw(Map.class, tp._1(), tp._2().getParams());
        return transformMap(ret,groupBy,true);
    }

    @StoreRegister(name = "GPU_SCALE", desc = "GPU-已购买")
    public List<GpuItem> getScale(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        String startYearMonth = req.getStartYearMonth();
        String endYearMonth = req.getEndYearMonth();
        List<String> yearMonthEndDay = ReportDateUtils.getYearMonthEndDay(startYearMonth, endYearMonth,
                LocalDate.of(2024, 1, 1), // 最小日期
                LocalDate.now().plusDays(-1)); // 最大日期
        WhereSQL extWhereSql = new WhereSQL();
        extWhereSql.and("stat_time in (?)", yearMonthEndDay);
        String sql = ORMUtils.getSql("/sql/gpu/scale.sql");
        List<String> groupBy = GpuItemFieldEnum.appendScaleDistinctKey(req.getGroupBy());
        // 按驾驶舱的去重逻辑：行业部门&实例类型&可用区&年月
        Tuple2<String, WhereSQL> tp = buildSql(req, 3, groupBy, sql, extWhereSql, true, true);
        List<Map> ret = ckcldStdCrpDBHelper.getRaw(Map.class, tp._1(), tp._2().getParams());
        return transformMap(ret,groupBy,true);
    }

    @StoreRegister(name = "GPU_WITHHOLDING", desc = "GPU-已预扣")
    public List<GpuItem> getWithholding(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        String sql = ORMUtils.getSql("/sql/gpu/withholding.sql");
        List<String> dims = new ArrayList<>(ObjectUtils.defaultIfNull(getGroupByWithMatchKey(req), new ArrayList<>()));
        // 加上预扣状态字段(有其他步骤依赖了这个，加了这个维度后再通过这个维度取过滤，比如已预扣-已购买和已预扣-未购买)
        dims.add(GpuItemFieldEnum.gridStatus.name());
        Tuple2<String, WhereSQL> tp = buildSql(req, 4, dims, sql, null, true, true);
        List<Map> ret = ckcldStdCrpDBHelper.getRaw(Map.class, tp._1(), tp._2().getParams());
        return transformMap(ret,dims,true);
    }

    @StoreRegister(name = "GPU_INVENTORY", desc = "GPU-在线库存")
    public List<GpuItem> getInventory(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        List<GpuItem> ret = new ArrayList<>();
        // 非全行业部门，则直接返回空集合
        if (BooleanUtils.isNotTrue(req.getIsAllIndustryDept())){
            return ret;
        }
        String orderCategory = req.getProduct();
        // 非 GPU 产品，则直接返回空集合
        if (!Objects.equal(orderCategory, Ppl13weekProductTypeEnum.GPU.getCode())){
            return ret;
        }
        YearMonth now = YearMonth.now();
        String ym = ReportDateUtils.getYearMonth(now.getYear(), now.getMonthValue());
        String startSupplyYearMonth = req.getStartSupplyYearMonth();
        String endSupplyYearMonth = req.getEndSupplyYearMonth();
        // 供应年月不包括当月，直接返回空数据
        if (StringUtils.compare(ym, startSupplyYearMonth) < 0 || StringUtils.compare(ym, endSupplyYearMonth) > 0){
            return ret;
        }
        List<GpuOnlineInventory> dbData = gpuDBHelper.getRaw(GpuOnlineInventory.class,
                ORMUtils.getSql("/sql/gpu/gpu_inventory.sql"));

        if (ListUtils.isEmpty(dbData)){
            return ret;
        }
        // gpu 在线库存底数只有 zone_id，需要清洗 可用区，地域，区域，国家和境内外
        Map<Integer, TxyRegionInfoDTO> allTxyRegionInfo = dictService.getAllTxyRegionInfoMap();
        for (GpuOnlineInventory dbDatum : dbData) {
            GpuItem item = new GpuItem();
            item.setGpuNumber(dbDatum.getGpuNumber());
            String gpuCardType = dbDatum.getGpuCardType();
            // 卡型处理，例子：H20-96G 需要去掉-96G，只保留 H20 这个字段
            item.setGpuCardType(GpuUtils.transformGpuCardType(gpuCardType));
            Integer zoneId = dbDatum.getZoneId();
            TxyRegionInfoDTO txyRegionInfoDTO = allTxyRegionInfo.get(zoneId);
            if (txyRegionInfoDTO != null){
                 item.setZoneName(txyRegionInfoDTO.getZoneName());
                 item.setRegionName(txyRegionInfoDTO.getRegionName());
                 item.setAreaName(txyRegionInfoDTO.getAreaName());
                 item.setCountryName(txyRegionInfoDTO.getCountry());
                 item.setCustomhouseTitle(txyRegionInfoDTO.getCustomhouseTitle());
            }else {
                item.setZoneName(Constant.EMPTY_VALUE_STR);
                item.setRegionName(Constant.EMPTY_VALUE_STR);
                item.setAreaName(Constant.EMPTY_VALUE_STR);
                item.setCountryName(Constant.EMPTY_VALUE_STR);
                item.setCustomhouseTitle(Constant.EMPTY_VALUE_STR);
            }
            item.setYearMonth(ym);
            ret.add(item);
        }
        // 过滤
        List<Predicate<GpuItem>> filter = new ArrayList<>();
        GpuUtils.addFilter(filter, req.getCustomhouseTitle(), GpuItem::getCustomhouseTitle);
        GpuUtils.addFilter(filter, req.getCountryName(), GpuItem::getCountryName);
        GpuUtils.addFilter(filter, req.getAreaName(), GpuItem::getAreaName);
        GpuUtils.addFilter(filter, req.getRegionName(), GpuItem::getRegionName);
        GpuUtils.addFilter(filter, req.getZoneName(), GpuItem::getZoneName);
        GpuUtils.addFilter(filter, req.getGpuCardType(), GpuItem::getGpuCardType);
        if (ListUtils.isNotEmpty(filter)){
            ret =  ret.stream().filter(item -> filter.stream().allMatch(p -> p.test(item))).collect(Collectors.toList());
        }
        return ret;
    }

    @StoreRegister(name = "GPU_PURCHASING", desc = "GPU-采购在途")
    public List<GpuItem> getPurchasing(Map<String, Object> params) {
        GpuReportReq req = (GpuReportReq) params.get("req");
        // 非全行业部门，则直接返回空集合
        if (BooleanUtils.isNotTrue(req.getIsAllIndustryDept())){
            return new ArrayList<>();
        }
        String orderCategory = req.getProduct();
        // 非 GPU 产品，则直接返回空集合
        if (!Objects.equal(orderCategory, Ppl13weekProductTypeEnum.GPU.getCode())){
            return new ArrayList<>();
        }
        String sql = ORMUtils.getSql("/sql/gpu/gpu_purchase.sql");
        List<String> dims = new ArrayList<>(ObjectUtils.defaultIfNull(getGapGroupByWithMatchKey(req), new ArrayList<>()));
        Tuple2<String, WhereSQL> tp = buildSql(req, 5, dims, sql, null, true, true);
        List<Map> retMap = demandDBHelper.getRaw(Map.class, tp._1(), tp._2().getParams());
        return transformMap(retMap,dims,true);
    }

    /**
     * 订单需求，未转单 PPL，和已预扣。需要调用该方法来实现强制 group by
     * @param req 请求
     * @return
     */
    private List<String> getGroupByWithMatchKey(GpuReportReq req) {
        List<String> groupBy = req.getGroupBy();
        // 全指标 or 维度包含待满足
        if (ListUtils.isEmpty(req.getIndexes()) || req.getIndexes().contains(GpuIndexEnum.WAIT_SATISFACTION.getName())){
            groupBy = GpuItemFieldEnum.appendMatchKey(groupBy);
        }
        return groupBy;
    }

    /**
     * 待满足 和 在线库存，采购在途对冲维度：国家，gpu 卡型
     * @param req 请求
     * @return
     */
    private List<String> getGapGroupByWithMatchKey(GpuReportReq req){
        List<String> groupBy = req.getGroupBy();
        // 全指标 or 维度包含待满足
        if (ListUtils.isEmpty(req.getIndexes()) || req.getIndexes().contains(GpuIndexEnum.CUR_GAP.getName()) ||
                 req.getIndexes().contains(GpuIndexEnum.CUM_GAP.getName())){
            groupBy = GpuItemFieldEnum.appendGapMatchKey(groupBy);
        }
        return groupBy;
    }

    /**
     * 转换
     * @param mapList map 数据集
     * @param dims 维度
     * @param needIndex 是否需要指标（gpuNumber）
     * @return
     */
    public List<GpuItem> transformMap(List<Map> mapList, List<String> dims, boolean needIndex){
        List<GpuItem> ret = new ArrayList<>();
        if (ListUtils.isEmpty(mapList)){
            return ret;
        }
        GpuItemFieldEnum[] anEnum = GpuItemFieldEnum.getEnum(dims);
        // gpu 指标
        GpuItemFieldEnum gpuNumber = GpuItemFieldEnum.gpuNumber;
        String gpuColumn = ORMUtils.getColumnByFieldName(GpuItem.class, gpuNumber.name());
        // cpu 指标
        GpuItemFieldEnum cpuNumber = GpuItemFieldEnum.cpuNumber;
        String cpuColumn = ORMUtils.getColumnByFieldName(GpuItem.class, cpuNumber.name());
        for (Map<String,Object> map : mapList) {
            GpuItem item = new GpuItem();
            for (GpuItemFieldEnum fieldEnum : anEnum) {
                String name = fieldEnum.name();
                String column = ORMUtils.getColumnByFieldName(GpuItem.class, name);
                Object o = map.get(column);
                if (o != null){
                    fieldEnum.getFieldSetter().accept(item, o);
                }
            }
            if (needIndex){
                Object o = map.get(gpuColumn);
                if (o != null){
                    gpuNumber.getFieldSetter().accept(item, o);
                }
                o = map.get(cpuColumn);
                if (o != null){
                    cpuNumber.getFieldSetter().accept(item, o);
                }
            }
            ret.add(item);
        }
        return ret;
    }

    /**
     * 构建 sql
     *
     * @param req 请求
     * @param group 组：1 - 订单需求；2 - 未转单PPL；3 - 已购买；4 - 已预扣；5 - 在线库存；6 - 采购在途
     * @param dims 维度
     * @param sql 原始 sql
     * @param extWhereSql 扩展 where 条件
     * @param hasWhere 是否有 where 条件
     * @param needIndex 是否需要指标
     * @return
     */
    public Tuple2<String,WhereSQL> buildSql(GpuReportReq req, int group, List<String> dims, String sql, WhereSQL extWhereSql,
            boolean hasWhere, boolean needIndex) {
        if (needIndex) {
            dims = new ArrayList<>(ObjectUtils.defaultIfNull(dims,new ArrayList<>()));
            // 指标
            dims.add(GpuItemFieldEnum.gpuNumber.name());
            dims.add(GpuItemFieldEnum.cpuNumber.name());
        }
        return WhereUtils.buildSql(req, GpuItem.class, group, dims, sql, extWhereSql, hasWhere, needIndex, true, true);
    }
}
