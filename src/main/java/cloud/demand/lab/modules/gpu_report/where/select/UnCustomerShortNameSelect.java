package cloud.demand.lab.modules.gpu_report.where.select;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.service.DictService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import report.utils.model.where.DynamicSelect;
import report.utils.utils.SelectCaseBuilder;

/** 通用客户简称 */
public class UnCustomerShortNameSelect implements DynamicSelect {

    @Override
    public String getSelect(Object o, String[] strings) {
        String customerShortName = strings[0];
        DictService bean = SpringUtil.getBean(DictService.class);
        Map<String, List<String>> map = bean.getUnCustomerShortName2Name();
        Map<List<String>, String> resMap = new HashMap<>();
        map.forEach((k,v)-> resMap.put(v,k));
        return SelectCaseBuilder.build(customerShortName, customerShortName, resMap);
    }
}
