package cloud.demand.lab.modules.gpu_report.where.where_parse;

import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder.WhereInfo;

/** 客户范围 */
public class CustomerRangeParseWhere implements IWhereParser {

    @Override
    public void parseSQL(WhereSQL content, WhereInfo whereInfo, Object t) {
        Object v = whereInfo.getV();
        List<String> ls = (List<String>) v;
        if (ListUtils.isNotEmpty(ls)){
            Map<Integer, String> customerRangeMap = GpuUtils.getCustomerRangeMap();
            Map<String, Integer> resMap = new HashMap<>();
            customerRangeMap.forEach( (k, val) -> resMap.put(val,k) );
            List<Integer> innerList = new ArrayList<>();
            for (String l : ls) {
                Integer e = resMap.get(l);
                if (e != null){
                    innerList.add(e);
                }
            }
            String columnValue = whereInfo.getColumnValue();
            content.andIf(ListUtils.isNotEmpty(innerList), columnValue + " in (?)", innerList);
        }
    }
}
