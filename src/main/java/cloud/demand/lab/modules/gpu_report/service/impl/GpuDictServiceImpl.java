package cloud.demand.lab.modules.gpu_report.service.impl;

import cloud.demand.lab.common.entity.UserPermissionDto;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.gpu_report.dto.GpuReportReq;
import cloud.demand.lab.modules.gpu_report.service.GpuDictService;
import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import cloud.demand.lab.modules.operation_view.operation_view.model.ReturnT;
import cloud.demand.lab.modules.order.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import report.utils.model.excel.ErrorMessage;

/** gpu字典 */
@Service
public class GpuDictServiceImpl implements GpuDictService {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<String> getSourcePpl(String orderCategory) {
        String sql = ORMUtils.getSql("/sql/gpu/ppl2order.sql");
        // ppl 来源来源单号字段
        return demandDBHelper.getRaw(String.class, sql, orderCategory);
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600, keyScript = "args[0]")
    @Override
    public List<String> getPplYearMonthRange(String yearMonth) {
        String sql = "select \n"
                + "CONCAT(demand_begin_year, '-' , if(demand_begin_month < 10, concat('0',demand_begin_month),demand_begin_month)) as `start_year_month`,\n"
                + "CONCAT(demand_end_year, '-' , if(demand_end_month < 10, concat('0',demand_end_month),demand_end_month)) as `end_year_month`\n"
                + "from ppl_version\n"
                + "where deleted = 0\n"
                + "and CONCAT(demand_begin_year, '-' , if(demand_begin_month < 10, concat('0',demand_begin_month),demand_begin_month)) > ?\n"
                + "order by version_code desc";
        YearMonthRange rawOne = demandDBHelper.getRawOne(YearMonthRange.class, sql, yearMonth);
        if (rawOne == null){
            // 默认取次月往后推3 个月
            YearMonth start = YearMonth.parse(yearMonth).plusMonths(1);
            List<YearMonth> yearMonths = GpuUtils.rangeDate(start, 3);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            return yearMonths.stream().map(item-> item.format(formatter)).collect(Collectors.toList());
        }else {
            return GpuUtils.rangeDate(rawOne.getStartYearMonth(), rawOne.getEndYearMonth());
        }
    }

    @Override
    public boolean hasGpuPermission() {
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        DictService dict = SpringUtil.getBean(DictService.class);
        Boolean adminUserOrNot = dict.checkIsAdmin(userNameWithSystem);
        if (!adminUserOrNot) {
            // 如果不是管理员 需要判断是否有权限看数据
            UserPermissionDto permissionByUserAndRole = dict.getPermissionByUserAndRole(
                    IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(), userNameWithSystem);
            if (permissionByUserAndRole == null){
                return false;
            }
            // 是否有 GPU 的权限（全产品 or 包含 GPU(裸金属&CVM)）
            boolean hasGpuPermission = permissionByUserAndRole.getIsAllProduct() || permissionByUserAndRole.getProduct().contains(
                    Ppl13weekProductTypeEnum.GPU.getName());
            // 需要是 GPU 产品并且有全部行业权限才行
            return hasGpuPermission && permissionByUserAndRole.getIsAllIndustry();
        }
        return true;
    }
}
