package cloud.demand.lab.modules.gpu_report.where.where_parse;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.service.DictService;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder.WhereInfo;

/** 地域 --> 区域 */
public class AreaNameParseWhere implements IWhereParser {

    @Override
    public void parseSQL(WhereSQL content, WhereInfo whereInfo, Object t) {
        Object v = whereInfo.getV();
        List<String> ls = (List<String>) v;
        if (ListUtils.isNotEmpty(ls)){
            String regionNameColumn = whereInfo.getParseParams()[0]; // 地域字段
            List<String> regionNameList = new ArrayList<>();
            Map<String, List<String>> areaNameToRegionName = SpringUtil.getBean(DictService.class)
                    .getAreaNameToRegionName();
            ls.forEach(item -> {
                List<String> list = areaNameToRegionName.get(item);
                if (ListUtils.isNotEmpty(list)){
                    regionNameList.addAll(list);
                }
            });
            content.andIf(ListUtils.isNotEmpty(regionNameList),regionNameColumn + " in (?)", regionNameList);
        }
    }
}
