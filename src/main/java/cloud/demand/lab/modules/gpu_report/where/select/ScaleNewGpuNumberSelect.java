package cloud.demand.lab.modules.gpu_report.where.select;

import cloud.demand.lab.modules.gpu_report.dto.IScaleType;
import report.utils.model.where.DynamicSelect;

/** 规模新增 gpu 卡数 */
public class ScaleNewGpuNumberSelect implements DynamicSelect {

    @Override
    public String getSelect(Object o, String[] strings) {
        String billColumn = strings[0];
        String serverColumn = strings[1];
        String column = null;
        if (o instanceof IScaleType){
            IScaleType scaleType = (IScaleType) o;
            if (scaleType.isServer()){
                column = serverColumn;
            }
        }
        if (column == null){
            column = billColumn;
        }

        String select = "if(sum(toDecimal64(${column},6)) > 0, sum(toDecimal64(${column},6)), toDecimal64(0,6))";
        return select.replace("${column}", column);
    }
}
