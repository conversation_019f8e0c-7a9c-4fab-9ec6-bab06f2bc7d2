package cloud.demand.lab.modules.gpu_report.utils;

import cloud.demand.lab.modules.gpu_report.DynamicProperties;
import cloud.demand.lab.modules.gpu_report.enums.OrderSourceEnum;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.enums.OrderNodeCodeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import report.utils.model.item.BigDecimalItem;

public class GpuUtils {

    public static Map<String,String> orderNodeCodeMap = Arrays.stream(OrderNodeCodeEnum.values())
            .collect(Collectors.toMap(OrderNodeCodeEnum::getCode, OrderNodeCodeEnum::getName));

    public static Map<String,String> orderSourceMap = Arrays.stream(OrderSourceEnum.values())
            .collect(Collectors.toMap(OrderSourceEnum::getCode, OrderSourceEnum::getName));


    public static List<YearMonth> rangeDate(YearMonth yearMonth, int n) {
        if (yearMonth == null) {
            return null;
        }
        List<YearMonth> ret = new ArrayList<>();
        int step = n > 0 ? 1 : -1;
        while (n != 0) {
            ret.add(yearMonth);
            yearMonth = yearMonth.plusMonths(step);
            n -= step;
        }
        return ret;
    }

    public static <T> BigDecimal getBigDecimal(T val, Function<T, BigDecimal> func, BigDecimal defaultValue)  {
        if (val == null){
            return defaultValue;
        }
        BigDecimal ret = func.apply(val);
        if (ret == null){
            return defaultValue;
        }
        return defaultValue.add(ret);
    }

    public static <T> BigDecimal getBigDecimal(T val, Function<T, BigDecimal> func)  {
        return getBigDecimal(val,func,getDefV());
    }

    public static BigDecimal getDefV(){
        return new BigDecimalItem(BigDecimal.ZERO, new BigDecimal[]{BigDecimal.ZERO});
    }

    public static BigDecimal addAll(BigDecimal... num){
        return addAllWithInit(getDefV(),num);
    }

    public static BigDecimal addAllWithInit(BigDecimal initVal,BigDecimal... num) {
        BigDecimal ret = initVal;
        BigDecimal[] var2 = num;
        int var3 = num.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            BigDecimal bigDecimal = var2[var4];
            if (bigDecimal != null) {
                ret = ret.add(bigDecimal);
            }
        }

        return ret;
    }

    public static String getYearMonth(YearMonth ym){
        return ym.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 年月范围
     * @param startYearMonth 起始年月
     * @param endYearMonth 结束年月
     * @return
     */
    public static List<String> rangeDate(String startYearMonth,String endYearMonth){
        String[] start_ = startYearMonth.split("-");
        String[] end_ = endYearMonth.split("-");
        int startHigh = Integer.parseInt(start_[0]);
        int startLow = Integer.parseInt(start_[1]);
        int endHigh = Integer.parseInt(end_[0]);
        int endLow = Integer.parseInt(end_[1]);
        List<String> ret = new ArrayList<>();
        IncrIterator incr = new IncrIterator(12, startHigh, startLow, 1);
        while(incr.hasNext(endHigh,endLow)){
            ret.add(incr.getHigh()+"-"+ fixNumber(incr.getLow()));
            incr.next();
        }
        return ret;
    }

    /** 递增迭代器 */
    @Getter
    @AllArgsConstructor
    public static class IncrIterator {
        /** 进高位需要的增量 */
        private int size;
        /** 高位，不受限制 */
        private int high;
        /** 低位，每增一个size，进一个高位 */
        private int low;

        /** 重置的lowNum */
        private int base;

        public IncrIterator(int size) {
            this.size = size;
            this.high = 0;
            this.low = 0;
            this.base = 0;
        }

        public void next(){
            low++;
            if (low >= size + base){
                low = base;
                high++;
            }
        }

        public boolean hasNext(int endHigh,int endLow){
            return high < endHigh || (high == endHigh && low <= endLow);
        }

    }

    public static String fixNumber(int num){
        if (num < 10 && num >= 0){
            return "0" + num;
        }
        return "" + num;
    }

    /** gpu 卡型处理 */
    public static String transformGpuCardType(String gpuCardType){
        if (StringUtils.isBlank(gpuCardType)){
            return gpuCardType;
        }
        Map<String, String> gpuInventoryGpuCardTypeMap = DynamicProperties.getGpuInventoryGpuCardTypeMap();
        String ret = gpuInventoryGpuCardTypeMap.get(gpuCardType);
        if (ret != null){
            return ret;
        }
        // H20-96G 这种格式，去掉-96G
        if (gpuCardType.endsWith("G")){
             ret = gpuCardType.substring(0, gpuCardType.lastIndexOf("-"));
        }else {
            ret =  gpuCardType;
        }
        return ret;
    }

    public static String orderNodeCode2Name(String code){
        if (code == null){
            return null;
        }
        return orderNodeCodeMap.getOrDefault(code,code);
    }

    public static String orderSource2Name(String code){
        if (code == null){
            return null;
        }
        return orderSourceMap.getOrDefault(code,code);
    }

    /**
     * 添加过滤条件，如果参数不为空，则添加过滤条件，否则不添加
     * @param filter 过滤集合
     * @param params 参数集合
     * @param getter 获取值的函数
     * @param <T>
     */
    public static <T> void addFilter(List<Predicate<T>> filter,List<String> params, Function<T,String> getter){
        if (ListUtils.isNotEmpty(params)){
            Set<String> set = new HashSet<>(params);
            filter.add(t -> set.contains(getter.apply(t)));
        }
    }

    /**
     * 客户范围，1：内部客户，2：外部客户
     * @return
     */
    public static Map<Integer,String> getCustomerRangeMap(){
        Map<Integer, String> map = new HashMap<>();
        map.put(1,"内部客户");
        map.put(0,"外部客户");
        return map;
    }

    /**
     * 客户类型，枚举：0-个人 1-企业  2-政府 3-组织 99 未知
     * @return
     */
    public static Map<Integer,String> getCustomerTypeMap(){
        Map<Integer, String> map = new HashMap<>();
        map.put(0,"个人");
        map.put(1,"企业");
        map.put(2,"政府");
        map.put(3,"组织");
        map.put(99,"未知");
        return map;
    }
}
