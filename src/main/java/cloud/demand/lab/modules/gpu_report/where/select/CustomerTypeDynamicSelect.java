package cloud.demand.lab.modules.gpu_report.where.select;

import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import java.util.HashMap;
import java.util.Map;
import report.utils.model.where.DynamicSelect;
import report.utils.utils.SelectCaseBuilder;

/** 客户类型 */
public class CustomerTypeDynamicSelect implements DynamicSelect {

    @Override
    public String getSelect(Object o, String[] strings) {
        String customerTypeColumn = strings[0];
        return SelectCaseBuilder.build(customerTypeColumn, "'(空值)'", GpuUtils.getCustomerTypeMap());
    }
}
