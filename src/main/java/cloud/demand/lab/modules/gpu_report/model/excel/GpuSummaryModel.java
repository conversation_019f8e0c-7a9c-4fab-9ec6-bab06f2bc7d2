package cloud.demand.lab.modules.gpu_report.model.excel;

import cloud.demand.lab.modules.gpu_report.entity.GpuPurchaseDataDO;
import cloud.demand.lab.modules.gpu_report.enums.GpuItemFieldEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;
import report.utils.anno.ExcelSheet;

/** gpu 导出总览 */
@Data
public class GpuSummaryModel extends GpuCommonModel{

    @ExcelSheet(value = "总需求",modelClass = GpuOrderDemandModel.class)
    private List<GpuOrderDemandModel> totalDemand;

    @ExcelSheet(value = "订单需求",modelClass = GpuOrderDemandModel.class)
    private List<GpuOrderDemandModel> orderDemand;

    @ExcelSheet(value = "未转单PPL",modelClass = GpuPplDemandModel.class)
    private List<GpuPplDemandModel> pplDemand;

    @ExcelSheet(value = "新增规模",modelClass = GpuScaleModel.class)
    private List<GpuScaleModel> scale;

    @ExcelSheet(value = "已预扣未购买",modelClass = GpuGridModel.class)
    private List<GpuGridModel> gridNotScale;

    @ExcelSheet(value = "已预扣已购买",modelClass = GpuGridModel.class)
    private List<GpuGridModel> gridScale;

    @ExcelSheet(value = "在线库存",modelClass = GpuInventoryModel.class)
    private List<GpuInventoryModel> inventory;

    @ExcelSheet(value = "采购在途",modelClass = GpuPurchaseModel.class)
    private List<GpuPurchaseModel> purchase;


    /** 清洗 */
    public void clean(){
        if (ListUtils.isNotEmpty(totalDemand)){
            totalDemand.forEach((item)->{
                GpuItemFieldEnum.orderSource.getFieldSetter().accept(item, GpuItemFieldEnum.orderSource.getFieldGetter().apply(item));
                GpuItemFieldEnum.orderNodeCode.getFieldSetter().accept(item, GpuItemFieldEnum.orderNodeCode.getFieldGetter().apply(item));
            });
        }
        if (ListUtils.isNotEmpty(orderDemand)){
            orderDemand.forEach((item)->{
                GpuItemFieldEnum.orderSource.getFieldSetter().accept(item, GpuItemFieldEnum.orderSource.getFieldGetter().apply(item));
                GpuItemFieldEnum.orderNodeCode.getFieldSetter().accept(item, GpuItemFieldEnum.orderNodeCode.getFieldGetter().apply(item));
            });
        }
    }
}
