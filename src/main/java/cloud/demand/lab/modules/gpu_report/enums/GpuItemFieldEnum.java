package cloud.demand.lab.modules.gpu_report.enums;

import cloud.demand.lab.modules.gpu_report.model.field.ICpuNumber;
import cloud.demand.lab.modules.gpu_report.model.field.IGpuNumber;
import cloud.demand.lab.modules.gpu_report.model.field.IGridStatus;
import cloud.demand.lab.modules.gpu_report.model.field.ISourcePpl;
import cloud.demand.lab.modules.gpu_report.model.field.ISubmitTime;
import cloud.demand.lab.modules.gpu_report.model.field.IUnCustomerShortName;
import cloud.demand.lab.modules.gpu_report.utils.GpuUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import report.utils.model.field.IDynamicFieldEnum;
import report.utils.model.field.IReportTecDynamicField;
import report.utils.model.field.customer.ICustomerShortName;
import report.utils.model.field.customer.ICustomerUin;
import report.utils.model.field.customer.IIndustryDept;
import report.utils.model.field.customer.IWarZone;
import report.utils.model.field.date.IYearMonth;
import report.utils.model.field.instance.IGpuCardType;
import report.utils.model.field.instance.IInstanceType;
import report.utils.model.field.order.IBeginBuyDate;
import report.utils.model.field.order.IEndBuyDate;
import report.utils.model.field.order.IOrderNodeCode;
import report.utils.model.field.order.IOrderNumber;
import report.utils.model.field.order.IOrderSource;
import report.utils.model.field.order.IOrderStatus;
import report.utils.model.field.order.IProjectName;
import report.utils.model.field.scale.IAppRole;
import report.utils.model.field.scale.ICustomerRange;
import report.utils.model.field.scale.ICustomerType;
import report.utils.model.field.zone.IAreaName;
import report.utils.model.field.zone.ICountryName;
import report.utils.model.field.zone.ICustomhouseTitle;
import report.utils.model.field.zone.IRegionName;
import report.utils.model.field.zone.IZoneName;
import report.utils.utils.ReportCommonUtils;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

/** gpu 信息 */
@Getter
@AllArgsConstructor
public enum GpuItemFieldEnum implements IDynamicFieldEnum {


    /** 客户信息 */
    industryDept("行业部门",obj -> ((IIndustryDept) obj).getIndustryDept(), (obj, value) -> ((IIndustryDept) obj).setIndustryDept(ReportCommonUtils.toString(value))),
    warZone("战区", (obj) -> ((IWarZone) obj).getWarZone(), (obj, value) -> ((IWarZone) obj).setWarZone(ReportCommonUtils.toString(value))),
    customerUin("客户uin", (obj) -> ((ICustomerUin) obj).getCustomerUin(), (obj, value) -> ((ICustomerUin) obj).setCustomerUin(ReportCommonUtils.toString(value))),
    customerShortName("客户简称", (obj) -> ((ICustomerShortName) obj).getCustomerShortName(), (obj, value) -> ((ICustomerShortName) obj).setCustomerShortName(ReportCommonUtils.toString(value))),
    unCustomerShortName("通用客户简称", (obj) -> ((IUnCustomerShortName) obj).getUnCustomerShortName(), (obj, value) -> ((IUnCustomerShortName) obj).setUnCustomerShortName(ReportCommonUtils.toString(value))),

    /** 地域信息 */
    zoneName("可用区", (obj) -> ((IZoneName) obj).getZoneName(), (obj,value) -> ((IZoneName) obj).setZoneName(ReportCommonUtils.toString(value))),
    regionName("地域", (obj) -> ((IRegionName) obj).getRegionName(), (obj,value) -> ((IRegionName) obj).setRegionName(ReportCommonUtils.toString(value))),
    areaName("区域", (obj) -> ((IAreaName) obj).getAreaName(), (obj,value) -> ((IAreaName) obj).setAreaName(ReportCommonUtils.toString(value))),
    countryName("国家", (obj) -> ((ICountryName) obj).getCountryName(), (obj,value) -> ((ICountryName) obj).setCountryName(ReportCommonUtils.toString(value))),
    customhouseTitle("境内外", (obj) -> ((ICustomhouseTitle) obj).getCustomhouseTitle(), (obj,value) -> ((ICustomhouseTitle) obj).setCustomhouseTitle(ReportCommonUtils.toString(value))),

    /** 实例信息 */
    instanceType("实例类型", (obj) -> ((IInstanceType) obj).getInstanceType(), (obj,value) -> ((IInstanceType) obj).setInstanceType(ReportCommonUtils.toString(value))),
    gpuCardType("GPU卡型", (obj) -> ((IGpuCardType) obj).getGpuCardType(), (obj,value) -> ((IGpuCardType) obj).setGpuCardType(ReportCommonUtils.toString(value))),

    /** 订单信息 */
    orderNumber("业务订单号", (obj) -> ((IOrderNumber) obj).getOrderNumber(),(obj, value) -> ((IOrderNumber) obj).setOrderNumber(ReportCommonUtils.toString(value))),
    appRole("应用角色", (obj) -> ((IAppRole) obj).getAppRole(), (obj, value) -> ((IAppRole) obj).setAppRole(ReportCommonUtils.toString(value))),
    projectName("项目名称", (obj) -> ((IProjectName) obj).getProjectName(), (obj, value) -> ((IProjectName) obj).setProjectName(ReportCommonUtils.toString(value))),
    orderSource("单据来源", (obj) -> GpuUtils.orderSource2Name((((IOrderSource) obj).getOrderSource())), (obj, value) -> ((IOrderSource) obj).setOrderSource(ReportCommonUtils.toString(value))),
    orderStatus("订单状态", (obj) -> ((IOrderStatus) obj).getOrderStatus(), (obj, value) -> ((IOrderStatus) obj).setOrderStatus(ReportCommonUtils.toString(value))),
    orderNodeCode("订单节点", (obj) -> GpuUtils.orderNodeCode2Name(((IOrderNodeCode) obj).getOrderNodeCode()), (obj, value) -> ((IOrderNodeCode) obj).setOrderNodeCode(ReportCommonUtils.toString(value))),
    sourcePpl("来源PPL单号", (obj) -> ((ISourcePpl) obj).getSourcePpl(), (obj, value) -> ((ISourcePpl) obj).setSourcePpl(ReportCommonUtils.toString(value))),
    beginBuyDate("开始购买日期", (obj) -> ((IBeginBuyDate) obj).getBeginBuyDate(), (obj, value) -> ((IBeginBuyDate) obj).setBeginBuyDate(ReportCommonUtils.toString(value))),
    endBuyDate("结束购买日期", (obj) -> ((IEndBuyDate) obj).getEndBuyDate(), (obj, value) -> ((IEndBuyDate) obj).setEndBuyDate(ReportCommonUtils.toString(value))),
    submitTime("提单时间", (obj) -> ((ISubmitTime) obj).getSubmitTime(), (obj, value) -> ((ISubmitTime) obj).setSubmitTime(ReportCommonUtils.toString(value))),

    /** 预扣 */
    gridStatus("预扣状态", (obj) -> ((IGridStatus) obj).getGridStatus(), (obj, value) -> ((IGridStatus) obj).setGridStatus(ReportCommonUtils.toString(value))),

    /** 规模信息 */
    customerRange("客户范围", (obj) -> ((ICustomerRange) obj).getCustomerRange(), (obj, value) -> ((ICustomerRange) obj).setCustomerRange(ReportCommonUtils.toString(value))),
    customerType("客户类型", (obj) -> ((ICustomerType) obj).getCustomerType(), (obj, value) -> ((ICustomerType) obj).setCustomerType(ReportCommonUtils.toString(value))),

    /** 年月 */
    yearMonth("年月", (obj) -> ((IYearMonth) obj).getYearMonth(), (obj, value) -> ((IYearMonth) obj).setYearMonth(ReportCommonUtils.toString(value))),
    /** 卡数 */

    cpuNumber("核数", (obj) -> ((ICpuNumber) obj).getCpuNumber(), (obj, value) -> ((ICpuNumber) obj).setCpuNumber(
            ReportCommonUtils.toBigDecimal(value))),
    /** 核数 */
    gpuNumber("卡数", (obj) -> ((IGpuNumber) obj).getGpuNumber(), (obj, value) -> ((IGpuNumber) obj).setGpuNumber(
            ReportCommonUtils.toBigDecimal(value)))
    ;

    private final String name; // 名称

    private final boolean isIndex; // 是否为指标

    private final IReportTecDynamicField dynamicField; // 动态字段

    private final Function<Object, Object> fieldGetter; // getter

    private final BiConsumer<Object, Object> fieldSetter; // setter

    GpuItemFieldEnum(String name, Function<Object, Object> fieldGetter, BiConsumer<Object, Object> fieldSetter) {
        this.name = name;
        this.dynamicField = null;
        this.isIndex = false;
        this.fieldGetter = fieldGetter;
        this.fieldSetter = fieldSetter;
    }

    /** 获取非指标字段 */
    public static List<String> getFields(){
        return Arrays.stream(values()).filter(item->!item.isIndex()).map(Enum::name).collect(Collectors.toList());
    }

    /** 获取订单需求明细字段 */
    public static List<String> getOrderDemandDetailFields(){
        return ListUtils.newList(
                GpuItemFieldEnum.orderNumber.name(),
                GpuItemFieldEnum.sourcePpl.name(),
                GpuItemFieldEnum.industryDept.name(),
                GpuItemFieldEnum.customerShortName.name(),
                GpuItemFieldEnum.unCustomerShortName.name(),
                GpuItemFieldEnum.orderNodeCode.name(),
                GpuItemFieldEnum.yearMonth.name(),
                GpuItemFieldEnum.beginBuyDate.name(),
                GpuItemFieldEnum.endBuyDate.name(),
                GpuItemFieldEnum.submitTime.name(),
                GpuItemFieldEnum.gpuCardType.name(),
                GpuItemFieldEnum.instanceType.name(),
                GpuItemFieldEnum.regionName.name(),
                GpuItemFieldEnum.zoneName.name()
        );
    }

    /**
     * 拼接需要匹配的字段，强制匹配：客户通用简称、GPU卡型、战区、年月
     * */
    public static List<String> appendMatchKey(List<String> dims){
        Set<String> set = new LinkedHashSet<>();
        if (ListUtils.isNotEmpty(dims)){
            set.addAll(dims);
        }
        set.add(GpuItemFieldEnum.customerShortName.name());
        set.add(GpuItemFieldEnum.gpuCardType.name());
        set.add(GpuItemFieldEnum.instanceType.name());
        set.add(GpuItemFieldEnum.countryName.name());
        set.add(GpuItemFieldEnum.regionName.name());
        set.add(GpuItemFieldEnum.yearMonth.name());
        return new ArrayList<>(set);
    }

    /**
     * 缺口对冲：国家+GPU卡型+年月
     * @param dims 维度
     * @return
     */
    public static List<String> appendGapMatchKey(List<String> dims){
        Set<String> set = new LinkedHashSet<>();
        if (ListUtils.isNotEmpty(dims)){
            set.addAll(dims);
        }
        set.add(GpuItemFieldEnum.gpuCardType.name());
        set.add(GpuItemFieldEnum.countryName.name());
        set.add(GpuItemFieldEnum.yearMonth.name());
        return new ArrayList<>(set);
    }

    /**
     * 规模去重：行业部门，实例类型，可用区，年月
     * */
    public static List<String> appendScaleDistinctKey(List<String> dims){
        Set<String> set = new LinkedHashSet<>();
        if (ListUtils.isNotEmpty(dims)){
            set.addAll(dims);
        }
        set.add(GpuItemFieldEnum.industryDept.name());
        set.add(GpuItemFieldEnum.instanceType.name());
        set.add(GpuItemFieldEnum.zoneName.name());
        set.add(GpuItemFieldEnum.yearMonth.name());
        return new ArrayList<>(set);
    }

    public static GpuItemFieldEnum[] getEnum(List<String> fields) {
        if (ListUtils.isNotEmpty(fields)) {
            Set<String> temp = new HashSet<>(fields);
            Map<String, GpuItemFieldEnum> collect = Arrays.stream(values())
                    .collect(Collectors.toMap(Enum::name, item->item));
            GpuItemFieldEnum[] ret = new GpuItemFieldEnum[temp.size()];
            int index = 0;
            for (String name : temp) {
                GpuItemFieldEnum value = collect.get(name);
                if (value == null){
                    throw new BizException(String.format("未知维度：【%s】", name));
                }
                ret[index++] = value;
            }
            return ret;
        }
        return new GpuItemFieldEnum[0];
    }

    private final static Map<String,Function<Object,Object>> fieldMap = Arrays.stream(values())
            .collect(Collectors.toMap(Enum::name, GpuItemFieldEnum::getFieldGetter));

    /** 根据字段名称获取字段值 */
    public static <T> Object getValue(String fieldName,T t){
        Function<Object, Object> fieldGetter = fieldMap.get(fieldName);
        if (fieldGetter == null){
            throw new ITException(String.format("字段找不到，字段名称：【%s】", ObjectUtils.defaultIfNull(fieldName,"null")));
        }
        return fieldGetter.apply(t);
    }
}
