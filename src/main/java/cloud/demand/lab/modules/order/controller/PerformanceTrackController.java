package cloud.demand.lab.modules.order.controller;

import cloud.demand.lab.modules.order.auth.PerformanceTrackAuthCheck;
import cloud.demand.lab.modules.order.dto.GroupOrderReq;
import cloud.demand.lab.modules.order.dto.OrderCategoryProductVO;
import cloud.demand.lab.modules.order.dto.PerformanceParamTypeReq;
import cloud.demand.lab.modules.order.dto.PerformanceTaskReq;
import cloud.demand.lab.modules.order.dto.PerformanceTrackReq;
import cloud.demand.lab.modules.order.dto.PerformanceTrackResp;
import cloud.demand.lab.modules.order.dto.PerformanceTrackSummaryResp;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import cloud.demand.lab.modules.order.service.PerformanceTrackService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@JsonrpcController("/performance-track")
@Slf4j
public class PerformanceTrackController {

    @Resource
    private PerformanceTrackService performanceTrackService;

    @RequestMapping
    @PerformanceTrackAuthCheck
    public PerformanceTrackSummaryResp queryMonthRateSummary(@JsonrpcParam PerformanceTrackReq req) {
        return performanceTrackService.queryMonthPerformanceTrackSummary(req);
    }

    @RequestMapping
    @PerformanceTrackAuthCheck
    public PerformanceTrackResp queryMonthPerformanceTrackItemList(@JsonrpcParam PerformanceTrackReq req) {
        return performanceTrackService.queryMonthPerformanceTrackItemList(req);
    }

    @RequestMapping
    @PerformanceTrackAuthCheck
    public Object queryAllOrderMonthTrack(@JsonrpcParam PerformanceTrackReq req) {
        req.setIsAllOrder(Boolean.TRUE);
        return performanceTrackService.queryAllOrderMonthTrack(req);
    }

    @RequestMapping
    @PerformanceTrackAuthCheck
    public Object queryGroupOrder(@JsonrpcParam GroupOrderReq req) {
        return performanceTrackService.queryGroupOrder(req);
    }


    @RequestMapping
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void initMonthPerformanceTrackDf(@JsonrpcParam PerformanceTaskReq req) {
        if(StringUtils.equals(req.getOderType(), OrderTypeEnum.ELASTIC.getName())){
            req.setFuzzyArea("zoneName");
            req.setFuzzyInstance("instanceType");
        }
        performanceTrackService.initMonthPerformanceTrackDf(req.getOderType(), req.getFuzzyInstance(), req.getFuzzyArea());
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam @Valid PerformanceParamTypeReq req) {
        return performanceTrackService.queryParams(req);
    }

    @RequestMapping
    public Map<String,List<String>> queryProduct() {
        List<OrderCategoryProductVO> list = performanceTrackService.queryProduct();
        return ListUtils.toMapList( list, OrderCategoryProductVO::getOrderCategory , OrderCategoryProductVO::getProduct);
    }

}
