package cloud.demand.lab.modules.order.service;

import cloud.demand.lab.modules.order.dto.MonthScaleDTO;
import cloud.demand.lab.modules.order.dto.PerformanceTrackDayScaleDTO;
import cloud.demand.lab.modules.order.entity.std_table.AwsMonthPerformanceTrackDfDO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/24 17:22
 */
public interface DayScaleService {
    Map<String,MonthScaleDTO> queryMonthScale(List<String> yearMonth, AwsMonthPerformanceTrackDfDO trackDO);

    List<PerformanceTrackDayScaleDTO> queryDayScale(List<String> statTimeList, AwsMonthPerformanceTrackDfDO trackDO);
}
