package cloud.demand.lab.modules.order.enums;

import com.pugwoo.wooutils.collect.ListUtils;

import java.util.List;

/**
 * 订单弹性类型
 */
public enum OrderElasticType {

    ONCE_TIME("一次性"),

    BY_MONTH("月弹性"),

    BY_WEEK("周弹性"),

    BY_DAY("日弹性");

    private final String typeName;

    OrderElasticType(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public static OrderElasticType getByTypeName(String typeName) {
        for (OrderElasticType type : OrderElasticType.values()) {
            if (type.getTypeName().equals(typeName)) {
                return type;
            }
        }
        return null;
    }

    public static List<String> allTypeNames() {
        return ListUtils.newArrayList(BY_MONTH.getTypeName(), BY_WEEK.getTypeName(), BY_DAY.getTypeName());
    }
}
