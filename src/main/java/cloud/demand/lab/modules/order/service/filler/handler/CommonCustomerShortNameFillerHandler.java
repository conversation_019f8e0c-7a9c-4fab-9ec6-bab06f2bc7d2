package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.lab.modules.order.service.filler.CommonCustomerShortNameFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class CommonCustomerShortNameFillerHandler implements FillerHandler<CommonCustomerShortNameFiller> {

    @Resource
    private DictService dictService;

    @Override
    public int getExecOrder() {
        return FillerHandler.commonCustomerShortNameFillerHandlerOrder;
    }

    @Override
    public void fill(List<CommonCustomerShortNameFiller> obj) {
        List<IndustryDemandIndustryWarZoneDictDO> dicts = dictService.queryEnableIndustryWarZoneCustomerConfig();
        Map<String, List<IndustryDemandIndustryWarZoneDictDO>> map = ListUtils.toMapList(dicts,
                IndustryDemandIndustryWarZoneDictDO::getCustomerName, item -> item);
        for (CommonCustomerShortNameFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            List<IndustryDemandIndustryWarZoneDictDO> list = map.get(filler.provideCustomerShortName());
            if (ListUtils.isEmpty(list)) {
                filler.fillCommonCustomerShortName(filler.provideCustomerShortName());
                continue;
            }
            if (list.size() > 1) {
                String industryDept = filler.provideIndustryDept();
                for (IndustryDemandIndustryWarZoneDictDO dictDO : list) {
                    if (Objects.equals(industryDept, dictDO.getIndustry())) {
                        filler.fillCommonCustomerShortName(dictDO.getCommonCustomerName());
                    }
                }
            } else {
                filler.fillCommonCustomerShortName(list.get(0).getCommonCustomerName());
            }
        }
    }

}
