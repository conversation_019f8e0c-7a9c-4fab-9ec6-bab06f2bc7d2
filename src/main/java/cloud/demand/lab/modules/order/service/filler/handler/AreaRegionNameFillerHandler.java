package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.order.service.filler.AreaRegionNameFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AreaRegionNameFillerHandler implements FillerHandler<AreaRegionNameFiller> {

    @Resource
    private DictService dictService;

    @Override
    public int getExecOrder() {
        return FillerHandler.areaRegionNameFillerHandlerOrder;
    }

    @Override
    public void fill(List<AreaRegionNameFiller> obj) {
        Map<String, YuntiStategyZoneDO> zoneInfoMap =
                dictService.getYunTiStategyZoneDOList()
                        .stream().collect(Collectors.toMap(YuntiStategyZoneDO::getZoneName, Function.identity(), (k1, k2) -> k1));
        for (AreaRegionNameFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            YuntiStategyZoneDO zoneInfo = zoneInfoMap.get(filler.provideZoneName());
            if (Objects.nonNull(zoneInfo)) {
                filler.fillRegionName(zoneInfo.getRegionName());
            } else {
                filler.fillRegionName(Constant.EMPTY_VALUE_STR);
            }
        }

    }
}
