package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class ZoneInfoFillerHandler implements FillerHandler<ZoneInfoFiller> {

    @Resource
    private DictService dictService;

    @Override
    public int getExecOrder() {
        return FillerHandler.zoneInfoFillerHandlerOrder;
    }

    @Override
    public void fill(List<ZoneInfoFiller> list) {
        Map<String, StaticZoneDO> zoneMap = dictService.getAllPlanZoneInfosGroupByName();
        list.forEach(item -> {
            if (item == null) {
                return;
            }
            if (StringUtils.isEmpty(item.provideZoneName())) {
                return;
            }
            StaticZoneDO zoneDO = MapUtils.getObject(zoneMap, item.provideZoneName());
            if(Objects.isNull(zoneDO)){
                return;
            }
            item.fillZone(zoneDO.getZone());
            item.fillRegionName(zoneDO.getRegionName());
            item.fillAreaName(zoneDO.getAreaName());
            item.fillCustomhouseTitle(zoneDO.getCustomhouseTitle());
        });
    }
}
