package cloud.demand.lab.modules.order.dto;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class PerformanceTrackDayScaleDTO {

    @Column(value = "stat_time")
    private String statTime;

    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 实例类型
     */
    @Column(value = "instance_type")
    private String instanceType;


    /**
     * 可用区
     */
    @Column(value = "fuzzy_area")
    private String fuzzyArea;

    /**
     * 当天服务规模量
     */
    @Column(value = "cur_service_total")
    private BigDecimal curServiceTotal;

    @Column(value = "change_service_total")
    private BigDecimal changeServiceTotal;
}
