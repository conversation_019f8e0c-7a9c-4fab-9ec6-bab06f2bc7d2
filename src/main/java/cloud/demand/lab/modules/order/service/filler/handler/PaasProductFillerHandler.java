package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.DO.PaasProductUinWhiteListDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.PplProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.YunxiaoAppRoleEnum;
import cloud.demand.lab.modules.order.constant.OrderConstant;
import cloud.demand.lab.modules.order.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import cloud.demand.lab.modules.order.service.PerformanceTrackService;
import cloud.demand.lab.modules.order.service.filler.BizTypeFiller;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.PaasProductFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class PaasProductFillerHandler implements FillerHandler<PaasProductFiller> {

    @Resource
    private DictService dictService;

    @Override
    public int getExecOrder() {
        return FillerHandler.paasProductFillerHandlerOrder;
    }

    @Override
    public void fill(List<PaasProductFiller> obj) {
        Map<String, List<String>> paasProductUinMap = dictService.getPaasProductUinMapping()
                .stream().collect(Collectors.groupingBy(PaasProductUinWhiteListDO::getProduct,Collectors.mapping(PaasProductUinWhiteListDO::getUin,Collectors.toList())));
        for(PaasProductFiller filler:obj){
            if(!StringUtils.equals(filler.provideOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM)){
                //暂只支持CVM
                continue;
            }
            if(StringUtils.equals(filler.provideAppRole(), YunxiaoAppRoleEnum.EMR.getCode())){
                filler.fillPaasProduct(PplProductEnum.EMR.getName());
            }else if(StringUtils.equals(filler.provideAppRole(), YunxiaoAppRoleEnum.EKS.getCode())){
                filler.fillPaasProduct(PplProductEnum.EKS.getName());
            }else if(paasProductUinMap.getOrDefault(PplProductEnum.ES.getName(), ListUtils.newArrayList()).contains(filler.provideUin())){
                filler.fillPaasProduct(PplProductEnum.ES.getName());
            }else if(paasProductUinMap.getOrDefault(PplProductEnum.TCHouse.getName(), ListUtils.newArrayList()).contains(filler.provideUin())){
                filler.fillPaasProduct(PplProductEnum.TCHouse.getName());
            }else if(paasProductUinMap.getOrDefault(PplProductEnum.DLC.getName(), ListUtils.newArrayList()).contains(filler.provideUin())){
                filler.fillPaasProduct(PplProductEnum.DLC.getName());
            }else if(paasProductUinMap.getOrDefault(PplProductEnum.CSIG.getName(), ListUtils.newArrayList()).contains(filler.provideUin())){
                filler.fillPaasProduct(PplProductEnum.CSIG.getName());
            }else if(StringUtils.equals(filler.provideAppRole(), YunxiaoAppRoleEnum.CVM.getCode()) &&
                    StringUtils.equals(filler.provideIndustryDept(), "港澳台及国际业务部") && filler.provideUinType() == 0){
                filler.fillPaasProduct(PplProductEnum.IEGG.getName());
            }else {
                filler.fillPaasProduct(PplProductEnum.CVM_CBS.getName());
            }
        }
    }
}
