package cloud.demand.lab.modules.order.service.filler.handler;


import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.order.service.filler.WarZoneByCustomerShortNameFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class WarZoneByCustomerShortNameFillerHandler implements FillerHandler<WarZoneByCustomerShortNameFiller> {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public void fill(List<WarZoneByCustomerShortNameFiller> obj) {
        if (ListUtils.isEmpty(obj)) {
            return;
        }
        Set<String> shortNameSet = ListUtils.toSet(obj, WarZoneByCustomerShortNameFiller::provideCustomerShortName);
        String sql = "select customer_name, war_zone_name  "
                + " from cloud_demand.industry_demand_industry_war_zone_dict "
                + " where deleted = 0 and is_enable = 1 ";
        List<Item> items = demandDBHelper.getRaw(Item.class, sql, shortNameSet);
        if (ListUtils.isEmpty(items)) {
            return;
        }
        Map<String, String> shortName2WarZoneName = ListUtils.toMap(items,
                Item::getCustomerShortName, Item::getWarZoneName);
        obj.forEach(x -> x.fillWarZone(shortName2WarZoneName.getOrDefault(x.provideCustomerShortName(), Constant.EMPTY_VALUE_STR)));
    }

    /**
     *  需要保证在{@link CommonCustomerShortNameFillerHandler} 后面执行
     */
    @Override
    public int getExecOrder() {
        return FillerHandler.warZoneByCustomerShortNameFillerHandlerOrder;
    }

    @Data
    public static class Item {

        @Column("customer_name")
        private String customerShortName;

        @Column("war_zone_name")
        private String warZoneName;

    }
}
