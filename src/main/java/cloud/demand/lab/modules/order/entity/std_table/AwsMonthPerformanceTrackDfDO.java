package cloud.demand.lab.modules.order.entity.std_table;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.order.dto.CrpPplOrderItemAndInfoVO;
import cloud.demand.lab.modules.order.entity.IFuzzyKey;
import cloud.demand.lab.modules.order.enums.OrderElasticType;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

@Data
@ToString
@Table("aws_month_performance_track_df")
public class AwsMonthPerformanceTrackDfDO {

    /**
     * 统计日期<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")

    private LocalDate statTime;

    /**
     * 区域范围模糊:countryName、regionName、zoneName
     */
    @Column(value = "fuzzy_area")
    private String fuzzyArea;

    /**
     * 机型模糊:instanceType、instanceGroup
     */
    @Column(value = "fuzzy_instance")
    private String fuzzyInstance;

    /**
     * 插入时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime = new Date();

    /**
     * 业务类型：内部或外部，默认外部
     */
    @Column(value = "biz_type")
    private String bizType;

    /**
     * 订单类别：CVM<br/>Column: [order_category]
     */
    @Column(value = "order_category")
    private String orderCategory;

    /**
     * 订单类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    /**
     * 弹性类型 {@link OrderElasticType#getTypeName()}
     */
    @Column(value = "elastic_type")
    private String elasticType = Constant.EMPTY_VALUE_STR;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 通用客户简称<br/>Column: [common_customer_name]
     */
    @Column(value = "common_customer_name")
    private String commonCustomerName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "uin_type")
    private Integer uinType;

    private List<String> uin;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * paas产品
     */
    @Column(value = "paas_product")
    private String paasProduct;

    @Column(value = "statistical_caliber")
    private String statisticalCaliber = "核心";

    /**
     * 应用角色<br/>Column: [app_role]
     */
    @Column(value = "app_role")
    private String appRole;

    /**
     * 机型族<br/>Column: [instance_type]
     */
    @Column(value = "instance_type", insertValueScript = "'(空值)'")
    private String instanceType;

    /**
     * 机型族<br/>Column: [instance_group]
     */
    @Column(value = "instance_group", insertValueScript = "'(空值)'")
    private String instanceGroup;

    /**
     * 是否新机型
     */
    @Column(value = "is_new_instance_type")
    private Boolean isNewInstanceType;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 国家
     */
    @Column(value = "country_name")
    private String countryName;

    /**
     * 地域<br/>Column: [region]
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone]
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 需求年月<br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 所有订单（包含满足方式评估、交付供应）-需求总核心数<br/>Column: [all_order_demand_total_Core]
     */
    @Column(value = "all_order_demand_total_Core")
    private Long allOrderDemandTotalAmount = 0L;

    /**
     * 本月所有订单（包含满足方式评估、交付供应）-需求总核心数<br/>Column: [all_order_cross_month_total_Core]
     */
    @Column(value = "all_order_current_month_total_Core")
    private Long allOrderCurrentMonthTotalAmount = 0L;

    /**
     * 跨月所有订单（包含满足方式评估、交付供应）-需求总核心数<br/>Column: [all_order_cross_month_total_Core]
     */
    @Column(value = "all_order_cross_month_total_Core")
    private Long allOrderCrossMonthTotalAmount = 0L;

    /**
     * 需求总核心数<br/>Column: [demand_total_core]
     */
    @Column(value = "demand_total_core")
    private Long demandTotalAmount = 0L;

    /**
     * 跨月需求总核心数<br/>Column: [cross_month_total_core]
     */
    @Column(value = "cross_month_total_core")
    private Long crossMonthTotalAmount = 0L;

    /**
     * 本月需求总核心数<br/>Column: [current_month_total_core]
     */
    @Column(value = "current_month_total_core")
    private Long currentMonthTotalAmount = 0L;

    /**
     * 满足核心数<br/>Column: [satisfy_core]
     */
    @Column(value = "satisfy_core")
    private Long satisfyAmount = 0L;

    /**
     * (本月)满足核心数<br/>Column: [current_month_satisfy_core]
     */
    @Column(value = "current_month_satisfy_core")
    private Long currentMonthSatisfyAmount = 0L;

    /**
     * (跨月)满足核心数<br/>Column: [cross_month_satisfy_core]
     */
    @Column(value = "cross_month_satisfy_core")
    private Long crossMonthSatisfyAmount = 0L;

    /**
     * 已到订单结束时间-需求总核心数<br/>Column: [finish_order_demand_total_core]
     */
    @Column(value = "finish_order_demand_total_core")
    private Long finishOrderDemandTotalAmount = 0L;

    /**
     * (本月)已到订单结束时间-需求总核心数<br/>Column: [finish_order_current_month_demand_total_core]
     */
    @Column(value = "finish_order_current_month_demand_total_core")
    private Long finishOrderCurrentMonthDemandTotalAmount = 0L;

    /**
     * (跨月)已到订单结束时间-需求总核心数<br/>Column: [finish_order_cross_month_demand_total_core]
     */
    @Column(value = "finish_order_cross_month_demand_total_core")
    private Long finishOrderCrossMonthDemandTotalAmount = 0L;

    /**
     * 已到订单结束时间-满足核心数<br/>Column: [finish_order_satisfy_core]
     */
    @Column(value = "finish_order_satisfy_core")
    private Long finishOrderSatisfyAmount = 0L;

    /**
     * (本月)已到订单结束时间-满足核心数<br/>Column: [finish_order_current_month_satisfy_core]
     */
    @Column(value = "finish_order_current_month_satisfy_core")
    private Long finishOrderCurrentMonthSatisfyAmount = 0L;

    /**
     * (跨月)已到订单结束时间-满足核心数<br/>Column: [finish_order_cross_month_satisfy_core]
     */
    @Column(value = "finish_order_cross_month_satisfy_core")
    private Long finishOrderCrossMonthSatisfyAmount = 0L;

    /**
     * 期初服务规模核心<br/>Column: [start_service_core]  上月最后一天的服务规模
     */
    @Column(value = "start_service_core")
    private BigDecimal startServiceAmount = new BigDecimal(0);

    /**
     * 期末服务规模<br/>Column: [end_service_core]  如果是当前月 就是当月最新一天的规模， 如果是历史月， 就是当前月最后一天的服务规模
     */
    @Column(value = "end_service_core")
    private BigDecimal endServiceAmount = new BigDecimal(0);

    /**
     * 实际购买核心<br/>Column: [buy_total_core]
     */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalAmount = new BigDecimal(0);

    @Column(value = "current_order_number_list", isJSON = true)
    private List<String> currentOrderNumberList = ListUtils.newArrayList();

    @Column(value = "cross_order_number_list", isJSON = true)
    private List<String> crossOrderNumberList = ListUtils.newArrayList();


    public static AwsMonthPerformanceTrackDfDO build(CrpPplOrderItemAndInfoVO first, String yearMonth, LocalDate dataDate, List<String> newGenerationInstanceType) {
        AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO = new AwsMonthPerformanceTrackDfDO();
        BeanUtils.copyProperties(first, awsOrderPerformanceTrackDfDO);
        awsOrderPerformanceTrackDfDO.setYearMonth(yearMonth);
        awsOrderPerformanceTrackDfDO.setCommonCustomerName(first.getCommonCustomerShortName());
        awsOrderPerformanceTrackDfDO.setOrderType(OrderTypeEnum.getNameByCode(first.getOrderType()));
        awsOrderPerformanceTrackDfDO.setStatTime(dataDate);
        awsOrderPerformanceTrackDfDO.setUin(ListUtils.newArrayList(first.getCustomerUin()));
        awsOrderPerformanceTrackDfDO.setCurrentOrderNumberList(ListUtils.newArrayList(first.getOrderNumber()));
        awsOrderPerformanceTrackDfDO.setFuzzyArea("zoneName");
        awsOrderPerformanceTrackDfDO.setFuzzyInstance("instanceType");
        awsOrderPerformanceTrackDfDO.setIsNewInstanceType(
                newGenerationInstanceType.contains(first.getInstanceType()));
        if (StringUtils.equals(first.getOrderCategory(), ProductCategoryEnum.CVM.getCode())) {
            awsOrderPerformanceTrackDfDO.setStatisticalCaliber("核心");
        } else {
            awsOrderPerformanceTrackDfDO.setStatisticalCaliber("部署内存");
        }
        return awsOrderPerformanceTrackDfDO;
    }

    public static  AwsMonthPerformanceTrackDfDO build(CrpPplOrderItemAndInfoVO vo, String yearMonth, LocalDate dataDate, String fuzzyArea, String fuzzyInstance) {
        // 基础信息初始化
        AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO = new AwsMonthPerformanceTrackDfDO();
        awsOrderPerformanceTrackDfDO.setStatTime(dataDate);
        awsOrderPerformanceTrackDfDO.setFuzzyArea(fuzzyArea);
        awsOrderPerformanceTrackDfDO.setFuzzyInstance(fuzzyInstance);

        BeanUtils.copyProperties(vo, awsOrderPerformanceTrackDfDO);
        awsOrderPerformanceTrackDfDO.setYearMonth(yearMonth);
        awsOrderPerformanceTrackDfDO.setCommonCustomerName(vo.getCommonCustomerShortName());
        awsOrderPerformanceTrackDfDO.setOrderType(OrderTypeEnum.getNameByCode(vo.getOrderType()));
        //新增订单的弹性类型置为空值
        awsOrderPerformanceTrackDfDO.setElasticType(Constant.EMPTY_VALUE_STR);
        if (StringUtils.equals(vo.getOrderCategory(), ProductCategoryEnum.CVM.getCode())) {
            awsOrderPerformanceTrackDfDO.setStatisticalCaliber("核心");
        } else {
            awsOrderPerformanceTrackDfDO.setStatisticalCaliber("部署内存");
        }

        List<String> newGenerationInstanceType = SpringUtil.getBean(DictService.class).getNewGenerationInstanceType();
        awsOrderPerformanceTrackDfDO.setIsNewInstanceType(
                newGenerationInstanceType.contains(vo.getInstanceType()));

        return  awsOrderPerformanceTrackDfDO;
    }
}