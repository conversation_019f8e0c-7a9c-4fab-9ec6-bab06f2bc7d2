package cloud.demand.lab.modules.order.service.impl;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.utils.DateUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.PaasProductUinWhiteListDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.PplDatabaseEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.PplProductEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.YunxiaoAppRoleEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.constant.OrderConstant;
import cloud.demand.lab.modules.order.dto.CrpPplOrderItemAndInfoVO;
import cloud.demand.lab.modules.order.dto.GroupOrderReq;
import cloud.demand.lab.modules.order.dto.GroupOrderResp;
import cloud.demand.lab.modules.order.dto.MonthScaleDTO;
import cloud.demand.lab.modules.order.dto.OrderCategoryProductVO;
import cloud.demand.lab.modules.order.dto.OrderItemReq;
import cloud.demand.lab.modules.order.dto.PerformanceOrderItemVO;
import cloud.demand.lab.modules.order.dto.PerformanceParamTypeReq;
import cloud.demand.lab.modules.order.dto.PerformanceTrackItem;
import cloud.demand.lab.modules.order.dto.PerformanceTrackReq;
import cloud.demand.lab.modules.order.dto.PerformanceTrackResp;
import cloud.demand.lab.modules.order.dto.PerformanceTrackSummaryResp;
import cloud.demand.lab.modules.order.dto.PerformanceTrackSummaryResp.MonthTarget;
import cloud.demand.lab.modules.order.entity.IFuzzyKey;
import cloud.demand.lab.modules.order.entity.std_table.AwsMonthPerformanceTrackDfDO;
import cloud.demand.lab.modules.order.enums.OrderElasticType;
import cloud.demand.lab.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import cloud.demand.lab.modules.order.service.DayScaleService;
import cloud.demand.lab.modules.order.service.PerformanceTrackService;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.alibaba.fastjson.JSONObject;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PerformanceTrackServiceImpl implements PerformanceTrackService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DictService dictService;

    @Resource
    private FillerService fillerService;
    @Resource
    private PerformanceTrackStrategy performanceTrackStrategy;

    @Resource
    private DayScaleService dayScaleService;

    @Override
    @TaskLog(taskName = "initMonthPerformanceTrackDf")
    public void initMonthPerformanceTrackDf(String orderType, String fuzzyInstance, String fuzzyArea) {

        LocalDate dataDate = LocalDate.now().minusDays(1);
        List<AwsMonthPerformanceTrackDfDO> ret = ListUtils.newArrayList();
        if (StringUtils.equals(orderType, OrderTypeEnum.ELASTIC.getName())) {
            ret = initElasticMonthPerformanceTrackDf(dataDate);
        } else {
            ret = initNewMonthPerformanceTrackDf(dataDate, fuzzyInstance, fuzzyArea);
        }
        List<AwsMonthPerformanceTrackDfDO> dbData = DBList.ckcldStdCrpDBHelper.getAll(AwsMonthPerformanceTrackDfDO.class,
                " where stat_time = ? ",
                dataDate.format(DateTimeFormatter.ISO_LOCAL_DATE));

        dbData.removeIf(item -> StringUtils.equals(item.getFuzzyArea(), fuzzyArea) && StringUtils.equals(item.getFuzzyInstance(), fuzzyInstance) && StringUtils.equals(item.getOrderType(), orderType));

        ret.addAll(dbData);
        // 查出
        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.aws_month_performance_track_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                dataDate);

        DBList.ckcldStdCrpDBHelper.insertBatchWithoutReturnId(ret);
        log.info(" initMonthPerformanceTrackDf completed all ");
    }

    private List<AwsMonthPerformanceTrackDfDO> initElasticMonthPerformanceTrackDf(LocalDate dataDate) {
        List<String> newGenerationInstanceType = dictService.getNewGenerationInstanceType();

        OrderItemReq req = new OrderItemReq();
        req.setBeginBuyDate(dataDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        req.setOrderNodeCode(ListUtils.newArrayList(OrderNodeCodeEnum.node_order_match_way.getCode(), OrderNodeCodeEnum.node_order_supply.getCode(),
                OrderNodeCodeEnum.node_order_following.getCode(), OrderNodeCodeEnum.node_order_close.getCode()));
        req.setOrderCategory(ListUtils.newArrayList(ProductCategoryEnum.CVM.getCode(), ProductCategoryEnum.DB.getCode()));
        req.setOrderTypeCode(OrderTypeEnum.ELASTIC.getCode());
        List<CrpPplOrderItemAndInfoVO> itemList = queryOrderItemList(req);

        Map<String, List<CrpPplOrderItemAndInfoVO>> passProductOrderItemListMap = splitOrderItemList(itemList);

        List<AwsMonthPerformanceTrackDfDO> result = new ArrayList<>();
        for (Map.Entry<String, List<CrpPplOrderItemAndInfoVO>> passProductEntry : passProductOrderItemListMap.entrySet()) {
            Map<String, List<CrpPplOrderItemAndInfoVO>> orderItemListMap = ListUtils.groupBy(passProductEntry.getValue(), v -> v.getElasticType());
            for (Map.Entry<String, List<CrpPplOrderItemAndInfoVO>> entry : orderItemListMap.entrySet()) {
                if (ListUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                // 订单类别、订单、uin、实例类型、可用区、开始购买时间、结束购买时间 作为维度
                Function<CrpPplOrderItemAndInfoVO, String> groupKey = v -> String.join("@", v.getOrderCategory(), v.getOrderNumber(), v.getCustomerUin(), v.getInstanceType(), v.getZoneName(),
                        v.getBeginBuyDate().format(DateTimeFormatter.ISO_LOCAL_DATE), v.getEndBuyDate().format(DateTimeFormatter.ISO_LOCAL_DATE));
                if (StringUtils.equals(entry.getKey(), OrderElasticType.ONCE_TIME.getTypeName())) {
                    groupKey = v -> String.join("@", v.getCustomerUin(), v.getOrderType(), v.getIndustryDept(), v.getCommonCustomerShortName(), v.getAppRole(), v.getZoneName(), v.getInstanceType());
                }
                final Function<CrpPplOrderItemAndInfoVO, String> finalGroupKey = groupKey;
                Map<String, List<CrpPplOrderItemAndInfoVO>> group = ListUtils.groupBy(entry.getValue(), item -> finalGroupKey.apply(item));
                group.forEach((key, value) -> {
                    CrpPplOrderItemAndInfoVO first = value.get(0);
                    List<String> yearMonthList = first.getDemandYearMonth();
                    for (String yearMonth : yearMonthList) {
                        AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO = AwsMonthPerformanceTrackDfDO.build(first, yearMonth, dataDate, newGenerationInstanceType);
                        performanceTrackStrategy.fillTrackData(yearMonth, awsOrderPerformanceTrackDfDO, value, null);
                        result.add(awsOrderPerformanceTrackDfDO);
                    }

                });
            }

        }

        return result;
    }

    private List<AwsMonthPerformanceTrackDfDO> initNewMonthPerformanceTrackDf(LocalDate dataDate, String fuzzyInstance, String fuzzyArea) {

        OrderItemReq req = new OrderItemReq();
        req.setBeginBuyDate(dataDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        req.setOrderNodeCode(ListUtils.newArrayList(OrderNodeCodeEnum.node_order_match_way.getCode(), OrderNodeCodeEnum.node_order_supply.getCode(),
                OrderNodeCodeEnum.node_order_following.getCode(), OrderNodeCodeEnum.node_order_close.getCode()));
        req.setOrderCategory(ListUtils.newArrayList(ProductCategoryEnum.CVM.getCode(), ProductCategoryEnum.DB.getCode()));
        req.setOrderTypeCode(OrderTypeEnum.NEW.getCode());
        List<CrpPplOrderItemAndInfoVO> itemList = queryOrderItemList(req);

        Map<String, List<CrpPplOrderItemAndInfoVO>> orderItemListMap = splitOrderItemList(itemList);

        List<AwsMonthPerformanceTrackDfDO> result = new ArrayList<>();

        for (Map.Entry<String, List<CrpPplOrderItemAndInfoVO>> entry : orderItemListMap.entrySet()) {
            if (ListUtils.isEmpty(entry.getValue())) {
                continue;
            }
            if (StringUtils.equals(entry.getKey(), ProductCategoryEnum.DB.getName()) && !StringUtils.equals(fuzzyInstance, "instanceGroup")) {
                // 数据库，不是机型族维度
                continue;
            }
            // 订单类别-需求年月-需求类型-行业-通用客户简称-appRole-地域-可用区 实例类型 作为聚合维度
            Map<String, List<CrpPplOrderItemAndInfoVO>> group = entry.getValue().stream()
                    .collect(Collectors.groupingBy(
                            v -> StringUtils.joinWith("@", v.getDemandYearMonth().get(0), v.getOrderCategory(), v.getOrderType(), v.getIndustryDept(),
                                    v.getCommonCustomerShortName(), v.getUinType(), v.getAppRole(), v.getProduct(), getFuzzyValueFunction(fuzzyArea).apply(v),
                                    getFuzzyValueFunction(fuzzyInstance).apply(v))));

            Map<String, List<CrpPplOrderItemAndInfoVO>> notWithYearMonthGroup = entry.getValue().stream()
                    .collect(Collectors.groupingBy(
                            v -> StringUtils.joinWith("@", v.getOrderCategory(), v.getOrderType(), v.getIndustryDept(),
                                    v.getCommonCustomerShortName(), v.getUinType(), v.getAppRole(), v.getProduct(), getFuzzyValueFunction(fuzzyArea).apply(v),
                                    getFuzzyValueFunction(fuzzyInstance).apply(v))));

            String minDemandYearMonth = entry.getValue().stream()
                    .map(item -> item.getDemandYearMonth().get(0))
                    .sorted(String::compareTo).collect(Collectors.toList()).get(0);
            String currentDemandYearMonth = dataDate.getYear() + "-" + DateUtils.fillZeroMonthOrDay(dataDate.getMonthValue());

            notWithYearMonthGroup.forEach((key, value) -> {
                List<String> yearMonthList = SoeCommonUtils.getMonthsBetween(minDemandYearMonth + "-01", currentDemandYearMonth + "-01");
                Map<String, MonthScaleDTO> monthScaleMap = null;
                String dataYearMonth = minDemandYearMonth;
                // 数据年月不小于当前需求年月时做处理
                while (DateUtils.compareYearMonth(dataYearMonth, currentDemandYearMonth) != 1) {

                    // 获取上月数据
                    String lastYearMonth = DateUtils.plusYearMonth(dataYearMonth, -1);
                    List<CrpPplOrderItemAndInfoVO> lastMonthData = group.get(lastYearMonth + "@" + key);
                    List<CrpPplOrderItemAndInfoVO> currentYearMonthData = group.get(dataYearMonth + "@" + key);

                    if (ListUtils.isEmpty(currentYearMonthData)) {
                        // 如果上月和当前月都没数据 则跳过
                        dataYearMonth = DateUtils.plusYearMonth(dataYearMonth, 1);
                        continue;
                    }

                    // 基础信息初始化
                    CrpPplOrderItemAndInfoVO vo =
                            ListUtils.isNotEmpty(currentYearMonthData) ? currentYearMonthData.get(0) : lastMonthData.get(0);
                    AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO = AwsMonthPerformanceTrackDfDO.build(vo, dataYearMonth, dataDate, fuzzyArea, fuzzyInstance);

                    // 查询履约数据
                    if (Objects.isNull(monthScaleMap)) {
                        monthScaleMap = dayScaleService.queryMonthScale(yearMonthList, awsOrderPerformanceTrackDfDO);
                    }
                    // 构建需求数据
                    buildTrackDemand(dataYearMonth, awsOrderPerformanceTrackDfDO, currentYearMonthData, lastMonthData, monthScaleMap);

                    // 构建履约数据
                    buildTrackBuy(awsOrderPerformanceTrackDfDO, monthScaleMap);


                    result.add(awsOrderPerformanceTrackDfDO);

                    dataYearMonth = DateUtils.plusYearMonth(dataYearMonth, 1);
                }
            });
        }


        return result;
    }


    /**
     * 构建需求情况
     *
     * @param dataYearMonth
     * @param awsOrderPerformanceTrackDfDO
     * @param currentMonthData
     * @param lastMonthData
     * @return
     */
    public void buildTrackDemand(
            String dataYearMonth,
            AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO,
            List<CrpPplOrderItemAndInfoVO> currentMonthData,
            List<CrpPplOrderItemAndInfoVO> lastMonthData, Map<String, MonthScaleDTO> monthScaleMap) {

        Long allOrderDemandTotalAmount = 0L;
        Long demandTotalAmount = 0L;
        Long finishOrderDemandTotalAmount = 0L;
        Long satisfyAmount = 0L;
        Long finishOrderSatisfyAmount = 0L;
        if (ListUtils.isNotEmpty(currentMonthData)) {

            LocalDate limitDate = DateUtils.isCurrentYearMonth(dataYearMonth)
                    ? LocalDate.now().plusDays(1)
                    : LocalDate.parse(DateUtils.getLastDayByYearMonth(dataYearMonth)).plusDays(1);

            // 到达截止日期的订单
            List<String> finishOrder = currentMonthData.stream().filter(o -> o.getEndBuyDate().isBefore(limitDate))
                    .map(CrpPplOrderItemAndInfoVO::getOrderNumber)
                    .collect(Collectors.toList());

            // 所有订单需求核心数
            allOrderDemandTotalAmount = currentMonthData.stream().mapToLong(CrpPplOrderItemAndInfoVO::getTotalAmount)
                    .sum();

            // 仅履约跟踪和订单关闭状态的订单
            List<CrpPplOrderItemAndInfoVO> needCalOrder = currentMonthData.stream().filter(o ->
                            o.getOrderNodeCode().equals(OrderNodeCodeEnum.node_order_following.getCode())
                                    || o.getOrderNodeCode().equals(OrderNodeCodeEnum.node_order_close.getCode()))
                    .collect(Collectors.toList());

            // 需求总核心数 （仅履约跟踪 和 订单关闭）
            demandTotalAmount = needCalOrder.stream()
                    .mapToLong(CrpPplOrderItemAndInfoVO::getTotalAmount).sum();

            // 到达截止日期订单的 需求总核心数
            finishOrderDemandTotalAmount = needCalOrder.stream()
                    .filter(o -> finishOrder.contains(o.getOrderNumber()))
                    .mapToLong(CrpPplOrderItemAndInfoVO::getTotalAmount).sum();

            satisfyAmount = needCalOrder.stream().mapToLong(o -> o.getSatisfyAmount().longValue()).sum();

            finishOrderSatisfyAmount = needCalOrder.stream()
                    .filter(o -> finishOrder.contains(o.getOrderNumber()))
                    .mapToLong(CrpPplOrderItemAndInfoVO::getSatisfyAmount).sum();
        }

        Long lastMonthAllOrderNotBuyDemandAmount = queryLastMonthNotBuyAmount(awsOrderPerformanceTrackDfDO, monthScaleMap, lastMonthData,
                Boolean.FALSE, Boolean.TRUE, Boolean.TRUE);
        Long lastMonthNotBuyDemandAmount = queryLastMonthNotBuyAmount(awsOrderPerformanceTrackDfDO, monthScaleMap, lastMonthData,
                Boolean.FALSE, Boolean.TRUE, Boolean.FALSE);
        Long lastMonthFinishNotBuyDemandAmount = queryLastMonthNotBuyAmount(awsOrderPerformanceTrackDfDO, monthScaleMap, lastMonthData,
                Boolean.TRUE, Boolean.TRUE, Boolean.FALSE);
        Long lastMonthNotBuySatisfyAmount = queryLastMonthNotBuyAmount(awsOrderPerformanceTrackDfDO, monthScaleMap, lastMonthData,
                Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
        Long lastMonthFinishNotBuySatisfyAmount = queryLastMonthNotBuyAmount(awsOrderPerformanceTrackDfDO, monthScaleMap, lastMonthData,
                Boolean.TRUE, Boolean.FALSE, Boolean.FALSE);

        // 所有订单
        awsOrderPerformanceTrackDfDO.setAllOrderDemandTotalAmount(allOrderDemandTotalAmount + lastMonthAllOrderNotBuyDemandAmount);
        awsOrderPerformanceTrackDfDO.setAllOrderCrossMonthTotalAmount(lastMonthAllOrderNotBuyDemandAmount);
        awsOrderPerformanceTrackDfDO.setAllOrderCurrentMonthTotalAmount(allOrderDemandTotalAmount);

        awsOrderPerformanceTrackDfDO.setDemandTotalAmount(demandTotalAmount + lastMonthNotBuyDemandAmount);
        awsOrderPerformanceTrackDfDO.setCrossMonthTotalAmount(lastMonthNotBuyDemandAmount);
        awsOrderPerformanceTrackDfDO.setCurrentMonthTotalAmount(demandTotalAmount);

        awsOrderPerformanceTrackDfDO.setFinishOrderDemandTotalAmount(finishOrderDemandTotalAmount + lastMonthFinishNotBuyDemandAmount);
        awsOrderPerformanceTrackDfDO.setFinishOrderCurrentMonthDemandTotalAmount(finishOrderDemandTotalAmount);
        awsOrderPerformanceTrackDfDO.setFinishOrderCrossMonthDemandTotalAmount(lastMonthFinishNotBuyDemandAmount);

        awsOrderPerformanceTrackDfDO.setSatisfyAmount(satisfyAmount + lastMonthNotBuySatisfyAmount);
        awsOrderPerformanceTrackDfDO.setCurrentMonthSatisfyAmount(satisfyAmount);
        awsOrderPerformanceTrackDfDO.setCrossMonthSatisfyAmount(lastMonthNotBuySatisfyAmount);

        awsOrderPerformanceTrackDfDO.setFinishOrderSatisfyAmount(finishOrderSatisfyAmount + lastMonthFinishNotBuySatisfyAmount);
        awsOrderPerformanceTrackDfDO.setFinishOrderCurrentMonthSatisfyAmount(finishOrderSatisfyAmount);
        awsOrderPerformanceTrackDfDO.setFinishOrderCrossMonthSatisfyAmount(lastMonthFinishNotBuySatisfyAmount);

        if (ListUtils.isNotEmpty(currentMonthData)) {
            List<String> orderNumber = currentMonthData.stream().map(CrpPplOrderItemAndInfoVO::getOrderNumber).collect(Collectors.toList());
            awsOrderPerformanceTrackDfDO.setCurrentOrderNumberList(orderNumber);
        }
        if (ListUtils.isNotEmpty(lastMonthData)) {
            List<String> orderNumber = lastMonthData.stream().map(CrpPplOrderItemAndInfoVO::getOrderNumber).collect(Collectors.toList());
            awsOrderPerformanceTrackDfDO.setCrossOrderNumberList(orderNumber);
        }
    }

    /**
     * 查询上月未履约数据
     */
    public Long queryLastMonthNotBuyAmount(AwsMonthPerformanceTrackDfDO trackDO, Map<String, MonthScaleDTO> monthScaleMap,
                                           List<CrpPplOrderItemAndInfoVO> dealData,
                                           Boolean endBuyDateIsEnd,
                                           Boolean isDemandCore,
                                           Boolean isAllOrder) {
        if (ListUtils.isEmpty(dealData)) {
            return 0L;
        }
        List<CrpPplOrderItemAndInfoVO> lastMonthData = new ArrayList<>(dealData);
        lastMonthData.removeIf(o -> {
            // 当月购买当月释放的不算进在内
            if (o.getBeginBuyDate().getMonthValue() == o.getEndBuyDate().getMonthValue()) {
                return true;
            }
            if (!isAllOrder) {
                if (o.getOrderNodeCode().equals(OrderNodeCodeEnum.node_order_match_way.getCode())
                        || o.getOrderNodeCode().equals(OrderNodeCodeEnum.node_order_supply.getCode())) {
                    return true;
                }
            }
            if (endBuyDateIsEnd) {
                String lastYearMonth = DateUtils.plusYearMonth(trackDO.getYearMonth(), -1);
                LocalDate limitDate = LocalDate.parse(DateUtils.getLastDayByYearMonth(lastYearMonth)).plusDays(1);

                return o.getEndBuyDate().isBefore(limitDate);
            }
            return false;
        });
        if (ListUtils.isEmpty(lastMonthData)) {
            return 0L;
        }

        Long sum = 0L;
        if (isDemandCore) {
            sum = lastMonthData.stream().mapToLong(CrpPplOrderItemAndInfoVO::getTotalAmount).sum();
        } else {
            sum = lastMonthData.stream().mapToLong(CrpPplOrderItemAndInfoVO::getSatisfyAmount).sum();
        }

        if (sum.equals(0L)) {
            return 0L;
        } else {
            MonthScaleDTO lastMonthScale = monthScaleMap.getOrDefault(DateUtils.plusYearMonth(trackDO.getYearMonth(), -1), new MonthScaleDTO());
            sum = sum - BigDecimal.ZERO.max(lastMonthScale.getMonthScale()).longValue();
            return Math.max(sum, 0L);
        }


    }

    /**
     * 构建履约情况
     *
     * @param awsOrderPerformanceTrackDfDO
     */
    public void buildTrackBuy(AwsMonthPerformanceTrackDfDO awsOrderPerformanceTrackDfDO, Map<String, MonthScaleDTO> monthScaleMap) {

        // 上月最后一天
        MonthScaleDTO monthScaleDTO = monthScaleMap.getOrDefault(awsOrderPerformanceTrackDfDO.getYearMonth(), new MonthScaleDTO());

        awsOrderPerformanceTrackDfDO.setStartServiceAmount(monthScaleDTO.getMonthStartScale());
        awsOrderPerformanceTrackDfDO.setEndServiceAmount(monthScaleDTO.getMonthEndScale());
        awsOrderPerformanceTrackDfDO.setBuyTotalAmount(monthScaleDTO.getMonthScale().max(BigDecimal.ZERO));

    }

    /**
     * if type=currentMonth 统计本月 ,if type = crossMonth 统计跨月 else 统计本月+跨月
     *
     * @param req
     * @param type
     * @return
     */
    public static Function<AwsMonthPerformanceTrackDfDO, Long> getDemandTotalAmount(PerformanceTrackReq req, String type) {
        if (req.getFilterNotEndOrder()) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCurrentMonthDemandTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCrossMonthDemandTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderDemandTotalAmount;
            }
        } else if (req.getIsAllOrder()) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getAllOrderCurrentMonthTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getAllOrderCrossMonthTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getAllOrderDemandTotalAmount;
            }
        } else {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCurrentMonthTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCrossMonthTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getDemandTotalAmount;
            }
        }
    }

    /**
     * if type=currentMonth 统计本月 ,if type = crossMonth 统计跨月 else 统计本月+跨月
     *
     * @param req
     * @param type
     * @return
     */
    public static Function<AwsMonthPerformanceTrackDfDO, Long> getSatisfyAmount(PerformanceTrackReq req, String type) {
        if (req.getFilterNotEndOrder()) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCurrentMonthSatisfyAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCrossMonthSatisfyAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderSatisfyAmount;
            }
        } else {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCurrentMonthSatisfyAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCrossMonthSatisfyAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getSatisfyAmount;
            }
        }
    }

    public static String getGroupKey(PerformanceTrackReq req, AwsMonthPerformanceTrackDfDO item) {
        Set<String> groupFields = new HashSet<>();
        if (StringUtils.equals(req.getStatisticalCaliber(), "instanceType")) {
            groupFields.add(item.getInstanceType());
        } else if (StringUtils.equals(req.getStatisticalCaliber(), "instanceGroup")) {
            groupFields.add(item.getInstanceGroup());
        } else {
            throw new BizException("非法的机型统计口径");
        }
        if (StringUtils.equals(req.getAreaStatisticalCaliber(), "countryName")) {
            groupFields.add(item.getCountryName());
        } else if (StringUtils.equals(req.getAreaStatisticalCaliber(), "regionName")) {
            groupFields.add(item.getRegionName());
        } else if (StringUtils.equals(req.getAreaStatisticalCaliber(), "zoneName")) {
            groupFields.add(item.getZoneName());
        } else {
            throw new BizException("非法的范围 统计口径");
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "industryDept"))) {
            groupFields.add(item.getIndustryDept());
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "warZone"))) {
            groupFields.add(item.getIndustryDept());
            groupFields.add(item.getWarZone());
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "customhouseTitle"))) {
            groupFields.add(item.getIndustryDept());
            groupFields.add(item.getWarZone());
            groupFields.add(item.getCustomhouseTitle());
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "countryName"))) {
            groupFields.add(item.getCountryName());
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "regionName"))) {
            groupFields.add(item.getRegionName());
            groupFields.add(item.getCountryName());
        }
        if (ListUtils.contains(req.getDims(), dim -> StringUtils.equals(dim, "zoneName"))) {
            groupFields.add(item.getZoneName());
            groupFields.add(item.getRegionName());
            groupFields.add(item.getCountryName());
        }
        return StringUtils.joinWith("@", groupFields.toArray());

    }

    @Override
    public PerformanceTrackSummaryResp queryMonthPerformanceTrackSummary(PerformanceTrackReq req) {
        PerformanceTrackSummaryResp resp = new PerformanceTrackSummaryResp();
        PerformanceTrackResp sourceResp = queryMonthPerformanceTrackItemList(req);
        List<PerformanceTrackItem> data = sourceResp.getData();
        if (ListUtils.isEmpty(data)) {
            return resp;
        }
        List<MonthTarget> monthTargetList = new ArrayList<>();
        for (PerformanceTrackItem datum : data) {
            MonthTarget monthTarget = new MonthTarget();
            monthTarget.setYearMonth(datum.getYearMonth());
            monthTarget.setDemandTotalCore(datum.getDemandTotalCore());
            monthTarget.setSatisfyCore(datum.getSatisfyCore());
            monthTarget.setBuyTotalCore(datum.getBuyTotalCore().setScale(0, BigDecimal.ROUND_HALF_UP));
            monthTarget.setNotBuyTotalCore(datum.getNotBuyTotalCore().setScale(0, BigDecimal.ROUND_HALF_UP));
            monthTarget.setBuyRate(SoeCommonUtils.multiply(datum.getBuyRate(), new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString()+ "%");
            monthTarget.setAmount();
            monthTargetList.add(monthTarget);
        }
        monthTargetList = monthTargetList.stream().sorted(Comparator.comparing(MonthTarget::getYearMonth)).collect(Collectors.toList());
        resp.setData(monthTargetList);
        if (monthTargetList.size() >= 1) {
            resp.setLastMonth(monthTargetList.get(monthTargetList.size() - 1));
        }
        if (monthTargetList.size() >= 2) {
            resp.setLastLastMonth(monthTargetList.get(monthTargetList.size() - 2));
        }

        return resp;
    }

    @Override
    public PerformanceTrackResp queryMonthPerformanceTrackItemList(PerformanceTrackReq req) {
        PerformanceTrackResp resp = new PerformanceTrackResp();
        List<AwsMonthPerformanceTrackDfDO> list = getPerformanceTrackDfList(req);
        if (ListUtils.isEmpty(list)) {
            return resp;
        }

        // 计算汇总指标
        /*PerformanceTrackItem performanceTrackItem = buildTarget(req, "", req.getDims(), list);

        List<PerformanceTrackItem> result = new ArrayList<>();
        getLowestItemList(performanceTrackItem.getChildItem(), result);*/
        List<PerformanceTrackItem> itemList = ListUtils.transform(list, item -> PerformanceTrackItem.transform(item, req.getFilterNotEndOrder(), req.getIsAllOrder()));
        List<PerformanceTrackItem> result = new ArrayList<>();
        List<String> sumFields = ListUtils.newArrayList("allOrderDemandTotalCore", "demandTotalCore", "currentMonthTotalCore", "crossMonthTotalCore",
                "satisfyCore", "currentMonthSatisfyCore", "crossMonthSatisfyCore", "buyTotalCore", "notBuyTotalCore", "currentOrderNumberList", "crossOrderNumberList");
        ListUtils.groupBy(itemList, item -> GroupUtils.getDimsGroupKey(item, req.getDims()))
                .forEach((k, v) -> {
                    PerformanceTrackItem trackItem = GroupUtils.mergeList(v, req.getDims(), sumFields);
                    result.add(trackItem);
                });
        for (PerformanceTrackItem item : result) {
            if (req.getIsAllOrder()) {
                // 所有订单的条件下 未购买 = 需求-已购买
                item.setNotBuyTotalCore(SoeCommonUtils.sub(BigDecimal.valueOf(item.getDemandTotalCore()), item.getBuyTotalCore()).max(BigDecimal.ZERO));
            }
            item.setAmount();
            item.setBuyRate(SoeCommonUtils.divide(item.getBuyTotalCore(), BigDecimal.valueOf(item.getSatisfyCore())));
        }
        resp.setData(result);
        return resp;
    }

    private List<AwsMonthPerformanceTrackDfDO> getPerformanceTrackDfList(PerformanceTrackReq req) {
        List<String> orderTypeList = req.getOrderType();
        if (ListUtils.isEmpty(orderTypeList)) {
            orderTypeList = ListUtils.newArrayList(OrderTypeEnum.NEW.getName(), OrderTypeEnum.ELASTIC.getName());
        }
        List<AwsMonthPerformanceTrackDfDO> retList = new ArrayList<>();
        for (String orderType : orderTypeList) {
            PerformanceTrackReq tempReq = new PerformanceTrackReq();
            BeanUtils.copyProperties(req, tempReq);
            tempReq.setOrderType(ListUtils.newArrayList(orderType));
            if (StringUtils.equals(orderType, OrderTypeEnum.ELASTIC.getName())) {
                //弹性只有instanceType和zoneName
                tempReq.setStatisticalCaliber("instanceType");
                tempReq.setAreaStatisticalCaliber("zoneName");
            } else {
                tempReq.setElasticType(null);
            }
            WhereSQL whereSQL = new WhereSQL();
            buildWhereSql(whereSQL, tempReq);
            List<AwsMonthPerformanceTrackDfDO> data = DBList.prodReadOnlyCkStdCrpDBHelper.getAll(
                    AwsMonthPerformanceTrackDfDO.class,
                    whereSQL.getSQL(), whereSQL.getParams());
            if (StringUtils.equals(orderType, OrderTypeEnum.NEW.getName())) {
                data.forEach(item -> item.setElasticType(Constant.EMPTY_VALUE_STR));
            }
            retList.addAll(data);
        }
        return retList;
    }

    /**
     * 获取最底层的数据
     *
     * @param source
     * @param result
     * @return
     */
    public void getLowestItemList(List<PerformanceTrackItem> source, List<PerformanceTrackItem> result) {
        for (PerformanceTrackItem performanceTrackItem : source) {
            if (performanceTrackItem.getChildItem() == null) {
                result.add(performanceTrackItem);
            } else {
                getLowestItemList(performanceTrackItem.getChildItem(), result);
            }
        }
    }

    @Override
    public PerformanceTrackResp queryAllOrderMonthTrack(PerformanceTrackReq req) {
        return queryMonthPerformanceTrackItemList(req);
    }

    @Override
    public GroupOrderResp queryGroupOrder(GroupOrderReq req) {
        List<String> orderTypeList = req.getOrderType();
        if (ListUtils.isEmpty(orderTypeList)) {
            orderTypeList = ListUtils.newArrayList(OrderTypeEnum.NEW.getName(), OrderTypeEnum.ELASTIC.getName());
        }
        List<PerformanceOrderItemVO> orderList = new ArrayList<>();
        for (String orderType : orderTypeList) {
            OrderItemReq orderItemReq = OrderItemReq.transform(req, orderType);

            List<CrpPplOrderItemAndInfoVO> itemList = queryOrderItemList(orderItemReq);
            List<String> orderNumberJsonList = itemList.stream().map(item -> JSONObject.toJSONString(ListUtils.newArrayList(item.getOrderNumber())))
                    .collect(Collectors.toList());
            Map<String, AwsMonthPerformanceTrackDfDO> dfMap = queryPerformanceTrackDfList(req, orderNumberJsonList);
            for (CrpPplOrderItemAndInfoVO item : itemList) {
                orderList.addAll(PerformanceOrderItemVO.transform(item, req.getStartYearMonth(), req.getEndYearMonth(), dfMap));
            }
        }
        if(req.getFilterNotEndOrder()){
            orderList = orderList.stream().filter(item -> {
                LocalDate lastDayOfMonth = LocalDate.parse(item.getYearMonth()+"-01").with(TemporalAdjusters.lastDayOfMonth());
                LocalDate limitDate = lastDayOfMonth.isBefore(LocalDate.now()) ? lastDayOfMonth : LocalDate.now();
                if(item.getEndBuyDate() != null && item.getEndBuyDate().isAfter(limitDate)) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        List<PerformanceOrderItemVO> retList = ListUtils.newArrayList();
        List<String> dims = req.getDims();
        List<String> sumFields = ListUtils.newArrayList("totalAmount", "satisfyAmount", "buyTotalAmount");
        ListUtils.groupBy(orderList, item -> GroupUtils.getDimsGroupKey(item, dims))
                .forEach((k, v) -> {
                    PerformanceOrderItemVO vo = GroupUtils.mergeList(v, dims, sumFields);
                    retList.add(vo);
                });
        retList.forEach(item -> item.setCore());
        GroupOrderResp groupOrderResp = new GroupOrderResp();
        groupOrderResp.setList(retList);
        return groupOrderResp;
    }

    private Map<String, AwsMonthPerformanceTrackDfDO> queryPerformanceTrackDfList(GroupOrderReq req, List<String> orderNumberJsonList) {
        PerformanceTrackReq performanceTrackReq = GroupOrderReq.transform(req, orderNumberJsonList);
        List<AwsMonthPerformanceTrackDfDO> list = getPerformanceTrackDfList(performanceTrackReq);
        return ListUtils.toMap(list, item -> StringUtils.joinWith("@", item.getElasticType(), item.getYearMonth(), item.getZoneName(), item.getInstanceType(), item.getCurrentOrderNumberList()), item -> item);
    }

    private List<CrpPplOrderItemAndInfoVO> queryOrderItemList(OrderItemReq req) {
        if (ListUtils.isEmpty(req.getOrderNodeCode())) {
            req.setOrderNodeCode(ListUtils.newArrayList(OrderNodeCodeEnum.node_order_following.getCode(), OrderNodeCodeEnum.node_order_close.getCode()));
        }
        WhereBuilder whereBuilder = new WhereBuilder(req, CrpPplOrderItemAndInfoVO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();

        if (StringUtils.isNotBlank(req.getStartYearMonth()) && StringUtils.isNotBlank(req.getEndYearMonth())) {
            String first = req.getStartYearMonth() + "-01";
            String last = DateUtils.getLastDayByYearMonth(req.getEndYearMonth());
            if (StringUtils.equals(OrderTypeEnum.NEW.getCode(), req.getOrderTypeCode())) {
                whereSQL.and(" begin_buy_date >= ? ", first);
                whereSQL.and(" begin_buy_date <= ? ", last);
            } else {
                WhereSQL orSql = new WhereSQL();
                orSql.or(" ( begin_buy_date >= ? and begin_buy_date <= ? ) ", first, last);
                orSql.or(" ( end_buy_date >= ? and end_buy_date <= ? )", first, last);
                orSql.or(" ( begin_buy_date <= ? and end_buy_date >= ? )", first, last);
                whereSQL.and(orSql);
            }

        }

        if (req.getFilterNotEndOrder()) {
            whereSQL.and(" end_buy_date <= ?", LocalDate.now());
        }

        String sql = ORMUtils.getSql("/sql/order/query_order_info.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());

        List<CrpPplOrderItemAndInfoVO> itemList = demandDBHelper.getRaw(CrpPplOrderItemAndInfoVO.class, sql, whereSQL.getParams());
        Map<String, String> zoneNameMap = dictService.getZoneNameMap();
        Map<String, String> regionMap = dictService.getRegionNameMap();
        for (CrpPplOrderItemAndInfoVO item : itemList) {
            //填充zone
            item.setZone(zoneNameMap.getOrDefault(item.getZoneName(), Constant.EMPTY_VALUE_STR));
            //填充region
            item.setRegion(regionMap.getOrDefault(item.getRegionName(), Constant.EMPTY_VALUE_STR));
            if (StringUtils.equals(OrderTypeEnum.NEW.getCode(), req.getOrderTypeCode())) {
                item.setDemandYearMonth(ListUtils.newArrayList(item.getBeginYearMonth()));
            } else {
                LocalDate beginBuyDate = item.getBeginBuyDate();
                LocalDate endBuyDate = LocalDate.now().isBefore(item.getEndBuyDate()) ? LocalDate.now() : item.getEndBuyDate();
                item.setDemandYearMonth(SoeCommonUtils.getMonthsBetween(beginBuyDate, endBuyDate));
            }
        }
        // 境内外等字段赋值
        fillerService.fill(itemList);
        //过滤bizType、paasProduct、countryName
        Iterator<CrpPplOrderItemAndInfoVO> iter = itemList.iterator();
        List<String> dbProduct = ListUtils.newArrayList(PplDatabaseEnum.MySQL.getName(), PplDatabaseEnum.Redis.getName());
        while (iter.hasNext()) {
            CrpPplOrderItemAndInfoVO it = iter.next();
            if (StringUtils.equals(it.getOrderCategory(), ProductCategoryEnum.DB.getCode()) &&
                    !ListUtils.contains(dbProduct, item -> StringUtils.equals(item, it.getProduct()))) {
                //数据库过滤掉非MySQL和Redis
                iter.remove();
            }
            // 过滤isNewInstanceType
            if (Objects.nonNull(req.getIsNewInstanceType()) && req.getIsNewInstanceType() != it.getIsNewInstanceType()) {
                iter.remove();
                continue;
            }
            // 过滤bizType
            if (ListUtils.isNotEmpty(req.getBizType()) && !req.getBizType().contains(it.getBizType())) {
                iter.remove();
                continue;
            }
            // 过滤paasProduct
            if (ListUtils.isNotEmpty(req.getPaasProduct()) && !req.getPaasProduct().contains(it.getPaasProduct())) {
                iter.remove();
                continue;
            }
            // 过滤countryName
            if (ListUtils.isNotEmpty(req.getCountryName()) && !req.getCountryName().contains(it.getCountryName())) {
                iter.remove();
            }

            // 过滤instanceGroup
            if (ListUtils.isNotEmpty(req.getInstanceGroup()) && !req.getInstanceGroup().contains(it.getInstanceGroup())) {
                iter.remove();
            }
            // 过滤uinType
            if (ListUtils.isNotEmpty(req.getUinType()) && !req.getUinType().contains(it.getUinType())) {
                iter.remove();
            }
        }
        return itemList;
    }

    @Override
    public List<String> queryParams(PerformanceParamTypeReq req) {
        String column = ORMUtils.getColumnByFieldName(AwsMonthPerformanceTrackDfDO.class, req.getParamType());
        if (column == null) {
            return new ArrayList<>();
        }
        List<String> statTimeList = DBList.ckcldStdCrpDBHelper.getRaw(String.class,
                "select distinct stat_time from std_crp.aws_month_performance_track_df order by stat_time desc limit 2");
        String sql = "select distinct ${paramType} from std_crp.aws_month_performance_track_df where stat_time in (?)";
        List<String> ret = DBList.ckcldStdCrpDBHelper.getRaw(String.class, sql.replace("${paramType}", column), statTimeList);
        if (StringUtils.equals(req.getParamType(), "paasProduct")) {
            ret.remove("CVM&CBS");
        }
        return ret;
    }

    @Override
    public List<OrderCategoryProductVO> queryProduct() {
        List<String> statTimeList = DBList.ckcldStdCrpDBHelper.getRaw(String.class,
                "select distinct stat_time from std_crp.aws_month_performance_track_df order by stat_time desc limit 2");
        String sql = "select distinct order_category,product from std_crp.aws_month_performance_track_df where stat_time in (?)";
        List<OrderCategoryProductVO> ret = DBList.ckcldStdCrpDBHelper.getRaw(OrderCategoryProductVO.class, sql, statTimeList);
        return ret;
    }

    public static Function<AwsMonthPerformanceTrackDfDO, Object> genGroupKeyByDimension(String dim) {
        switch (dim) {
            case "yearMonth":
                return AwsMonthPerformanceTrackDfDO::getYearMonth;
            case "bizType":
                return AwsMonthPerformanceTrackDfDO::getBizType;
            case "uinType":
                return AwsMonthPerformanceTrackDfDO::getUinType;
            case "orderType":
                return AwsMonthPerformanceTrackDfDO::getOrderType;
            case "elasticType":
                return AwsMonthPerformanceTrackDfDO::getElasticType;
            case "paasProduct":
                return AwsMonthPerformanceTrackDfDO::getPaasProduct;
            case "industryDept":
                return AwsMonthPerformanceTrackDfDO::getIndustryDept;
            case "warZone":
                return AwsMonthPerformanceTrackDfDO::getWarZone;
            case "commonCustomerName":
                return AwsMonthPerformanceTrackDfDO::getCommonCustomerName;
            case "instanceType":
                return AwsMonthPerformanceTrackDfDO::getInstanceType;
            case "instanceGroup":
                return AwsMonthPerformanceTrackDfDO::getInstanceGroup;
            case "customhouseTitle":
                return AwsMonthPerformanceTrackDfDO::getCustomhouseTitle;
            case "countryName":
                return AwsMonthPerformanceTrackDfDO::getCountryName;
            case "regionName":
                return AwsMonthPerformanceTrackDfDO::getRegionName;
            case "zoneName":
                return AwsMonthPerformanceTrackDfDO::getZoneName;
            case "isNewInstanceType":
                return AwsMonthPerformanceTrackDfDO::getIsNewInstanceType;
            case "product":
                return AwsMonthPerformanceTrackDfDO::getProduct;
            default:
                throw new BizException("非法的聚合维度");
        }
    }

    public static Function<IFuzzyKey, String> getFuzzyValueFunction(String fuzzyKey) {
        if (StringUtils.equals(fuzzyKey, "instanceType")) {
            return IFuzzyKey::getInstanceType;
        }
        if (StringUtils.equals(fuzzyKey, "instanceGroup")) {
            return IFuzzyKey::getInstanceGroup;
        }
        if (StringUtils.equals(fuzzyKey, "countryName")) {
            return IFuzzyKey::getCountryName;
        }
        if (StringUtils.equals(fuzzyKey, "regionName")) {
            return IFuzzyKey::getRegion;
        }
        if (StringUtils.equals(fuzzyKey, "zoneName")) {
            return IFuzzyKey::getZone;
        }
        throw new BizException("非法的模糊维度");
    }

    /**
     * 根据不同统计口径计算出不同的基层指标 instanceType instanceGroup
     *
     * @param req
     * @param value
     * @return
     */
    public List<PerformanceTrackItem> calBaseTarget(PerformanceTrackReq req,
                                                    List<AwsMonthPerformanceTrackDfDO> value) {

        List<PerformanceTrackItem> result = new ArrayList<>();
        Function<AwsMonthPerformanceTrackDfDO, Long> demandToTotalCore = getDemandTotalAmount(req, null);
        Function<AwsMonthPerformanceTrackDfDO, Long> crossMonthDemandToTotalCore = getDemandTotalAmount(req, "crossMonth");
        Function<AwsMonthPerformanceTrackDfDO, Long> currentMonthDemandToTotalCore = getDemandTotalAmount(req, "currentMonth");
        Function<AwsMonthPerformanceTrackDfDO, Long> crossMonthToSatisfyCore = getSatisfyAmount(req, "crossMonth");
        Function<AwsMonthPerformanceTrackDfDO, Long> currentMonthToSatisfyCore = getSatisfyAmount(req, "currentMonth");
        Function<AwsMonthPerformanceTrackDfDO, Long> satisfyToCore = getSatisfyAmount(req, null);

        //BigDecimal totalSatisfyCore = new BigDecimal(value.stream().mapToLong(satisfyToCore::apply).sum());

        Map<String, List<AwsMonthPerformanceTrackDfDO>> instanceGroup = value.stream()
                .collect(Collectors.groupingBy(v -> getGroupKey(req, v)));
        instanceGroup.forEach((k, v) -> {
            AwsMonthPerformanceTrackDfDO awsMonthPerformanceTrackDfDO = v.get(0);
            PerformanceTrackItem performanceTrackItem = new PerformanceTrackItem();
            BeanUtils.copyProperties(awsMonthPerformanceTrackDfDO, performanceTrackItem);
            Long demandTotalCore = v.stream().mapToLong(demandToTotalCore::apply).sum();
            Long currentMonthDemandTotalCore = v.stream().mapToLong(currentMonthDemandToTotalCore::apply).sum();
            Long crossMonthDemandTotalCore = v.stream().mapToLong(crossMonthDemandToTotalCore::apply).sum();
            Long currentMonthSatisfyCore = v.stream().mapToLong(currentMonthToSatisfyCore::apply).sum();
            Long crossMonthSatisfyCore = v.stream().mapToLong(crossMonthToSatisfyCore::apply).sum();
            Long satisfyCore = v.stream().mapToLong(satisfyToCore::apply).sum();
            // 对buyTotalCore进行求和
            BigDecimal buyTotalCore = v.stream().map(AwsMonthPerformanceTrackDfDO::getBuyTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            List<String> currentOrderNumber = v.stream().map(AwsMonthPerformanceTrackDfDO::getCurrentOrderNumberList).flatMap(List::stream)
                    .collect(Collectors.toList());
            List<String> crossOrderNumber = v.stream().map(AwsMonthPerformanceTrackDfDO::getCrossOrderNumberList).flatMap(List::stream)
                    .collect(Collectors.toList());

            performanceTrackItem.setDemandTotalCore(demandTotalCore);
            performanceTrackItem.setCrossMonthTotalCore(crossMonthDemandTotalCore);
            performanceTrackItem.setCurrentMonthTotalCore(currentMonthDemandTotalCore);
            performanceTrackItem.setSatisfyCore(satisfyCore);
            performanceTrackItem.setCurrentMonthSatisfyCore(currentMonthSatisfyCore);
            performanceTrackItem.setCrossMonthSatisfyCore(crossMonthSatisfyCore);
            performanceTrackItem.setBuyTotalCore(buyTotalCore);
            performanceTrackItem.setCurrentOrderNumberList(currentOrderNumber);
            performanceTrackItem.setCrossOrderNumberList(crossOrderNumber);

            BigDecimal notBuyTotalCore = new BigDecimal(performanceTrackItem.getSatisfyCore()).subtract(
                    performanceTrackItem.getBuyTotalCore());
            performanceTrackItem.setNotBuyTotalCore(notBuyTotalCore.max(new BigDecimal(0)));

            if (performanceTrackItem.getSatisfyCore() != 0) {
                // 履约率 = 履约量 / 满足量
                performanceTrackItem.setBuyRate(performanceTrackItem.getBuyTotalCore()
                        .divide(BigDecimal.valueOf(performanceTrackItem.getSatisfyCore()), 4, RoundingMode.HALF_UP));
            } else {
                performanceTrackItem.setBuyRate(BigDecimal.ZERO);
            }
            result.add(performanceTrackItem);
        });

        return result;

    }

    /**
     * @param req
     * @param parentGroupKey
     * @param sourceDims
     * @param itemList
     * @return
     */
    public PerformanceTrackItem buildTarget(PerformanceTrackReq req, String parentGroupKey, List<String> sourceDims,
                                            List<AwsMonthPerformanceTrackDfDO> itemList) {

        List<PerformanceTrackItem> data = calBaseTarget(req, itemList);

        PerformanceTrackItem performanceTrackItem = new PerformanceTrackItem();
        BeanUtils.copyProperties(data.get(0), performanceTrackItem);
        performanceTrackItem.setGroupKey(parentGroupKey);
        performanceTrackItem.setDemandTotalCore(
                data.stream().mapToLong(PerformanceTrackItem::getDemandTotalCore).sum());
        performanceTrackItem.setCurrentMonthTotalCore(
                data.stream().mapToLong(PerformanceTrackItem::getCurrentMonthTotalCore).sum());
        performanceTrackItem.setCrossMonthTotalCore(
                data.stream().mapToLong(PerformanceTrackItem::getCrossMonthTotalCore).sum());
        performanceTrackItem.setSatisfyCore(data.stream().mapToLong(PerformanceTrackItem::getSatisfyCore).sum());
        performanceTrackItem.setCurrentMonthSatisfyCore(
                data.stream().mapToLong(PerformanceTrackItem::getCurrentMonthSatisfyCore).sum());
        performanceTrackItem.setCrossMonthSatisfyCore(
                data.stream().mapToLong(PerformanceTrackItem::getCrossMonthSatisfyCore).sum());
        performanceTrackItem.setBuyTotalCore(data.stream().map(PerformanceTrackItem::getBuyTotalCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        List<String> currentOrderNumber = data.stream().map(PerformanceTrackItem::getCurrentOrderNumberList).flatMap(List::stream)
                .collect(Collectors.toList());
        performanceTrackItem.setCurrentOrderNumberList(currentOrderNumber);
        List<String> crossOrderNumber = data.stream().map(PerformanceTrackItem::getCrossOrderNumberList).flatMap(List::stream)
                .collect(Collectors.toList());
        performanceTrackItem.setCrossOrderNumberList(crossOrderNumber);

        if (req.getIsAllOrder()) {
            // 所有订单的条件下 未购买 = 需求-已购买
            performanceTrackItem.setNotBuyTotalCore(
                    BigDecimal.valueOf(performanceTrackItem.getDemandTotalCore())
                            .subtract(performanceTrackItem.getBuyTotalCore()).max(BigDecimal.ZERO));

        } else {
            performanceTrackItem.setNotBuyTotalCore(data.stream().map(PerformanceTrackItem::getNotBuyTotalCore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (performanceTrackItem.getSatisfyCore() != 0) {
                performanceTrackItem.setBuyRate(performanceTrackItem.getBuyTotalCore()
                        .divide(BigDecimal.valueOf(performanceTrackItem.getSatisfyCore()), 4, RoundingMode.HALF_UP));
            } else {
                performanceTrackItem.setBuyRate(BigDecimal.ZERO);
            }
        }

        List<String> dims = new ArrayList<>(sourceDims);
        if (ListUtils.isNotEmpty(dims)) {
            String groupKey = dims.get(0);
            dims.remove(0);
            Function<AwsMonthPerformanceTrackDfDO, Object> groupKeyFunction = genGroupKeyByDimension(groupKey);

            Map<Object, List<AwsMonthPerformanceTrackDfDO>> targetYearMonthGroup = itemList.stream()
                    .collect(Collectors.groupingBy(groupKeyFunction::apply));
            List<PerformanceTrackItem> childItem = new ArrayList<>();
            targetYearMonthGroup.forEach((key, value) -> {
                childItem.add(buildTarget(req,
                        StringUtils.isNotBlank(parentGroupKey) ? String.join("@", parentGroupKey, groupKey) : groupKey,
                        dims, value));
            });
            childItem.sort(Comparator.comparing(PerformanceTrackItem::getYearMonth));
            performanceTrackItem.setChildItem(childItem);
        }
        //performanceTrackItem.setBuyRate();
        return performanceTrackItem;

    }


    public void buildWhereSql(WhereSQL whereSQL, PerformanceTrackReq req) {
        if (StringUtils.isEmpty(req.getStatTime())) {
            String latestStatTime = DBList.ckcldStdCrpDBHelper.getRawOne(String.class,
                    "select stat_time from std_crp.aws_month_performance_track_df where fuzzy_instance = ? and fuzzy_area = ? and order_type in (?) order by stat_time desc limit 1",
                    req.getStatisticalCaliber(), req.getAreaStatisticalCaliber(), req.getOrderType());
            req.setStatTime(latestStatTime);
        }


        whereSQL.and(" stat_time = ? ", req.getStatTime());
        whereSQL.and(" fuzzy_instance = ? ", req.getStatisticalCaliber());
        whereSQL.and(" fuzzy_area = ? ", req.getAreaStatisticalCaliber());
        if (req.getIsNewInstanceType() != null) {
            whereSQL.and(" is_new_instance_type = ?", req.getIsNewInstanceType());
        }
        if (ListUtils.isNotEmpty(req.getIndustryDept())) {
            whereSQL.and(" industry_dept in (?) ", req.getIndustryDept());
        }
        if (ListUtils.isNotEmpty(req.getWarZone())) {
            whereSQL.and(" war_zone in (?) ", req.getWarZone());
        }
        if (ListUtils.isNotEmpty(req.getCommonCustomerName())) {
            whereSQL.and(" common_customer_name in (?) ", req.getCommonCustomerName());
        }
        if (ListUtils.isNotEmpty(req.getBizType())) {
            whereSQL.and(" biz_type in (?) ", req.getBizType());
        }
        if (ListUtils.isNotEmpty(req.getUinType())) {
            whereSQL.and(" uin_type in (?) ", req.getUinType());
        }
        if (ListUtils.isNotEmpty(req.getOrderCategory())) {
            whereSQL.and(" order_category in (?) ", req.getOrderCategory());
        }
        if (ListUtils.isNotEmpty(req.getProduct())) {
            whereSQL.and(" product in (?) ", req.getProduct());
        }
        if (ListUtils.isNotEmpty(req.getPaasProduct())) {
            whereSQL.and(" paas_product in (?) ", req.getPaasProduct());
        }
        if (ListUtils.isNotEmpty(req.getOrderType())) {
            whereSQL.and(" order_type in (?) ", req.getOrderType());
        }
        whereSQL.andIf(ListUtils.isNotEmpty(req.getElasticType()), " elastic_type in (?) ", req.getElasticType());
        if (ListUtils.isNotEmpty(req.getAppId())) {
            whereSQL.and(" app_id in (?) ", req.getAppId());
        }
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            whereSQL.and(" customhouse_title in (?) ", req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            whereSQL.and(" instance_type in (?) ", req.getInstanceType());
        }
        if (ListUtils.isNotEmpty(req.getInstanceGroup())) {
            whereSQL.and(" instance_group in (?) ", req.getInstanceGroup());
        }
        if (ListUtils.isNotEmpty(req.getCountryName())) {
            whereSQL.and(" country_name in (?) ", req.getCountryName());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            whereSQL.and(" region_name in (?) ", req.getRegionName());
        }

        if (ListUtils.isNotEmpty(req.getZoneName())) {
            whereSQL.and(" zone_name in (?) ", req.getZoneName());
        }

        if (StringUtils.isNotBlank(req.getStartYearMonth())) {
            whereSQL.and(" year_month >= ? ", req.getStartYearMonth());
        }

        if (StringUtils.isNotBlank(req.getEndYearMonth())) {
            whereSQL.and(" year_month <= ? ", req.getEndYearMonth());
        }
        whereSQL.andIf(ListUtils.isNotEmpty(req.getOrderNumberJson()), " current_order_number_list in ( ? ) ", req.getOrderNumberJson());
        if (req.getFilterNotEndOrder()) {
            whereSQL.and(" finish_order_demand_total_core > 0");
        } else if (req.getIsAllOrder()) {
            whereSQL.and(" all_order_demand_total_Core > 0");
        } else {
            whereSQL.and(" demand_total_core > 0");
        }
    }

    private Map<String, List<CrpPplOrderItemAndInfoVO>> splitOrderItemList(List<CrpPplOrderItemAndInfoVO> list) {
        Map<String, List<CrpPplOrderItemAndInfoVO>> result = new HashMap<>();
        result.put(PplProductEnum.CVM_CBS.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.EMR.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.EKS.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.ES.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.DLC.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.CSIG.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.IEGG.getName(), ListUtils.newArrayList());
        result.put(PplProductEnum.TCHouse.getName(), ListUtils.newArrayList());
        result.put(ProductCategoryEnum.DB.getName(), ListUtils.newArrayList());

        Map<String, Predicate<CrpPplOrderItemAndInfoVO>> orderPredicateMap = getOrderPaasProductPredicate();
        for (CrpPplOrderItemAndInfoVO item : list) {
            if (StringUtils.equals(item.getOrderCategory(), ProductCategoryEnum.CVM.getCode())) {
                if (orderPredicateMap.get(PplProductEnum.EMR.getName()).test(item)) {
                    result.get(PplProductEnum.EMR.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.EKS.getName()).test(item)) {
                    result.get(PplProductEnum.EKS.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.ES.getName()).test(item)) {
                    result.get(PplProductEnum.ES.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.DLC.getName()).test(item)) {
                    result.get(PplProductEnum.DLC.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.CSIG.getName()).test(item)) {
                    result.get(PplProductEnum.CSIG.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.IEGG.getName()).test(item)) {
                    result.get(PplProductEnum.IEGG.getName()).add(item);
                } else if (orderPredicateMap.get(PplProductEnum.TCHouse.getName()).test(item)) {
                    result.get(PplProductEnum.TCHouse.getName()).add(item);
                } else {
                    result.get(PplProductEnum.CVM_CBS.getName()).add(item);
                }
            } else if (StringUtils.equals(item.getOrderCategory(), ProductCategoryEnum.DB.getCode())) {
                result.get(ProductCategoryEnum.DB.getName()).add(item);
            }

        }
        return result;
    }

    private Map<String, Predicate<CrpPplOrderItemAndInfoVO>> getOrderPaasProductPredicate() {
        Map<String, Predicate<CrpPplOrderItemAndInfoVO>> result = new HashMap<>();

        Map<String, List<String>> paasProductUinMap = dictService.getPaasProductUinMapping()
                .stream().collect(Collectors.groupingBy(PaasProductUinWhiteListDO::getProduct, Collectors.mapping(PaasProductUinWhiteListDO::getUin, Collectors.toList())));

        //弹性MapReduce（EMR）
        Predicate<CrpPplOrderItemAndInfoVO> emr = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                StringUtils.equals(item.getAppRole(), YunxiaoAppRoleEnum.EMR.getCode());
        result.put(PplProductEnum.EMR.getName(), emr);

        //EKS官网
        Predicate<CrpPplOrderItemAndInfoVO> eks = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                StringUtils.equals(item.getAppRole(), YunxiaoAppRoleEnum.EKS.getCode());
        result.put(PplProductEnum.EKS.getName(), eks);

        //Elasticsearch Service
        Predicate<CrpPplOrderItemAndInfoVO> es = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                paasProductUinMap.getOrDefault(PplProductEnum.ES.getName(), ListUtils.newArrayList()).contains(item.getCustomerUin());
        result.put(PplProductEnum.ES.getName(), es);

        //云数据仓库
        Predicate<CrpPplOrderItemAndInfoVO> tCHouse = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                paasProductUinMap.getOrDefault(PplProductEnum.TCHouse.getName(), ListUtils.newArrayList()).contains(item.getCustomerUin());
        result.put(PplProductEnum.TCHouse.getName(), tCHouse);

        //数据湖 DLC
        Predicate<CrpPplOrderItemAndInfoVO> dLC = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                paasProductUinMap.getOrDefault(PplProductEnum.DLC.getName(), ListUtils.newArrayList()).contains(item.getCustomerUin());
        result.put(PplProductEnum.DLC.getName(), dLC);

        //CSIG容器平台
        Predicate<CrpPplOrderItemAndInfoVO> csig = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                paasProductUinMap.getOrDefault(PplProductEnum.CSIG.getName(), ListUtils.newArrayList()).contains(item.getCustomerUin());
        result.put(PplProductEnum.CSIG.getName(), csig);

        //IEGG
        Predicate<CrpPplOrderItemAndInfoVO> iegg = (item) -> StringUtils.equals(item.getOrderCategory(), OrderConstant.ORDER_CATEGORY_CVM) &&
                StringUtils.equals(item.getAppRole(), YunxiaoAppRoleEnum.CVM.getCode()) &&
                StringUtils.equals(item.getIndustryDept(), "港澳台及国际业务部") && item.getUinType() == 0;
        result.put(PplProductEnum.IEGG.getName(), iegg);

        return result;
    }
}
