package cloud.demand.lab.modules.order.service.filler.handler;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.order.service.filler.IsNewInstanceTypeFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class IsNewInstanceTypeFillerHandler implements FillerHandler<IsNewInstanceTypeFiller> {

    @Resource
    private DictService dictService;

    @Override
    public void fill(List<IsNewInstanceTypeFiller> obj) {
        List<String> newList = dictService.getNewGenerationInstanceType();
        obj.forEach(item -> {
            item.fillIsNewInstanceType(newList.contains(item.provideInstanceType()));
        });
    }


}
