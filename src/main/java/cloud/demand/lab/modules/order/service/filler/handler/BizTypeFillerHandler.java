package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.PplProductEnum;
import cloud.demand.lab.modules.order.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import cloud.demand.lab.modules.order.service.PerformanceTrackService;
import cloud.demand.lab.modules.order.service.filler.BizTypeFiller;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

@Service
public class BizTypeFillerHandler implements FillerHandler<BizTypeFiller> {

    @Override
    public int getExecOrder() {
        return FillerHandler.bizTypeFillerHandlerOrder;
    }

    @Override
    public void fill(List<BizTypeFiller> obj) {
        for (BizTypeFiller filler : obj) {
            if(StringUtils.equals(filler.providePaasProduct(), PplProductEnum.CVM_CBS.getName())){
                filler.fillBizType("外部");
            }else {
                filler.fillBizType("内部");
            }
        }
    }
}
