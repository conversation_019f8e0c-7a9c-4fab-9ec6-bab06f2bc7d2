package cloud.demand.lab.modules.order.job;

import cloud.demand.lab.common.utils.AlarmRobotUtil;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import cloud.demand.lab.modules.order.service.PerformanceTrackService;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import task.run.exporter.anno.TaskRunLog;

import javax.annotation.Resource;

@Slf4j
@Service
public class OrderRelevantTask {

    @Resource
    private PerformanceTrackService performanceTrackService;


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 8 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf1_new_instanceGroup_countryName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf1() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceGroup", "countryName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }

    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 9 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf2_new_instanceGroup_regionName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf2() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceGroup", "regionName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 9 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf3_new_instanceGroup_zoneName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf3() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceGroup", "zoneName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 10 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf4_new_instanceType_countryName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf4() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceType", "countryName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 10 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf5_new_instanceType_regionName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf5() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceType", "regionName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 11 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf6_new_instanceType_zoneName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf6() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.NEW.getName(), "instanceType", "zoneName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }


    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 11 * * * ")
    @TaskRunLog(namespace = "MONTH_PERFORMANCE_TRACK", nameScript = "'initMonthPerformanceTrackDf8_elastic_instanceType_regionName'", keyScript = "java.time.LocalDate.now().minusDays(1)")
    public void initMonthPerformanceTrackDf8() throws InterruptedException {
        Thread.sleep(1000);
        try {
            performanceTrackService.initMonthPerformanceTrackDf(OrderTypeEnum.ELASTIC.getName(), "instanceType", "zoneName");
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("refreshDemandFollow", e.getMessage(), null, false);
            throw e;
        }
    }

}
