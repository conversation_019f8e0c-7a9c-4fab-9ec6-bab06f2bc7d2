package cloud.demand.lab.modules.order.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/5/13 21:48
 */
@Data
public class OrderGridDetailVO {

    @Column(value = "stat_time")
    private String statTime;

    private String week;

    @Column(value = "order_number")
    private String orderNumber;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "instance_type")
    private String InstanceType;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "buy_core")
    private BigDecimal buyCore;

    @Column(value = "satisfy_core")
    private Long satisfyCore;
}
