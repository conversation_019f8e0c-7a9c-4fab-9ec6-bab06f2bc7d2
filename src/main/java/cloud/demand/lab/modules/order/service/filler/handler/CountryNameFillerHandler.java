package cloud.demand.lab.modules.order.service.filler.handler;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import cloud.demand.lab.modules.order.service.filler.core.FillerHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CountryNameFillerHandler implements FillerHandler<CountryNameFiller> {

    @Override
    public int getExecOrder() {
        return FillerHandler.countryNameFillerHandlerOrder;
    }

    @Resource
    private DictService dictService;

    @Override
    public void fill(List<CountryNameFiller> obj) {
        Map<String, String> region2CountryMap = dictService.getRegion2CountryMapping();
        Map<String, String> regionName2CountryMap = dictService.getRegionName2CountryMapping();
        for (CountryNameFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            if(StringUtils.isNotBlank(filler.provideRegion())){
                String countryName = region2CountryMap.get(filler.provideRegion());
                if (StringUtils.isNotBlank(countryName)) {
                    filler.fillCountryName(countryName);
                    continue;
                }
            }
            if(StringUtils.isNotBlank(filler.provideRegionName())){
                String countryName = regionName2CountryMap.get(filler.provideRegionName());
                if (StringUtils.isNotBlank(countryName)) {
                    filler.fillCountryName(countryName);
                    continue;
                }
            }
            filler.fillCountryName(Constant.EMPTY_VALUE_STR);
        }
    }
}
