package cloud.demand.lab.modules.order.dto;

import cloud.demand.lab.modules.order.auth.PerformanceTrackAuthCheckParam;
import cloud.demand.lab.modules.order.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import yunti.boot.exception.BizException;

import javax.validation.constraints.NotNull;

@Data
public class GroupOrderReq {


    /**
     * 剔除未到截止时间的订单
     */
    private Boolean filterNotEndOrder = Boolean.FALSE;

    private List<String> orderCategory;

    @PerformanceTrackAuthCheckParam(authField = "product")
    private List<String> product;

    /**
     * 业务类型：内部、外部
     */
    private List<String> bizType;

    /**
     * paas产品
     */
    private List<String> paasProduct;

    private List<String> orderType;

    private List<String> elasticType;

    @PerformanceTrackAuthCheckParam(authField = "industry")
    private List<String> industryDept;

    private List<String> warZone;


    private List<String> appId;


    private List<String> commonCustomerName;

    private List<String> countryName;

    private List<String> regionName;

    private List<String> zoneName;

    private List<String> customhouseTitle;

    private List<String> billTypeName;

    private List<String> orderNodeCode;

    private List<String> instanceType = new ArrayList<>();

    private List<String> instanceGroup = new ArrayList<>();

    /**
     * 是否新机型
     */
    private Boolean isNewInstanceType;

    private String startYearMonth;

    private String endYearMonth;

    @NotNull
    private List<String> dims = ListUtils.newArrayList();

    public static PerformanceTrackReq transform(GroupOrderReq req, List<String> orderNumberJson) {
        PerformanceTrackReq ret = new PerformanceTrackReq();
        BeanUtils.copyProperties(req, ret);
        return ret;
    }

}
