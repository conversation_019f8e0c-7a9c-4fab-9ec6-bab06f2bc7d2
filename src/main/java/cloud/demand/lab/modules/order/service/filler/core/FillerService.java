package cloud.demand.lab.modules.order.service.filler.core;

import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 *  数据填充适配器，用于数据填充与业务代码的解藕，自动适配到各个具体的{@link FillerHandler} 进行数据填充
 * @see FillerHandler
 */
@Component
public class FillerService implements InitializingBean {

    @Resource
    private List<FillerHandler<?>> fillerHandlers;

    private static final ExecutorService executor = Executors.newFixedThreadPool(8);

    private final Map<Class<?>, FillerHandler<?>> filerHandlerCache = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        if (ListUtils.isEmpty(fillerHandlers)) {
            return;
        }
        // bean初始化完成后，扫描所有的FillerHandler，注册进来
        for (FillerHandler<?> fillerHandler : fillerHandlers) {
            filerHandlerCache.put(fillerHandler.getSupportFiller(), fillerHandler);
        }
    }


    /**
     *  数据填充，适配到各个{@link FillerHandler}
     * @param list 待填充的数据
     */
    public void fill(List<?> list) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        Object filler = null;
        for (Object item : list) {
            if (item != null) {
                filler = item;
                break;
            }
        }
        if (filler == null) {
            return;
        }
        // 提倡面向接口编程，暂时只考虑基于接口的FillerHandler范型，不考虑FillerHandler范型为实体类的情况
        // 若存在FillerHandler范型为实体类的情况，那应该是业务强相关，不能抽离到FillerHandler模式中来
        Class<?>[] interfaces = filler.getClass().getInterfaces();
        List<FillerHandler<?>> handlerList = new ArrayList<>();
        for (Class<?> anInterface : interfaces) {
            // 获取指定的FillerHandler，进行数据填充
            FillerHandler<?> fillerHandler = filerHandlerCache.get(anInterface);
            if (fillerHandler != null) {
                handlerList.add(fillerHandler);
            }
        }
        // 顺序号值越小越先执行
        handlerList.stream().sorted(Comparator.comparing(FillerHandler::getExecOrder))
                .forEach(fillerHandler -> fillerHandler.fill((List)list));
    }

    /**
     *  异步数据填充，适配到各个{@link FillerHandler}，暂时不会考虑执行顺序
     * @param list 待填充的数据
     * @param excludeFillerInterface 需要排除的filler 接口
     */
    public void fillAsyncAndExcludeSomeFiller(List<?> list, Class<?>... excludeFillerInterface) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        Object filler = null;
        for (Object item : list) {
            if (item != null) {
                filler = item;
                break;
            }
        }
        if (filler == null) {
            return;
        }
        List<Class<?>> excludeFillerInterfaceList = new ArrayList<>();
        if (excludeFillerInterface != null) {
            excludeFillerInterfaceList.addAll(Arrays.asList(excludeFillerInterface));
        }
        // 提倡面向接口编程，暂时只考虑基于接口的FillerHandler范型，不考虑FillerHandler范型为实体类的情况
        // 若存在FillerHandler范型为实体类的情况，那应该是业务强相关，不能抽离到FillerHandler模式中来
        Class<?>[] interfaces = filler.getClass().getInterfaces();
        List<FillerHandler<?>> handlerList = new ArrayList<>();
        for (Class<?> anInterface : interfaces) {
            if (excludeFillerInterfaceList.contains(anInterface)) {
                // 排除
                continue;
            }
            // 获取指定的FillerHandler，进行数据填充
            FillerHandler<?> fillerHandler = filerHandlerCache.get(anInterface);
            if (fillerHandler != null) {
                handlerList.add(fillerHandler);
            }
        }

        CountDownLatch latch = new CountDownLatch(handlerList.size());
        for (FillerHandler<?> handler : handlerList) {
            executor.submit(() -> doFillAsync(latch, handler, list));
        }

        try {
            latch.await();
        } catch (Exception e) {
            // non
        }
    }

    private void doFillAsync(CountDownLatch latch, FillerHandler<?> fillerHandler, List<?> datas) {
        try {
            fillerHandler.fill((List)datas);
        } finally {
            latch.countDown();
        }
    }

    /**
     *  数据填充，适配到各个{@link FillerHandler}
     * @param list 待填充的数据
     * @param fillerInterface 需要填充的filler接口，例如{@link cloud.demand.app.modules.p2p.ppl13week.service.filler.CustomerInfoFiller}
     */
    public void fill(List<?> list, Class<?> fillerInterface) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        Object filler = null;
        for (Object item : list) {
            if (item != null) {
                filler = item;
                break;
            }
        }
        if (filler == null) {
            return;
        }
        // 提倡面向接口编程，暂时只考虑基于接口的FillerHandler范型，不考虑FillerHandler范型为实体类的情况
        // 若存在FillerHandler范型为实体类的情况，那应该是业务强相关，不能抽离到FillerHandler模式中来
        Class<?>[] interfaces = filler.getClass().getInterfaces();
        List<FillerHandler<?>> handlerList = new ArrayList<>();
        for (Class<?> anInterface : interfaces) {
            if (!anInterface.equals(fillerInterface)) {
                continue;
            }
            // 获取指定的FillerHandler，进行数据填充
            FillerHandler<?> fillerHandler = filerHandlerCache.get(anInterface);
            if (fillerHandler != null) {
                handlerList.add(fillerHandler);
            }
        }
        // 顺序号值越小越先执行
        handlerList.stream().sorted(Comparator.comparing(FillerHandler::getExecOrder))
                .forEach(fillerHandler -> fillerHandler.fill((List)list));
    }

}
