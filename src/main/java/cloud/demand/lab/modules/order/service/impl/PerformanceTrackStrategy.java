package cloud.demand.lab.modules.order.service.impl;

import cloud.demand.lab.modules.order.dto.CrpPplOrderItemAndInfoVO;
import cloud.demand.lab.modules.order.dto.FillTrackDfReq;
import cloud.demand.lab.modules.order.entity.std_table.AwsMonthPerformanceTrackDfDO;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import cloud.demand.lab.modules.order.service.TrackService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/4/24 18:37
 */
@Service
public class PerformanceTrackStrategy implements InitializingBean {
    @Resource
    private List<TrackService> trackServices;

    private final Map<String, TrackService> trackServiceMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        if (ListUtils.isEmpty(trackServices)) {
            return;
        }
        // bean初始化完成后，扫描所有的demandExcelServices，注册进来
        for (TrackService service : trackServices) {
            trackServiceMap.put(service.getKey(), service);
        }
    }

    public void fillTrackData(String yearMonth, AwsMonthPerformanceTrackDfDO trackDfDO,
                              List<CrpPplOrderItemAndInfoVO> currentMonthData, List<CrpPplOrderItemAndInfoVO> lastMonthData) {
        String key = getKeyByTrackDf(trackDfDO) ;
        TrackService service = getService(key);

        FillTrackDfReq fillTrackDfReq = new FillTrackDfReq();
        fillTrackDfReq.setDataYearMonth(yearMonth);
        fillTrackDfReq.setCurrentMonthData(currentMonthData);
        fillTrackDfReq.setLastMonthData(lastMonthData);
        service.fillTrackData(trackDfDO, fillTrackDfReq);
    }

    public Long getSatisfyCore(String yearMonth, CrpPplOrderItemAndInfoVO orderItem) {
        String key = getKeyByOrderItem(orderItem);

        TrackService service = getService(key);
        return service.getMonthSatisfyCore(yearMonth,ListUtils.newArrayList(orderItem));
    }

    public BigDecimal getBuyCore(String yearMonth, CrpPplOrderItemAndInfoVO orderItem) {
        String key = getKeyByOrderItem(orderItem);
        TrackService service = getService(key);
        return service.getBuyCore(yearMonth,null, ListUtils.newArrayList(orderItem));
    }

    private TrackService getService(String key) {
        TrackService service = trackServiceMap.get(key);
        if (Objects.isNull(service)) {
            throw new BizException("未找到对应的Key:" + key + "的服务");
        }
        return service;
    }

    private String getKeyByTrackDf(AwsMonthPerformanceTrackDfDO trackDfDO) {
        String key;
        if (StringUtils.equals(trackDfDO.getOrderType(), OrderTypeEnum.NEW.getName())) {
            key = StringUtils.joinWith("@", trackDfDO.getOrderType());
        } else {
            key = StringUtils.joinWith("@", trackDfDO.getOrderType(), trackDfDO.getElasticType());
        }
        return key;
    }

    private String getKeyByOrderItem(CrpPplOrderItemAndInfoVO orderItem) {
        String key;
        if (StringUtils.equals(orderItem.getOrderType(), OrderTypeEnum.NEW.getCode())) {
            key = StringUtils.joinWith("@", OrderTypeEnum.getNameByCode(orderItem.getOrderType()));
        } else {
            key = StringUtils.joinWith("@", OrderTypeEnum.getNameByCode(orderItem.getOrderType()), orderItem.getElasticType());
        }
        return key;
    }
}
