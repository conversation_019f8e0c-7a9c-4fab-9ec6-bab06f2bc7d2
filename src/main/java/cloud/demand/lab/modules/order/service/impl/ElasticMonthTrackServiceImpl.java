package cloud.demand.lab.modules.order.service.impl;

import cloud.demand.lab.modules.order.dto.CrpPplOrderItemAndInfoVO;
import cloud.demand.lab.modules.order.dto.FillTrackDfReq;
import cloud.demand.lab.modules.order.dto.OrderGridDetailReq;
import cloud.demand.lab.modules.order.dto.OrderGridDetailVO;
import cloud.demand.lab.modules.order.entity.std_table.AwsMonthPerformanceTrackDfDO;
import cloud.demand.lab.modules.order.enums.OrderElasticType;
import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import cloud.demand.lab.modules.order.service.OrderGridDetailService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/13 22:30
 */
@Service
public class ElasticMonthTrackServiceImpl extends AbstractTrackService {
    @Resource
    private OrderGridDetailService orderGridDetailService;

    @Override
    public void fillAllOrderCrossMonthTotalCore(AwsMonthPerformanceTrackDfDO trackDfDO, FillTrackDfReq trackDfReq) {
        trackDfDO.setAllOrderCrossMonthTotalAmount(0L);
    }

    @Override
    public void fillCrossMonthTotalCore(AwsMonthPerformanceTrackDfDO trackDfDO, FillTrackDfReq trackDfReq) {
        trackDfDO.setCrossMonthTotalAmount(0L);
    }

    @Override
    public void fillFinishOrderCrossMonthDemandTotalCore(AwsMonthPerformanceTrackDfDO trackDfDO, FillTrackDfReq trackDfReq) {
        trackDfDO.setFinishOrderCrossMonthDemandTotalAmount(0L);
    }

    @Override
    public void fillCrossMonthSatisfy(AwsMonthPerformanceTrackDfDO trackDfDO, FillTrackDfReq trackDfReq) {
        trackDfDO.setCrossMonthSatisfyAmount(0L);
        trackDfDO.setFinishOrderSatisfyAmount(0L);
    }

    @Override
    public Long getMonthSatisfyCore(String yearMonth, List<CrpPplOrderItemAndInfoVO> orderItemList) {
        OrderGridDetailReq req = OrderGridDetailReq.build(yearMonth, orderItemList);

        List<OrderGridDetailVO> details = orderGridDetailService.queryOrderGridDetailList(req);
        // 计算平均值（保留整数部分）
        if (ListUtils.isEmpty(details)) {
            return 0L;
        }
        Long satisfyCore = details.stream().map(OrderGridDetailVO::getSatisfyCore).max(Long::compareTo).orElse(0L);
        return Math.min(satisfyCore, getTotalCore(orderItemList));
    }

    @Override
    public Long getFinishedOrderSatisfyCore(String yearMonth, List<CrpPplOrderItemAndInfoVO> orderItemList, LocalDate limitDate) {
        OrderGridDetailReq req = OrderGridDetailReq.build(yearMonth, orderItemList);

        List<OrderGridDetailVO> details = orderGridDetailService.queryOrderGridDetailList(req);
        // 计算平均值（保留整数部分）
        if (ListUtils.isEmpty(details)) {
            return 0L;
        }
        List<CrpPplOrderItemAndInfoVO> finishOrderItemList = orderItemList.stream().filter(o -> o.getEndBuyDate().isBefore(limitDate)).collect(Collectors.toList());
        List<String> finishOrderNumberList = finishOrderItemList.stream()
                .map(CrpPplOrderItemAndInfoVO::getOrderNumber)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(finishOrderNumberList)) {
            return 0L;
        }
        List<OrderGridDetailVO> finishDetails = details.stream().filter(o -> finishOrderNumberList.contains(o.getOrderNumber())).collect(Collectors.toList());
        if (ListUtils.isEmpty(finishDetails)) {
            return 0L;
        }
        Long satisfyCore = finishDetails.stream().map(OrderGridDetailVO::getSatisfyCore).max(Long::compareTo).orElse(0L);
        return Math.min(satisfyCore, getTotalCore(finishOrderItemList));
    }

    @Override
    public BigDecimal getBuyCore(String yearMonth, AwsMonthPerformanceTrackDfDO trackDfDO, List<CrpPplOrderItemAndInfoVO> orderItemList) {
        OrderGridDetailReq req = OrderGridDetailReq.build(yearMonth, orderItemList);

        List<OrderGridDetailVO> details = orderGridDetailService.queryOrderGridDetailList(req);
        // 计算平均值（保留整数部分）
        if (ListUtils.isEmpty(details)) {
            return BigDecimal.ZERO;
        }
        BigDecimal buyCore = details.stream().map(OrderGridDetailVO::getBuyCore).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        return new BigDecimal(Math.min(buyCore.longValue(), getTotalCore(orderItemList)));
    }

    @Override
    public String getKey() {
        return StringUtils.joinWith("@", OrderTypeEnum.ELASTIC.getName(), OrderElasticType.BY_MONTH.getTypeName());
    }
}
