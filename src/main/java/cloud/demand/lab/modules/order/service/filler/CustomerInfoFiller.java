package cloud.demand.lab.modules.order.service.filler;

/**
 * ppl的客户的相关信息字段的接口定义，用于统一的参数填充<br/>
 * 实现此接口需要在实现类中定义以下的字段<br/>
 * <blockquote><pre>
 *     // 以下是进行数据填充的需要的条件字段
 *
 *     private String customerUin; // customer_uin
 *
 *
 *     // 以下是待进行数据填充的字段
 *
 *     private String cId; // 客户集团id
 *     private String cName; // 客户集团
 *     private String panShiWarZone; // 磐石战区
 *     private Integer uinType;  // uin类型
 *     private Integer customerSocietyType;  // 客户类型，0个人 1企业
 *
 * </pre></blockquote><p/>
 * 填充实现：{@link cloud.demand.app.modules.p2p.ppl13week.service.filler.handler.CustomerInfoFillerHandler}
 */
public interface CustomerInfoFiller {

    /** customer_uin */
    String provideCustomerUin();


    /** 客户集团id */
    void fillCId(String cId);

    /** 客户集团 */
    void fillCName(String cName);

    /** 磐石战区 */
    void fillPanShiWarZone(String panShiWarZone);

    /** uin类型 */
    void fillUinType(Integer uinType);

    /** 客户类型(从社会角度上看属于哪一类)，0个人 1企业 */
    void fillCustomerSocietyType(Integer customerSocietyType);

    /** 客户简称 */
    default void fillCustomerShortName(String customerShortName) {
        // non
    }

}
