package cloud.demand.lab.modules.order.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PerformanceTrackSummaryResp {

    private MonthTarget lastMonth;

    private MonthTarget lastLastMonth;


    private List<MonthTarget> data;

    @Data
    public static class MonthTarget {

        String yearMonth;

        Long demandTotalCore;

        Long satisfyCore;

        BigDecimal buyTotalCore;

        BigDecimal notBuyTotalCore;

        Long demandTotalAmount;

        Long satisfyAmount;

        BigDecimal buyTotalAmount;

        BigDecimal notBuyTotalAmount;


        String buyRate;

        public void setAmount(){
            this.demandTotalAmount = this.demandTotalCore;
            this.satisfyAmount = this.satisfyCore;
            this.buyTotalAmount = this.buyTotalCore;
            this.notBuyTotalAmount = this.notBuyTotalCore;
        }
    }
}
