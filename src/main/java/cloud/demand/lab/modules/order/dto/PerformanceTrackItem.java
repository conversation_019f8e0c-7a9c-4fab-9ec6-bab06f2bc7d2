package cloud.demand.lab.modules.order.dto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.function.Function;

import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.entity.std_table.AwsMonthPerformanceTrackDfDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PerformanceTrackItem {

    private String groupKey;

    /**
     * 需求年月<br/>Column: [year_month]
     */
    private String yearMonth;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    private String warZone;

    /**
     * 通用客户简称<br/>Column: [common_customer_name]
     */
    private String commonCustomerName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    private String customerShortName;

    /**
     * 产品<br/>Column: [product]
     */
    private String product;

    /**
     * 应用角色<br/>Column: [app_role]
     */
    private String appRole;

    /**
     * 机型族<br/>Column: [instance_type]
     */
    private String instanceType;

    /**
     * 机型族<br/>Column: [instance_group]
     */
    private String instanceGroup;

    /**
     * 是否新机型
     */
    private Boolean isNewInstanceType;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    private String customhouseTitle;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 地域<br/>Column: [region]
     */
    private String region;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    private String regionName;

    /**
     * 可用区<br/>Column: [zone]
     */
    private String zone;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    private String zoneName;

    private String bizType;

    private Integer uinType;

    private String orderType;

    private String elasticType;

    private String paasProduct;


    /**
     * 所有订单（包含满足方式评估、交付供应）-需求总核心数<br/>Column: [all_order_demand_total_Core]
     */
    private Long allOrderDemandTotalCore;

    private Long allOrderDemandTotalAmount;

    /**
     * 需求总核心数<br/>Column: [demand_total_core]
     */
    private Long demandTotalCore;

    private Long demandTotalAmount;

    /**
     * 本月共识核心数
     */
    private Long currentMonthTotalCore = 0L;

    private Long currentMonthTotalAmount = 0L;

    /**
     * 跨月共识核心数
     */
    private Long crossMonthTotalCore = 0L;

    private Long crossMonthTotalAmount = 0L;

    /**
     * 满足核心数<br/>Column: [satisfy_core]
     */
    private Long satisfyCore;

    private Long satisfyAmount;

    /**
     * 本月满足核心数
     */
    private Long currentMonthSatisfyCore = 0L;

    private Long currentMonthSatisfyAmount = 0L;

    /**
     * 跨月满足核心数
     */
    private Long crossMonthSatisfyCore = 0L;

    private Long crossMonthSatisfyAmount = 0L;

    /**
     * 实际购买核心<br/>Column: [buy_total_core]
     */
    private BigDecimal buyTotalCore = new BigDecimal(0);

    private BigDecimal buyTotalAmount = new BigDecimal(0);


    /**
     * 履约率
     */
    private BigDecimal buyRate;

    /**
     * 满足量占比
     */
    private BigDecimal satisfyCoreRate;

    /**
     * 满足量基层数据
     */
    private BigDecimal satisfyCoreBaseRate;

    /**
     * 未履约核心数
     */
    private BigDecimal notBuyTotalCore;

    private BigDecimal notBuyTotalAmount;

    private List<String> currentOrderNumberList;

    private List<String> crossOrderNumberList;


    private List<PerformanceTrackItem> childItem;

    public void setAmount() {
        this.buyTotalAmount = this.buyTotalCore;
        this.allOrderDemandTotalAmount = this.allOrderDemandTotalCore;
        this.demandTotalAmount = this.demandTotalCore;
        this.currentMonthTotalAmount = this.currentMonthTotalCore;
        this.crossMonthTotalAmount = this.crossMonthTotalCore;
        this.satisfyAmount = this.satisfyCore;
        this.currentMonthSatisfyAmount = this.currentMonthSatisfyCore;
        this.crossMonthSatisfyAmount = this.crossMonthSatisfyCore;
        this.notBuyTotalAmount = this.notBuyTotalCore;
    }

    public static PerformanceTrackItem transform(AwsMonthPerformanceTrackDfDO trackDfDO, boolean filterNotEndOrder, boolean isAllOrder) {
        PerformanceTrackItem item = new PerformanceTrackItem();
        item.setYearMonth(trackDfDO.getYearMonth());
        item.setIndustryDept(trackDfDO.getIndustryDept());
        item.setWarZone(trackDfDO.getWarZone());
        item.setCommonCustomerName(trackDfDO.getCommonCustomerName());
        item.setCustomerShortName(trackDfDO.getCustomerShortName());
        item.setProduct(trackDfDO.getProduct());
        item.setAppRole(trackDfDO.getAppRole());
        item.setInstanceType(trackDfDO.getInstanceType());
        item.setInstanceGroup(trackDfDO.getInstanceGroup());
        item.setIsNewInstanceType(trackDfDO.getIsNewInstanceType());
        item.setCustomhouseTitle(trackDfDO.getCustomhouseTitle());
        item.setCountryName(trackDfDO.getCountryName());
        item.setRegion(trackDfDO.getRegion());
        item.setRegionName(trackDfDO.getRegionName());
        item.setZone(trackDfDO.getZone());
        item.setZoneName(trackDfDO.getZoneName());
        item.setBizType(trackDfDO.getBizType());
        item.setUinType(trackDfDO.getUinType());
        item.setOrderType(trackDfDO.getOrderType());
        item.setElasticType(trackDfDO.getElasticType());
        item.setPaasProduct(trackDfDO.getPaasProduct());
        item.setAllOrderDemandTotalCore(getDemandTotalAmount(filterNotEndOrder, isAllOrder,null).apply(trackDfDO));
        item.setDemandTotalCore(getDemandTotalAmount(filterNotEndOrder, isAllOrder,null).apply(trackDfDO));
        item.setCurrentMonthTotalCore(getDemandTotalAmount(filterNotEndOrder, isAllOrder,"currentMonth").apply(trackDfDO));
        item.setCrossMonthTotalCore(getDemandTotalAmount(filterNotEndOrder, isAllOrder,"crossMonth").apply(trackDfDO));
        item.setSatisfyCore(getSatisfyAmount(filterNotEndOrder, null).apply(trackDfDO));
        item.setCurrentMonthSatisfyCore(getSatisfyAmount(filterNotEndOrder, "currentMonth").apply(trackDfDO));
        item.setCrossMonthSatisfyCore(getSatisfyAmount(filterNotEndOrder, "crossMonth").apply(trackDfDO));
        item.setBuyTotalCore(trackDfDO.getBuyTotalAmount());
        if(isAllOrder){
            // 所有订单的条件下 未购买 = 需求-已购买
            item.setNotBuyTotalCore(SoeCommonUtils.sub(BigDecimal.valueOf(item.getDemandTotalCore()), item.getBuyTotalCore()).max(BigDecimal.ZERO));
        }else {
            item.setNotBuyTotalCore(SoeCommonUtils.sub(BigDecimal.valueOf(item.getSatisfyCore()), item.getBuyTotalCore()).max(BigDecimal.ZERO));
        }
        item.setBuyRate( SoeCommonUtils.divide(item.getBuyTotalCore(), BigDecimal.valueOf(item.getSatisfyCore())));
        item.setCurrentOrderNumberList(trackDfDO.getCurrentOrderNumberList());
        item.setCrossOrderNumberList(trackDfDO.getCrossOrderNumberList());
        return item;
    }

    /**
     * if type=currentMonth 统计本月 ,if type = crossMonth 统计跨月 else 统计本月+跨月
     *
     * @param filterNotEndOrder
     * @param isAllOrder
     * @param type
     * @return
     */
    public static Function<AwsMonthPerformanceTrackDfDO, Long> getDemandTotalAmount(boolean filterNotEndOrder, boolean isAllOrder, String type) {
        if (filterNotEndOrder) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCurrentMonthDemandTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCrossMonthDemandTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderDemandTotalAmount;
            }
        } else if (isAllOrder) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getAllOrderCurrentMonthTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getAllOrderCrossMonthTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getAllOrderDemandTotalAmount;
            }
        } else {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCurrentMonthTotalAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCrossMonthTotalAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getDemandTotalAmount;
            }
        }
    }

    /**
     * if type=currentMonth 统计本月 ,if type = crossMonth 统计跨月 else 统计本月+跨月
     *
     * @param filterNotEndOrder
     * @param type
     * @return
     */
    public static Function<AwsMonthPerformanceTrackDfDO, Long> getSatisfyAmount(boolean filterNotEndOrder, String type) {
        if (filterNotEndOrder) {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCurrentMonthSatisfyAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderCrossMonthSatisfyAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getFinishOrderSatisfyAmount;
            }
        } else {
            if (StringUtils.equals(type, "currentMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCurrentMonthSatisfyAmount;
            } else if (StringUtils.equals(type, "crossMonth")) {
                return AwsMonthPerformanceTrackDfDO::getCrossMonthSatisfyAmount;
            } else {
                return AwsMonthPerformanceTrackDfDO::getSatisfyAmount;
            }
        }
    }
}
