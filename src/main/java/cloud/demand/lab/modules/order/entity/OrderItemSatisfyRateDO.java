package cloud.demand.lab.modules.order.entity;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

/**
 * 不同满足方式下的订单明细满足度信息
 */
@Data
@Table("order_item_satisfy_rate")
public class OrderItemSatisfyRateDO extends BaseDO implements InstanceGroupFiller, CountryNameFiller,IOrderItemSatisfyGroupKey {

    @Column("order_number")
    private String orderNumber;

    @Column("order_number_id")
    private String orderNumberId;

    private String instanceGroup;

    @Column("instance_type")
    private String instanceType;

    @Column("instance_model")
    private String instanceModel;

    @Column("zone_name")
    private String zoneName;

    @Column("zone")
    private String zone;

    private String  countryName;

    /**
     * 地域code<br/>Column: [region]
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 战区
     * Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 产品
     * Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * APPID<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 单据类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    @Column("match_type")
    private String matchType;

    @Column("match_type_name")
    private String matchTypeName;

    @Column("consensus_core")
    private BigDecimal consensusCore;

    // 参与对冲的需求核心数
    @Column("wait_stock_core")
    private BigDecimal waitStockCore;

    // 已实际预扣核心数
    @Column("pre_deduct_core")
    private BigDecimal preDeductCore;

    // 已履约核心数
    @Column("keep_promise_core")
    private BigDecimal keepPromiseCore;

    // 参与对冲部分的满足核心数
    @Column("match_core")
    private BigDecimal matchCore;

    // 按规则计算总体数据分摊到此维度的，总体的满足核心数（包含了已履约、已预扣的认为满足）
    @Column("total_system_match_core")
    private BigDecimal totalSystemMatchCore;

    // 最终总体的满足核心数（供应方案明细满足度修改后的）
    @Column("total_match_core")
    private BigDecimal totalMatchCore;

    /**
     * 当前计算满足度时的订单开始购买日期
     */
    @Column("begin_buy_date")
    private LocalDate beginBuyDate;

    @Column("calc_date")
    private LocalDate calcDate;

    /**
     * 是否主要有效的满足度结果
     */
    @Column("main_result")
    private Boolean mainResult;

    /**
     * 第一次标记为主要有效的满足度结果时的开始购买日期
     */
    @Column("first_tag_begin_buy_date")
    private LocalDate firstTagBeginBuyDate;

    @Override
    public String provideRegion() {
        return this.region;
    }

    @Override
    public String provideRegionName() {
        return null;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
    }
}
