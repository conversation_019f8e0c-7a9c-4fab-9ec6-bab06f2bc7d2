package cloud.demand.lab.modules.order.parsers;

import cloud.demand.lab.modules.order.enums.OrderTypeEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang.StringUtils;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder.WhereInfo;

import java.util.ArrayList;
import java.util.List;
/**
 * 订单类型解析器
 */
public class OrderTypeParseWhere implements IWhereParser {

    @Override
    public void parseSQL(WhereSQL content, WhereInfo whereInfo, Object t) {
        Object v = whereInfo.getV();
        List<String> ls = (List<String>) v;
        if (ListUtils.isNotEmpty(ls)) {
            List<String> orderTypeCode = new ArrayList<>();
            String column = whereInfo.getParseParams()[0]; // 地域字段
            ls.forEach(item -> {
                String code = OrderTypeEnum.getCodeByName(item);
                if(StringUtils.isNotEmpty(code)){
                    orderTypeCode.add(code);
                }
            });
            content.andIf(ListUtils.isNotEmpty(orderTypeCode), column + " in (?)", orderTypeCode);
        }
    }
}
