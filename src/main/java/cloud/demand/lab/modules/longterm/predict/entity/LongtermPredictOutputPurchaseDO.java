package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("longterm_predict_output_purchase")
public class LongtermPredictOutputPurchaseDO extends BaseDO {

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "input_args_id")
    private Long inputArgsId;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "date_name")
    private String dateName;

    @Column(value = "start_date")
    private LocalDate startDate;

    @Column(value = "end_date")
    private LocalDate endDate;

    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    /** 国家名称<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    public  LongtermPredictOutputPurchaseSplitIndustryDeptDO toIndustryDeptSplitDO() {
        LongtermPredictOutputPurchaseDO c = this;
        LongtermPredictOutputPurchaseSplitIndustryDeptDO target = new LongtermPredictOutputPurchaseSplitIndustryDeptDO();
        target.setOutputId(c.getId());
        target.setTaskId(c.getTaskId());
        target.setStrategyType(c.getStrategyType());
        target.setInputArgsId(c.getInputArgsId());
        target.setStartDate(c.getStartDate());
        target.setEndDate(c.getEndDate());
        target.setYear(c.getStartDate().getYear());
        target.setPurchaseCore(c.getPurchaseCore());
        target.setCountryName(c.getCountryName());
        target.setRegionName(c.getRegionName());
        return target;
    }

    public  LongtermPredictOutputPurchaseSplitDO toDeviceTypeSplitDO() {
        LongtermPredictOutputPurchaseDO c = this;
        LongtermPredictOutputPurchaseSplitDO target = new LongtermPredictOutputPurchaseSplitDO();
        target.setOutputId(c.getId());
        target.setTaskId(c.getTaskId());
        target.setStrategyType(c.getStrategyType());
        target.setInputArgsId(c.getInputArgsId());
        target.setStartDate(c.getStartDate());
        target.setEndDate(c.getEndDate());
        target.setYear(c.getStartDate().getYear());
        target.setPurchaseCore(c.getPurchaseCore());
        target.setRegionName(c.getRegionName());
        target.setCountryName(c.getCountryName());
        return target;
    }


    public static DBHelperWithClz<LongtermPredictOutputPurchaseDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

}