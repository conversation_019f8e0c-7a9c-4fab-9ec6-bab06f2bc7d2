package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("longterm_predict_input_purchase")
public class LongtermPredictInputPurchaseDO extends BaseDO {


    public static DBHelperWithClz<LongtermPredictInputPurchaseDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    /** 预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    /** 采购的物理机的campus<br/>Column: [campus] */
    @Column(value = "campus")
    private String campus;

    /** CVM的可用区，广州三区，由campus或module关联出来<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** CVM的region<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** CVM的国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 由物理机转换成CVM实例大类<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** CVM实例族<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 采购到货量（交付月份），这个采购量也是模型公式中用到的采购量<br/>Column: [purchase_core] */
    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    /** 采购到货量（需求月份）<br/>Column: [purchase_core_by_demand_month_delivered] */
    @Column(value = "purchase_core_by_demand_month_delivered")
    private BigDecimal purchaseCoreByDemandMonthDelivered;

    /** 采购下单量<br/>Column: [purchase_core_by_demand_month_total] */
    @Column(value = "purchase_core_by_demand_month_total")
    private BigDecimal purchaseCoreByDemandMonthTotal;

}