package cloud.demand.lab.modules.longterm.cos.web.req;

import lombok.Data;

/**
 * 查询大客户历史变动数据请求
 */
@Data
public class QueryBigCustomerHistoryChangeReq {

    public QueryBigCustomerHistoryChangeReq() {}

    public QueryBigCustomerHistoryChangeReq(Long categoryId, Long taskId) {
        this.categoryId = categoryId;
        this.taskId = taskId;
    }

    /**
     * 方案id，必须参数
     */
    private Long categoryId;

    /**
     * 任务id，可选参数
     * 当taskId是0或未传时，表示查询当前方案categoryId下的默认大客户历史变动信息
     */
    private Long taskId;
}
