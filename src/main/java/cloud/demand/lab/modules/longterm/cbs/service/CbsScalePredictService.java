package cloud.demand.lab.modules.longterm.cbs.service;


import cloud.demand.lab.modules.longterm.cbs.dto.CbsLongtermPredictScaleDTO;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.*;

import java.util.List;

/**
 * CBS中长期存量及其预测
 */
public interface CbsScalePredictService {
    /**
     * 查询存量净增规模
     */
    QueryCbsScalePredictTotalResp queryCbsScalePredictTotal(QueryCbsScalePredictTotalReq req);


    /**
     * 存量数据框字典
     */
    QueryCbsScalePredictDictResp queryCbsScalePredictDict(QueryCbsScalePredictDictReq req);

    /**
     * 存量历史趋势图
     * @param req
     * @return
     */
    QueryCbsScaleHistoryResp queryCbsScaleScaleHistory(QueryCbsScaleScaleHistoryReq req);

    /**
     * 存量预测
     * @param req
     * @return
     */
    QueryScalePredictResp queryCbsScalePredict(QueryCbsScalePredictReq req);
}
