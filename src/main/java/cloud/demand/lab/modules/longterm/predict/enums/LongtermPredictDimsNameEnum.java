package cloud.demand.lab.modules.longterm.predict.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
public enum LongtermPredictDimsNameEnum {

    TOTAL("全量"),
    REGION_OR_COUNTRY("地域/国家"),
    INSTANCE_FAMILY("机型大类"),
    DEVICE_TYPE("物理机机型"),
    CAMPUS("campus"),
    INDUSTRY_DEPT("行业部门"),
    ;
    final private String name;

    LongtermPredictDimsNameEnum(String name) {
        this.name = name;
    }

    public static List<String> getNames(){
        return Arrays.stream(LongtermPredictDimsNameEnum.values())
                .map(LongtermPredictDimsNameEnum::getName).collect(Collectors.toList());
    }
    public static LongtermPredictDimsNameEnum getByName(String name) {
        for (LongtermPredictDimsNameEnum value : LongtermPredictDimsNameEnum.values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }
    public static LongtermPredictDimsNameEnum getByCode(String code) {
        for (LongtermPredictDimsNameEnum value : LongtermPredictDimsNameEnum.values()) {
            if (Objects.equals(value.name(), code)) {
                return value;
            }
        }
        return null;
    }
}