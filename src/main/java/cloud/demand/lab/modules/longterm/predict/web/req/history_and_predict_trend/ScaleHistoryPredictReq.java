package cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend;

import lombok.Data;

import java.util.List;

/**
 * 添加预测部分请求体
 */
@Data
public class ScaleHistoryPredictReq {
    //任务id
    private Long taskId;
    // 格式 yyyy-mm
    private String startYearmonth;
    // 格式 yyyy-mm
    private String endYearmonth;
    //拆分版本id
    private Long splitVersionId;
    //境内外
    private List<String> customhouseTitle;
    //内外部客户
    private List<String> bizRangeType;
    //城市国家
    private List<String> regionOrCountry;
    //机型大类
    private List<String> instanceFamily;
    //聚合维度(全量为yearMonth，城市国家传 regionOrCountry,按机型大类传instanceType
    private String dims;
    //是否预览，做保留，默认关闭
    //private Boolean isPreview=false;
}
