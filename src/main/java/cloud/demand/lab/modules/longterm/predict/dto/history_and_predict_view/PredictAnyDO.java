package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class PredictAnyDO {
    @Column(value = "year_month_str")
    private String yearMonth;

    @Column(value = "cur_core")
    private BigDecimal curCore;

    @Column(value = "input_args_id")
    private String inputArgsId;

    @Column(value = "dim_name")
    private String dimName;
}
