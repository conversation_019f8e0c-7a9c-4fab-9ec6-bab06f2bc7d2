package cloud.demand.lab.modules.longterm.predict.web.resp.split;

import cloud.demand.lab.modules.longterm.predict.web.entity.InstanceToDeviceItem;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class InstanceToDeviceResp {

    List<InstanceToDeviceItem> excelData;
    List<InstanceToDeviceItem> webData;
    List<InstanceToDeviceItemDiff> diffData;

    List<String> warnings = new ArrayList<>();
    List<String> errors = new ArrayList<>();

    @Data
    public static class InstanceToDeviceItemDiff extends InstanceToDeviceItem{

        // INSERT UPDATE DELETE
        private String type;
        private String info;

        // 修改后的值
        BigDecimal rateAfterChange;
        public static void  setSuper(InstanceToDeviceItemDiff target, InstanceToDeviceItem sup){
            target.setKey(sup.getKey());
            target.setRate(sup.getRate());
        }
    }


}
