package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitMonthKeyGetter;
import java.time.YearMonth;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_config_month_rate")
public class LongtermPredictConfigMonthRateDO extends BaseDO
        implements SplitMonthKeyGetter {
    public static DBHelperWithClz<LongtermPredictConfigInstanceFamilyRateDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * 月<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    public YearMonth getYm(){
        return YearMonth.of(year, month);
    }

    /**
     * 占比<br/>Column: [rate]
     */
    @Column(value = "rate")
    private BigDecimal rate;

}