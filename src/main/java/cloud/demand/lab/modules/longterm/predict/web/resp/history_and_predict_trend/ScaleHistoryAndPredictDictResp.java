package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import lombok.Data;

import java.util.List;

/**
 * 字典查询返回体
 */
@Data
public class ScaleHistoryAndPredictDictResp {
    //境内外
    private List<String> customhouseTitle;
    //内外部客户
    private List<String> bizRangeType;
    //城市国家
    private List<String> regionOrCountry;
    //机型大类
    private List<String> instanceFamily;
    //数据起始时间
    private String startYearMonth;
    //数据结束时间
    private String endYearMonth;

    private String message;

    public ScaleHistoryAndPredictDictResp() {
    }

    public ScaleHistoryAndPredictDictResp(String message) {
        this.message = message;
    }
}
