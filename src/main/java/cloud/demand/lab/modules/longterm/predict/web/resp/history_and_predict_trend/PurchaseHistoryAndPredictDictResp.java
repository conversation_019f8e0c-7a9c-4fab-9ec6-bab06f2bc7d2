package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view.PurchaseHistoryAndPredictDictDTO;
import java.util.List;
import lombok.Data;

/**
 * 字典查询返回体
 */
@Data
public class PurchaseHistoryAndPredictDictResp {

    CvmDict cvmDict;
    DeviceTypeDict deviceTypeDict;
    IndustryDeptDict industryDeptDict;

    @Data public static class  DataRange{
        //数据起始时间
        private String startYearMonth;
        //数据结束时间
        private String endYearMonth;
    }

    @Data
    public static class CvmDict extends DataRange{
        //境内外
        private List<String> customhouseTitle;
        //内外部客户
        private List<String> bizRangeType;
        //城市国家
        private List<String> regionOrCountry;
        //机型大类
        private List<String> instanceFamily;

    }

    @Data
    public static class DeviceTypeDict extends DataRange{
        //境内外
        private List<String> customhouseTitle;
        //城市国家
        private List<String> regionOrCountry;
        //物理机
        private List<String> deviceType;
    }
    @Data
    public static class IndustryDeptDict extends DataRange{
        //境内外
        private List<String> customhouseTitle;
        //内外部客户
        private List<String> bizRangeType;
        //行业部门
        private List<String> industryDept;
    }

    public static PurchaseHistoryAndPredictDictResp toResp(PurchaseHistoryAndPredictDictDTO dict) {
        PurchaseHistoryAndPredictDictResp resp = new PurchaseHistoryAndPredictDictResp();
        CvmDict cvmDict = new CvmDict();
        cvmDict.setBizRangeType(dict.getBizRangeTypeDict());
        cvmDict.setInstanceFamily(dict.getInstanceFamilyDict());
        cvmDict.setCustomhouseTitle(dict.getCustomhouseTitleDict());
        cvmDict.setRegionOrCountry(dict.getRegionOrCountryDict());
        String start = dict.getMinYearMonth();
        String end = dict.getMaxYearMonth();
        setYearInfo(cvmDict, start, end);
        resp.setCvmDict(cvmDict);

        DeviceTypeDict deviceTypeDict = new DeviceTypeDict();
        deviceTypeDict.setDeviceType(dict.getDeviceTypeDict());
        deviceTypeDict.setRegionOrCountry(dict.getRegionOrCountryDict());
        deviceTypeDict.setCustomhouseTitle(dict.getCustomhouseTitleDict());
        setYearInfo(deviceTypeDict, start, end);
        resp.setDeviceTypeDict(deviceTypeDict);

        IndustryDeptDict industryDeptDict = new IndustryDeptDict();
        industryDeptDict.setIndustryDept(dict.getIndustryDeptDict());
        industryDeptDict.setBizRangeType(dict.getBizRangeTypeDict());
        industryDeptDict.setCustomhouseTitle(dict.getCustomhouseTitleDict());
        setYearInfo(industryDeptDict, start, end);
        resp.setIndustryDeptDict(industryDeptDict);
        return resp;
    }


    private static void setYearInfo(DataRange dataRange, String start, String end) {
        dataRange.setStartYearMonth(start);
        dataRange.setEndYearMonth(end);
    }
}
