package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.longterm.predict.service.accessor.DateAccessor;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import org.nutz.lang.Strings;

@Data
@ToString
@Table("longterm_predict_output_purchase_split_adjust")
public class LongtermPredictOutputPurchaseSplitAdjustDO extends BaseDO
        implements DateAccessor {

    public static DBHelperWithClz<LongtermPredictOutputPurchaseSplitAdjustDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    public String getKeyWithOutDateStr(){
        return Strings.join("@", this.getDeviceType(), this.getCustomerName(),
                this.getZoneName(), this.getIndustryName(), this.getModuleBusinessTypeName(),
                this.getObsProjectType(), this.getReason(),
                this.getCampus(),this.getZone(),this.getRegion());
    }

    /** 预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 每次修改一个版本<br/>Column: [split_version_id] */
    @Column(value = "split_version_id")
    private Long splitVersionId;

    @Column(value = "output_id")
    private Long outputId;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "half_year")
    private Integer halfYear;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "year")
    private Integer year;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 行业名称<br/>Column: [industry_name] */
    @Column(value = "industry_name")
    private String industryName;

    /** 业务类型<br/>Column: [module_business_type_name] */
    @Column(value = "module_business_type_name")
    private String moduleBusinessTypeName;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** 需求原因<br/>Column: [reason] */
    @Column(value = "reason")
    private String reason;

    /** cvm的可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 采购的物理机的campus<br/>Column: [campus] */
    @Column(value = "campus")
    private String campus;

    /** 物理机的可用区，广州<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 物理机的region<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** CVM的国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 物理机的核心数<br/>Column: [device_type_core_num] */
    @Column(value = "device_type_core_num")
    private Integer deviceTypeCoreNum;

    /** 采购到货量（交付月份），这个采购量也是模型公式中用到的采购量<br/>Column: [purchase_core] */
    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    @Column(value = "purchase_core_origin")
    private BigDecimal purchaseCoreOrigin;

    @Column(value = "purchase_core_debug")
    private BigDecimal purchaseCoreDebug;

    /** excel下载的note<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    public void appendNote(String msg) {
        if (Strings.isBlank(note)) {
            note = msg;
        } else {
            note += "\n" + msg;
        }
    }



    /** 剔除13周需求的debug备注<br/>Column: [remove_13week_note] */
    @Column(value = "remove_13week_note")
    private String remove13weekNote;

}