package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view.ScaleHistoryAnyDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 存量历史曲线返回体
 */
@Data
public class ScaleHistoryResp {
    //数据集
    private List<Item> items=new ArrayList<>();

    private List<IncreaseRate> increaseRates=new ArrayList<>();

    private String message;

    @Data
    public static class Item{
        //年月
        private String yearMonth;
        //存量核心数
        private BigDecimal curCore;
        //维度类型(年月为""，城市国家为城市/国家名称,机型大类为机型大类名称）
        private String dimsName;
        //占比
        private BigDecimal rate;
        // 近半年的增速
        private BigDecimal passHalfYearIncreaseRate;
    }

    //最后一个完整半年的增速
    @Data
    public static class IncreaseRate{
        private String startYearMonth;
        private String endYearMonth;
        private BigDecimal increaseRate;
        //上下半年展示名称
        private String dateName;
    }

    public List<Item> transItem(List<ScaleHistoryAnyDO> scaleHistoryAnyDOList) {
        //将do中的数值存入item中
        List<Item> items = new ArrayList<>();
        for (ScaleHistoryAnyDO scaleHistoryAnyDO : scaleHistoryAnyDOList) {
            Item item = new Item();
            item.setYearMonth(scaleHistoryAnyDO.getYearMonth());
            item.setCurCore(scaleHistoryAnyDO.getCurCore());
            item.setDimsName(scaleHistoryAnyDO.getDimName());
            items.add(item);
        }
        return items;
    }

    public ScaleHistoryResp() {
    }

    public ScaleHistoryResp(String message) {
        this.message = message;
    }
}
