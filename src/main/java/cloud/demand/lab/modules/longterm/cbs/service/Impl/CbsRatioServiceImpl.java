package cloud.demand.lab.modules.longterm.cbs.service.Impl;

import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.longterm.cbs.dto.*;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsLongtermCommonRatioDO;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioCategoryDO;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import cloud.demand.lab.modules.longterm.cbs.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.lab.modules.longterm.cbs.service.CbsRatioService;
import cloud.demand.lab.modules.longterm.cbs.web.req.ratio.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.ratio.*;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.cache.HiSpeedCacheAspect;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CbsRatioServiceImpl implements CbsRatioService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcubesDBHelper;
    @Autowired
    private HiSpeedCacheAspect hiSpeedCacheAspect;

    /**
     * 查询配比方案列表
     */
    @Override
    public QueryCbsRatioCategoryResp queryCbsRatioCategory(QueryCbsRatioCategoryReq req) {
        List<CbsRatioCategoryDO> all = cdLabDbHelper.getAll(CbsRatioCategoryDO.class);
        QueryCbsRatioCategoryResp resp = new QueryCbsRatioCategoryResp();
        resp.setCategoryList(resp.createCategoryList(all));
        return resp;
    }
    /**
     * 创建配比方案
     */
    @Transactional(value = "cdlabTransactionManager")
    @Override
    public CreateCbsRatioResp createCbsRatio(CreateCbsRatioReq req) {
        CreateCbsRatioResp resp = new CreateCbsRatioResp();
        //存入category
        CbsRatioCategoryDO categoryDO = new CbsRatioCategoryDO();
        categoryDO.setName(req.getName());
        categoryDO.setCreator(LoginUtils.getUserName());
        categoryDO.setNote(req.getNote());
        cdLabDbHelper.insert(categoryDO);
        //获得最后一个的id
        long ratioCategoryId = categoryDO.getId();
        List<CbsRatioDetailDO> dos = req.getDos();
        if (dos != null && !dos.isEmpty()) {
            ListUtils.forEach(dos, o -> o.setRatioCategoryId(ratioCategoryId));
            //存入detail
            int i = cdLabDbHelper.insertBatchWithoutReturnId(dos);
            if(dos.size() == i) log.info("创建配比方案成功，插入数据{}条", i);
            else throw new RuntimeException("创建配比方案失败，插入数据"+i+"条");
        }
        resp.setId(ratioCategoryId);
        return resp;
    }
    /**
     * 查看配比方案详情
     */
    @Override
    public QueryCbsRatioDetailResp queryCbsRatioDetail(QueryCbsRatioDetailReq req) {
        QueryCbsRatioDetailResp resp = new QueryCbsRatioDetailResp();
        Long ratioCategoryId = req.getRatioCategoryId();
        //查找category
        CbsRatioCategoryDO categoryDO = cdLabDbHelper.getOne(CbsRatioCategoryDO.class, "where id =?", ratioCategoryId);
        if(categoryDO==null) throw new RuntimeException("未找到该配比方案");
        //查找detail
        List<CbsRatioDetailDO> detailDOS = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id = ?", ratioCategoryId);
        if (detailDOS.isEmpty())throw new RuntimeException("该配比方案下无数据");
        //List<CbsRatioDetailDTO> detailDTOS = CbsRatioDetailDTO.convertToDTO(detailDOS);
        resp.setDos(detailDOS);
        resp.setName(categoryDO.getName());
        resp.setNote(categoryDO.getNote());
        resp.setCreator(categoryDO.getCreator());
        //添加cbs机型大类-》cvm机型大类的映射关系
        return resp;
    }

    /**
     * 删除配比方案
     */
    @Transactional(value = "cdlabTransactionManager")
    @Override
    public DeleteCbsRatioResp deleteCbsRatio(DeleteCbsRatioReq req) {
        Long ratioCategoryId = req.getRatioCategoryId();
        CbsRatioCategoryDO categoryDO = cdLabDbHelper.getOne(CbsRatioCategoryDO.class, "where id =?", ratioCategoryId);
        if(categoryDO==null)throw new RuntimeException("未找到该配比方案"+ratioCategoryId);
        cdLabDbHelper.delete(CbsRatioCategoryDO.class, "where id =?", ratioCategoryId);
        cdLabDbHelper.delete(CbsRatioDetailDO.class, "where ratio_category_id = ?", req.getRatioCategoryId());
        return new DeleteCbsRatioResp("SUCCESS");
    }

    /**
     * 更新配比方案
     */
    @Transactional(value = "cdlabTransactionManager")
    @Override
    public UpdateCbsRatioResp updateCbsRatio(UpdateCbsRatioReq req) {
        // 检查传入的配比方案 ID 是否存在
        Long ratioCategoryId = req.getRatioCategoryId();
        if (ratioCategoryId == null) {
            throw new RuntimeException("配比方案ID为空: " + ratioCategoryId);
        }
        // 查询配比方案（CbsLongtermPredictRatioCategoryDO）
        CbsRatioCategoryDO categoryDO = cdLabDbHelper.getOne(CbsRatioCategoryDO.class, "where id =?", ratioCategoryId);
        if (categoryDO == null) {
            throw new RuntimeException("未找到配比方案: " + ratioCategoryId);
        }
        // 更新配比方案的基本信息
        categoryDO.setName(req.getName());
        categoryDO.setNote(req.getNote());

        // 更新配比方案到数据库
        cdLabDbHelper.update(categoryDO);

        //原来的都删除，然后插入新数据
        cdLabDbHelper.delete(CbsRatioDetailDO.class, "where ratio_category_id = ?", req.getRatioCategoryId());
        // 更新配比方案明细
        List<CbsRatioDetailDO> dos = req.getDos();
        if(dos==null||dos.isEmpty())throw new RuntimeException("填写的配比明细为空");
        for (CbsRatioDetailDO detailDo :dos){
            detailDo.setId(null);
            if(null==detailDo.getRatioCategoryId())throw new RuntimeException("ratio_category_id为空");
        }
        if (!dos.isEmpty()) {
            // 批量更新配比方案明细
            int i = cdLabDbHelper.insert(dos);
            if(dos.size()==i)log.info("更新配比方案成功，更新数据{}条",i);
            else throw new RuntimeException("更新配比方案失败，更新数据"+i+"条");
        }
        return new UpdateCbsRatioResp("SUCCESS");
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public CopyCbsRatioResp copyCbsRatio(CopyCbsRatioReq req) {
        CopyCbsRatioResp resp = new CopyCbsRatioResp();
        String ratioCategoryId = req.getRatioCategoryId();
        CbsRatioCategoryDO categoryDO = cdLabDbHelper.getOne(CbsRatioCategoryDO.class, "where id =?", ratioCategoryId);
        if(categoryDO==null)throw new RuntimeException("未找到该配比方案"+ratioCategoryId);
        List<CbsRatioDetailDO> detailDOS = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id = ?", ratioCategoryId);
        if (detailDOS.isEmpty())throw new RuntimeException("该配比方案下无数据");

        //复制category
        categoryDO.setName(req.getNewRatioCategoryName());
        categoryDO.setNote(req.getNote());
        categoryDO.setCreator(LoginUtils.getUserName());
        categoryDO.setSourceId(categoryDO.getId());
        categoryDO.setId(null);
        cdLabDbHelper.insert(categoryDO);

        //复制detail
        ListUtils.forEach(detailDOS,o->{
            o.setRatioCategoryId(categoryDO.getId());
            o.setId(null);
        });
        int i = cdLabDbHelper.insertBatchWithoutReturnId(detailDOS);
        if(i==detailDOS.size())log.info("复制配比方案成功，插入数据{}条",i);
        else throw new RuntimeException("复制配比方案失败，插入数据"+i+"条");
        resp.setId(categoryDO.getId());
        return resp;
    }
    @Override
    @HiSpeedCache(expireSecond = 3600,continueFetchSecond = 1800,keyScript = "args[0]+args[1]")
    public Map<String,BigDecimal> getScaleRatios(Long taskId, Long splitVersionId){
        Map<String,BigDecimal> scaleMap=new HashMap<>();
        Map<String,BigDecimal> ratioMap=new HashMap<>();

        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getByKey(taskId);
        LocalDate predictStart = taskDO.getPredictStart();

        String predStartDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).toString();
        String predEndDate = YearMonth.of(predictStart.getYear() , 12).plusYears(1).toString();
        String finishStartDate = YearMonth.of(predictStart.getYear(), 1).toString();
        String finishEndDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue())
                .minusMonths(1).toString();
        String currentYearEnd=YearMonth.of(predictStart.getYear(),12).toString();
        String nextYearEnd=YearMonth.of(predictStart.getYear(),12).plusYears(1).toString();
        String scaleYearMonth=YearMonth.of(predictStart.getYear(),12).minusYears(1).toString();
        Integer currentYear=predictStart.getYear();
        Integer nextYear=predictStart.getYear()+1;
        // 当年的采购量， 当年的预测量，明年的预测量
        Map<String, Object> params = new HashMap<>();
        params.put("splitVersionId", splitVersionId);
        params.put("taskId", taskId);
        params.put("finishStartDate", finishStartDate);
        params.put("finishEndDate", finishEndDate);
        params.put("predStartDate", predStartDate);
        params.put("predEndDate", predEndDate);
        params.put("currentYearEnd",currentYearEnd);
        params.put("nextYearEnd",nextYearEnd);
        params.put("scaleYearMonth",scaleYearMonth);

        String sql = ORMUtils.getSql("/sql/longterm_predict/cbs/cbs_scale_ratio_purchase.sql");
        List<CbsScaleRatioItemDTO> details =
                cdLabDbHelper.getRaw(CbsScaleRatioItemDTO.class, sql, params);
        if(details==null||details.isEmpty())throw new RuntimeException("计算占比时未找到对应的采购量，taskId:"+taskId+",splitVersionId:"+splitVersionId+",sql:"+sql);
        String sql1= ORMUtils.getSql("/sql/longterm_predict/cbs/cbs_scale_ratio_scale.sql");
        List<CbsScaleRatioItemDTO> scaledetails=
                cdLabDbHelper.getRaw(CbsScaleRatioItemDTO.class, sql1, params);
        if(scaledetails==null||scaledetails.isEmpty())throw new RuntimeException("计算占比时未找到对应的存量，taskId:"+taskId+",splitVersionId:"+splitVersionId+",sql:"+sql1);
        List<CbsScaleRatioItemDTO> strategyList = scaledetails.stream().filter(o -> !o.getStrategyType().equals("存量")).collect(Collectors.toList());
        BigDecimal scaleCore=scaledetails.stream().filter(o->o.getStrategyType().equals("存量")).map(CbsScaleRatioItemDTO::getSumCore).reduce(BigDecimal.ZERO, BigDecimal::add);

        for(CbsScaleRatioItemDTO item:strategyList){
            String key=item.getYear()+"@"+item.getStrategyType();
            scaleMap.put(key,item.getSumCore());
        }
        for(CbsScaleRatioItemDTO item:details){
            String key=item.getYear()+"@"+item.getStrategyType();
            ratioMap.put(key,item.getSumCore());
        }
        //算出增量
        //先处理2026
        for(Map.Entry<String, BigDecimal> entry:scaleMap.entrySet()){
            String key=entry.getKey();
            String[] split=key.split("@");
            Integer year=Integer.parseInt(split[0]);
            String StrategyType=split[1];
            if(!year.equals(currentYear)){
                entry.setValue(entry.getValue().subtract(scaleMap.get(currentYear+"@"+StrategyType)));
            }
        }
        //处理2025
        for(Map.Entry<String, BigDecimal> entry:scaleMap.entrySet()){
            String key=entry.getKey();
            String[] split=key.split("@");
            Integer year=Integer.parseInt(split[0]);
            String StrategyType=split[1];
            if(year.equals(currentYear)){
                //处理当年情况
                entry.setValue(entry.getValue().subtract(scaleCore));
            }
        }
        //算出增量采购量比例
        for(Map.Entry<String, BigDecimal> entry:ratioMap.entrySet()){
            String key=entry.getKey();
            String[] split=key.split("@");
            Integer year=Integer.parseInt(split[0]);
            String StrategyType=split[1];
            BigDecimal scaleSumCore = scaleMap.get(key);
            entry.setValue(scaleSumCore.divide(entry.getValue(),4,RoundingMode.HALF_UP));
        }
        return ratioMap;
    }

    @SneakyThrows
    @HiSpeedCache(expireSecond = 3600,keyScript = "args[0]+args[1]+args[2]")
    @Override
    public List<CbsScaleFinishAndLatestDTO> getScaleFinishAndLatest(String finishEndDate,String scaleLastYear){
        Map<String,Object> params=new HashMap<>();
        params.put("finishEndDate",finishEndDate);
        params.put("scaleLastYear",scaleLastYear);
        String sql= IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_scale_total_finish_and_latest.sql");
        List<CbsScaleFinishAndLatestDTO> list=ckstdcrpDBHelper.getRaw(CbsScaleFinishAndLatestDTO.class, sql, params);
        if(list.isEmpty())throw new RuntimeException("未找到对应的存量数据");
        return list;
    }

    @Override
    @HiSpeedCache(expireSecond =3600,continueFetchSecond = 1800,keyScript = "args[0]+args[1]")
    @SneakyThrows
    public List<CbsDemandMarketDTO> getFinishedPurchaseData(LongtermPredictTaskDO taskDO, Map<String, Object> params) {
        String finishedSql=IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cbs/cbs_demand_market_2.sql");
        LongtermPredictCategoryConfigDO categoryDO = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", taskDO.getCategoryId());
        List<CbsDemandMarketDTO> finishedPurcharse;
        if (categoryDO != null) {
            int year = taskDO.getPredictStart().getYear();
            finishedSql = finishedSql.replace("${YEAR}", String.valueOf(year));
            finishedPurcharse = ckcubesDBHelper.getRaw(CbsDemandMarketDTO.class, finishedSql, params);
        }else{
            finishedPurcharse = null;
        }
        return finishedPurcharse;
    }

    @SneakyThrows
    @Override
    @HiSpeedCache(expireSecond = 3600,continueFetchSecond = 1800,keyScript = "args[0]")
    public List<CbsDemandMarketDTO> getLatestPurchaseData(LongtermPredictTaskDO taskDO) {
        // 查询最新的采购到货量
        String latestPurchaseSql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cbs/cbs_demand_market.sql");
        LongtermPredictCategoryConfigDO categoryDO = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", taskDO.getCategoryId());
        List<CbsDemandMarketDTO> latestPurcharse;
        if (categoryDO != null) {
            int year = taskDO.getPredictStart().getYear();
            latestPurchaseSql = latestPurchaseSql.replace("${YEAR}", String.valueOf(year));
            latestPurcharse = ckcubesDBHelper.getRaw(CbsDemandMarketDTO.class, latestPurchaseSql);
        } else {
            latestPurcharse = null;
        }
        return latestPurcharse;
    }



    /**
     * 根据配置数据构建 cbsInstanceFamily 与 commonInstanceType 的映射
     * @return cbsInstanceFamily 与对应 commonInstanceType 的映射
     */
    private Map<String, List<String>> buildCbsToCommonInstanceMap() {
        List<Mrpv2CommonInstanceTypeConfigDO> all = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class);
        return all.stream()
                .collect(Collectors.groupingBy(
                        Mrpv2CommonInstanceTypeConfigDO::getCbsInstanceFamily,  // 按 cbsInstanceFamily 分组
                        Collectors.mapping(Mrpv2CommonInstanceTypeConfigDO::getCommonInstanceType, Collectors.toList())  // 将 commonInstanceType 收集到 List 中
                ));
    }

    /**
     * 根据配置数据构建 cvm 机型与 cbs 机型族的映射
     * @return cvm 机型与对应的 cbs 机型族的映射
     */
    public List<CbsRatioDetailDO> buildCvmInstanceRatioList(Long categoryId) {
        // 获取配比方案明细
        List<CbsRatioDetailDO> ratioDetailList = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id=?", categoryId);
        if(ratioDetailList==null||ratioDetailList.isEmpty())throw new RuntimeException("查询不到配比方案明细"+categoryId+"下的配比明细");
        List<CbsRatioDetailDO> result=new ArrayList<>();
        for (CbsRatioDetailDO ratioDetailDO : ratioDetailList) {
            String[] split = ratioDetailDO.getCbsInstanceFamily().split(",");
            for (String instance:split){
                CbsRatioDetailDO cbsDo= new CbsRatioDetailDO();
                cbsDo.setId(ratioDetailDO.getId());
                cbsDo.setCbsInstanceFamily(instance);
                cbsDo.setRatioCategoryId(ratioDetailDO.getRatioCategoryId());
                cbsDo.setStrategyType(ratioDetailDO.getStrategyType());
                cbsDo.setRatioNum(ratioDetailDO.getRatioNum());
                cbsDo.setCustomhouseTitle(ratioDetailDO.getCustomhouseTitle());
                cbsDo.setBizRangeType(ratioDetailDO.getBizRangeType());
                result.add(cbsDo);
            }
        }
        return result;
//        // 获取所有配置
//        List<Mrpv2CommonInstanceTypeConfigDO> all = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class);
//        if(all==null||all.isEmpty())throw new RuntimeException("未找到实例类型配置表");
//        Map<String, String> cvmToCbsMap = new HashMap<>();
//
//        // 遍历所有配置
//        for (Mrpv2CommonInstanceTypeConfigDO config : all) {
//            // 获取实例类型字典（可能包含多个实例类型）
//            List<String> instanceTypes = config.getInstanceTypeDict();
//
//            // 遍历每个实例类型
//            for (String instanceType : instanceTypes) {
//                // 将每个实例类型与其对应的 CBS 机型族进行映射
//                cvmToCbsMap.put(instanceType, config.getCbsInstanceFamily());
//            }
//        }
//
//        return cvmToCbsMap;
    }

    @Override
    public convertCbsInstanceFamilyToCommonInstanceTypeResp queryCbsInstanceFamilyToCommonInstanceType(convertCbsInstanceFamilyToCommonInstanceTypeReq req) {
        convertCbsInstanceFamilyToCommonInstanceTypeResp resp = new convertCbsInstanceFamilyToCommonInstanceTypeResp();
        //添加cbs机型大类-》cvm机型大类的映射关系
        Map<String, List<String>> stringListMap = buildCbsToCommonInstanceMap();
        stringListMap.forEach((k,v)->{
            convertCbsInstanceFamilyToCommonInstanceTypeResp.cbsInstanceFamilyItem item= new convertCbsInstanceFamilyToCommonInstanceTypeResp.cbsInstanceFamilyItem();
            item.setCbsInstanceFamily(k);
            item.setCvmInstanceTypeList(v);
            resp.getCbsInstanceFamilyList().add(item);
        });
        return resp;
    }

    @SneakyThrows
    @HiSpeedCache(expireSecond = 3600,continueFetchSecond = 7200,keyScript = "args[0]")
    @Override
    public QueryCbsRatioHistoryResp queryCbsRatioHistory(QueryCbsRatioHistoryReq req) {
        QueryCbsRatioHistoryResp resp = new QueryCbsRatioHistoryResp();
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_ratio_histroy.sql");
        List<CbsRatioHistoryDTO> dtos = ckstdcrpDBHelper.getRaw(CbsRatioHistoryDTO.class, sql);

        // 1. 最细粒度分组（使用原始机型）
        List<CbsRatioHistoryDTO> windowCalculated = calculateSlidingWindowWithOriginalInstance(dtos);

        // 2. 转换机型并过滤无效数据
        Map<String, String> convertMap = getInstanceTypeToCommonInstanceTypeMap();
        windowCalculated.forEach(dto -> dto.setInstanceType(convertMap.get(dto.getInstanceType())));
        windowCalculated.removeIf(dto -> dto.getInstanceType() == null);
        windowCalculated.removeIf(dto -> dto.getStatTime().compareTo("2023-07-31") < 0);

        // 3. 处理三种维度
        //List<QueryCbsRatioHistoryResp.RatioItem> allItems = aggregateByDimension(windowCalculated, "all");
        List<QueryCbsRatioHistoryResp.RatioItem> defaultItems = aggregateByDimension(windowCalculated, "InstanceType");
        //List<QueryCbsRatioHistoryResp.RatioItem> yearMonthItems = aggregateByDimension(windowCalculated, "yearMonth");
        List<QueryCbsRatioHistoryResp.RatioItem> customhouseItems = aggregateByDimension(windowCalculated, "InstanceTypeAndCustomhouseTitle");

        // 4. 创建TypeItem并添加到resp
        //addToTypeItem(resp, "all", allItems);
        addToTypeItem(resp, "InstanceType", defaultItems);
        //addToTypeItem(resp, "yearMonth", yearMonthItems);
        addToTypeItem(resp, "InstanceTypeAndCustomhouseTitle", customhouseItems);

        return resp;
    }

    private Map<String,String> getInstanceTypeToCommonInstanceTypeMap(){
        List<Mrpv2CommonInstanceTypeConfigDO> all = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class);
        Map<String,String> resultMap=new HashMap<>();
        all.forEach(configDo->{
            String[] instanceTypes=configDo.getInstanceTypes().split(",");
            for(String instance_type:instanceTypes){
                resultMap.put(instance_type,configDo.getCommonInstanceType());
            }
        });
        return resultMap;
    }


    // 辅助方法：将结果添加到TypeItem
    private void addToTypeItem(QueryCbsRatioHistoryResp resp, String type, List<QueryCbsRatioHistoryResp.RatioItem> items) {
        QueryCbsRatioHistoryResp.TypeItem typeItem = new QueryCbsRatioHistoryResp.TypeItem();
        typeItem.setType(type);
        typeItem.setRatioItemList(items);
        resp.getTypeItems().add(typeItem);
    }

    /**
     * 使用原始机型进行最细粒度分组并计算滑动窗口
     */
    private List<CbsRatioHistoryDTO> calculateSlidingWindowWithOriginalInstance(List<CbsRatioHistoryDTO> dtos) {
        // 1. 最细粒度分组键（使用原始instanceType）
        Function<CbsRatioHistoryDTO, String> fineGrainedKeyGenerator = dto ->
                dto.getType() + "@" +
                        dto.getInstanceType() + "@" +
                        //(dto.getBizRangeType() != null ? dto.getBizRangeType() : "NULL") + "@" +
                        (dto.getCustomhouseTitle() != null ? dto.getCustomhouseTitle() : "NULL");

        // 2. 按最细粒度分组
        Map<String, List<CbsRatioHistoryDTO>> groupedData = dtos.stream()
                .collect(Collectors.groupingBy(fineGrainedKeyGenerator));

        // 3. 滑动窗口计算
        List<CbsRatioHistoryDTO> result = new ArrayList<>();
        for (List<CbsRatioHistoryDTO> group : groupedData.values()) {
            // 按时间排序确保窗口计算正确
            group.sort(Comparator.comparing(CbsRatioHistoryDTO::getStatTime));

            for (int i = 0; i < group.size(); i++) {
                BigDecimal sumCurCore12 = BigDecimal.ZERO;
                int startIdx = Math.max(0, i - 11); // 取前11行

                // 累加当前及前11条数据
                for (int j = startIdx; j <= i; j++) {
                    sumCurCore12 = sumCurCore12.add(group.get(j).getCur());
                }

                CbsRatioHistoryDTO dto = group.get(i);
                CbsRatioHistoryDTO resultDto = new CbsRatioHistoryDTO();
                // 保留原始机型
                resultDto.setInstanceType(dto.getInstanceType());
                resultDto.setStatTime(dto.getStatTime());
                resultDto.setType(dto.getType());
                //resultDto.setBizRangeType(dto.getBizRangeType());
                resultDto.setCustomhouseTitle(dto.getCustomhouseTitle());
                resultDto.setSumCurCore12(sumCurCore12);

                result.add(resultDto);
            }
        }

        return result;
    }
    /**
     * 根据维度聚合数据
     */
    private List<QueryCbsRatioHistoryResp.RatioItem> aggregateByDimension(
            List<CbsRatioHistoryDTO> windowCalculated, String dimensionType) {

        // 1. 构建聚合键
        Function<CbsRatioHistoryDTO, String> aggregationKeyGenerator = dto -> {
            if ("InstanceType".equals(dimensionType)) {
                // 默认维度：年月 + 机型
                return dto.getStatTime() + "@" + dto.getInstanceType();
            } else if ("yearMonth".equals(dimensionType)) {
                // 年月维度：年月
                if (dto.getStatTime() == null) return null;
                return dto.getStatTime();
            } else if ("InstanceTypeAndCustomhouseTitle".equals(dimensionType)) {
                // 境内外维度：年月 +机型+ 境内外
                if (dto.getCustomhouseTitle() == null) return null;
                return dto.getStatTime() + "@" + dto.getInstanceType() + "@" + dto.getCustomhouseTitle();
            }else{
                throw new RuntimeException("不支持的分析维度");
            }
        };

        // 2. 聚合CBS和CVM的滚动和
        Map<String, BigDecimal[]> aggregationMap = new HashMap<>();
        for (CbsRatioHistoryDTO dto : windowCalculated) {
            String key = aggregationKeyGenerator.apply(dto);
            if (key == null) continue;

            BigDecimal[] sums = aggregationMap.computeIfAbsent(key,
                    k -> new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO});

            if ("CBS".equals(dto.getType())) {
                sums[0] = sums[0].add(dto.getSumCurCore12());
            }
            else if ("CVM".equals(dto.getType())) {
                sums[1] = sums[1].add(dto.getSumCurCore12());
            }
        }

        // 3. 计算配比并构建结果
        List<QueryCbsRatioHistoryResp.RatioItem> result = new ArrayList<>();
        for (Map.Entry<String, BigDecimal[]> entry : aggregationMap.entrySet()) {
            String[] keyParts = entry.getKey().split("@");
            BigDecimal cbsSum = entry.getValue()[0];
            BigDecimal cvmSum = entry.getValue()[1];

            // 跳过无效数据
            if (cvmSum.compareTo(BigDecimal.ZERO) <= 0 || cbsSum.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算配比
            BigDecimal ratio = cbsSum.divide(cvmSum, 2, RoundingMode.HALF_UP);

            // 创建结果项
            QueryCbsRatioHistoryResp.RatioItem item = new QueryCbsRatioHistoryResp.RatioItem();
            item.setStatTime(keyParts[0]);
            if(!"yearMonth".equals(dimensionType))item.setCbsInstanceFamily(keyParts[1]);
            item.setRatio(ratio);

            // 设置维度特定字段
            if ("InstanceTypeAndCustomhouseTitle".equals(dimensionType)) {
                // 境内外维度：设置境内外字段
                item.setCustomhouseTitle(keyParts[2]);
            }

            result.add(item);
        }

        // 4. 按时间降序排序
        result.sort(Comparator.comparing(QueryCbsRatioHistoryResp.RatioItem::getStatTime).reversed());
        return result;
    }

    //@HiSpeedCache(expireSecond = 3600)
    @Override
    public Map<String,BigDecimal> getCommonRatioMap(String taskId){
        Map<String,BigDecimal> result= new HashMap<>();
        List<CbsLongtermCommonRatioDO> list= cdLabDbHelper.getAll(CbsLongtermCommonRatioDO.class);
        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getByKey(taskId);
        String yearMonthstr = YearMonth.of(taskDO.getPredictStart().getYear(), taskDO.getPredictStart().getMonth()).toString();
        list.removeIf(o->
                (o.getRatioName().equals("next_enhance_year_ratio")||o.getRatioName().equals("current_enhance_year_ratio"))
                        &&!o.getYearMonth().equals(yearMonthstr)
        );
        list.forEach(o->{
            result.putIfAbsent(o.getRatioName(),o.getRatio());
        });
        return result;
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public SetRatioResp setCustomhouseRatio(SetRatioReq req) {
        SetRatioResp resp = new SetRatioResp();
        BigDecimal ratio=req.getRatio();
        // 检查传入的比率是否为null或者为负数
        if (ratio == null || ratio.compareTo(BigDecimal.ZERO) <= 0||ratio.compareTo(BigDecimal.ONE)>=1) {
            throw new RuntimeException("无效的配比值: " + ratio);
        }

        CbsLongtermCommonRatioDO one = cdLabDbHelper.getOne(CbsLongtermCommonRatioDO.class, "where ratio_name='customhouse_ratio'");
        if(one==null){
            throw new RuntimeException("未找到customhouse_ratio的记录");
        }
        one.setRatio(ratio);
        int updatedCount = cdLabDbHelper.update(one);
        // 根据更新的记录数判断是否成功
        if (updatedCount > 0) {
            log.info("成功更新配比，更新了 {} 条记录", updatedCount);
            resp.setMessage("SUCCESS");
            return resp;
        } else {
            throw new RuntimeException("没有记录被更新，更新失败");
        }

    }


    @Override
    @Transactional(value = "cdlabTransactionManager")
    public SetRatioResp createEnhanceYearRatio(SetRatioReq req) {
        SetRatioResp resp = new SetRatioResp();
        BigDecimal ratio = req.getRatio();
        String ratioName = req.getRatioName();
        Long taskId = req.getTaskId();
        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getByKey(taskId);
        if(taskDO==null) throw new RuntimeException("未找到task版本，请重试");
        String yearMonthstr = YearMonth.of(taskDO.getPredictStart().getYear(), taskDO.getPredictStart().getMonth()).toString();

        // 检查传入的比率是否为null或者为负数
        if (ratio == null || ratio.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("无效的配比值: " + ratio);
        }else if (ratio.compareTo(BigDecimal.valueOf(100))>0){
            throw new RuntimeException("配比值不能大于100");
        }

        // 尝试从数据库中获取记录
        CbsLongtermCommonRatioDO one = cdLabDbHelper.getOne(CbsLongtermCommonRatioDO.class, "where ratio_name='"+ratioName+"' and year_month_str = '"+yearMonthstr+"'");

        if (one != null) {
            // 如果记录存在，执行更新操作
            one.setRatio(ratio);
            if (req.getNote()!=null) {
                one.setNote(req.getNote());
            }
            int updatedCount = cdLabDbHelper.update(one);
            if (updatedCount > 0) {
                log.info("成功更新配比，更新了 {} 条记录", updatedCount);
                resp.setMessage("SUCCESS");
            } else {
                throw new RuntimeException("没有记录被更新，更新失败");
            }
        } else {
            // 如果记录不存在，执行插入操作
            CbsLongtermCommonRatioDO ratioDo = new CbsLongtermCommonRatioDO();
            ratioDo.setRatioName(ratioName);
            ratioDo.setYearMonth(yearMonthstr);
            if (req.getNote() != null) {
                ratioDo.setNote(req.getNote());
            } else {
                ratioDo.setNote("年份数据提升比例，默认为1不提升");
            }
            ratioDo.setRatio(ratio);
            int insert = cdLabDbHelper.insert(ratioDo);
            resp.setMessage("SUCCESS");
        }

        return resp;
    }


//    @Transactional(value = "cdlabTransactionManager")
//    public SetRatioResp updateEnhanceYearRatio(SetRatioReq req) {
//        SetRatioResp resp = new SetRatioResp();
//        String ratioName = req.getRatioName();
//        BigDecimal ratio = req.getRatio();
//        Long taskId = req.getTaskId();
//        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getByKey(taskId);
//        String yearMonthstr = YearMonth.of(taskDO.getPredictStart().getYear(), taskDO.getPredictStart().getMonth()).toString();
//        // 检查传入的比率是否为null或者为负数
//        if (ratio == null || ratio.compareTo(BigDecimal.ZERO) < 0) {
//            throw new RuntimeException("无效的配比值: " + ratio);
//        }
//
//        //String key = ratioName+"@"+req.getTaskId();
//        CbsLongtermCommonRatioDO one = cdLabDbHelper.getOne(CbsLongtermCommonRatioDO.class, "where ratio_name='"+ratioName+"' and year_month_str = '"+yearMonthstr+"'");
//        if(one==null){
//            throw new RuntimeException("未找到enhance_year的记录");
//        }
//        one.setRatio(ratio);
//        if(!req.getNote().isEmpty())one.setNote(req.getNote());
//        int updatedCount = cdLabDbHelper.update(one);
//        // 根据更新的记录数判断是否成功
//        if (updatedCount > 0) {
//            log.info("成功更新配比，更新了 {} 条记录", updatedCount);
//            resp.setMessage("SUCCESS");
//            return resp;
//        } else {
//            throw new RuntimeException("没有记录被更新，更新失败");
//        }
//    }

    @Override
    public GetCommonRatioResp getAllCommonRatios(GetCommonRatioReq req) {
        String taskId = req.getTaskId().toString();
        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getByKey(taskId);
        String yearMonthstr = YearMonth.of(taskDO.getPredictStart().getYear(), taskDO.getPredictStart().getMonth()).toString();
        GetCommonRatioResp resp = new GetCommonRatioResp();
        List<CbsLongtermCommonRatioDO> all = cdLabDbHelper.getAll(CbsLongtermCommonRatioDO.class);
        if(all==null||all.isEmpty())throw new RuntimeException("未找到任何记录");
        all.removeIf(o->
                (o.getRatioName().equals("next_enhance_year_ratio")||o.getRatioName().equals("current_enhance_year_ratio"))
                &&!o.getYearMonth().equals(yearMonthstr)
        );
        resp.setData(all);
        return resp;
    }


    @SneakyThrows
    @HiSpeedCache(expireSecond = 3600)
    @Override
    public Map<String,BigDecimal> getRegionAndInstanceTypeIncreaseShareRatio(){
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_scale_ratio_instance_and_region_share.sql");
//        List<CbsInstanceAndRegionShareDTO> raw = ckcldStdCrpDBHelper.getRaw(CbsInstanceAndRegionShareDTO.class, sql);
//        Map<String,BigDecimal> result= new HashMap<>();
//        raw.forEach(o->{
//            String key=o.getCustomhouseTitle()+"@"
//                    +o.getRegionName();
//            result.putIfAbsent(key,o.getRatio());
//        });
        List<CbsInstanceAndRegionShareDTO> cbsData = ckcldStdCrpDBHelper.getRaw(CbsInstanceAndRegionShareDTO.class, sql);
// 使用 Map 存储每个实例类型和区域的累计增量
        Map<String, List<CbsInstanceAndRegionShareDTO>> groupedData = new HashMap<>();

        // 将数据按 (instance_type, customhouse_title, region_name) 进行分组
        for (CbsInstanceAndRegionShareDTO dto : cbsData) {
            String key = dto.getCustomhouseTitle() + "@" + dto.getRegionName() + "@" + dto.getInstanceType();
            groupedData.computeIfAbsent(key, k -> new ArrayList<>()).add(dto);
        }

        // 计算每个分组的增量
        Map<String, BigDecimal> result = new HashMap<>();
        List<CbsInstanceAndRegionShareDTO> temp=new ArrayList<>();

        for (Map.Entry<String, List<CbsInstanceAndRegionShareDTO>> entry : groupedData.entrySet()) {
            List<CbsInstanceAndRegionShareDTO> group = entry.getValue();
            // 排序，按日期（stat_time）降序排列
            group.sort((o1, o2) -> o1.getStatTime().compareTo(o2.getStatTime()));
            // 计算最近三个月的增量
            BigDecimal totalIncrease = BigDecimal.ZERO;
            for (int i = 0; i < Math.min(group.size(), 3); i++) {
                totalIncrease=totalIncrease.add(group.get(i).getIncrease());
                group.get(i).setTotalIncrease(totalIncrease);
            }
            temp.add(group.get(group.size()-1));
        }
        Stream<CbsInstanceAndRegionShareDTO> cbsInstanceAndRegionShareDTOStream = temp.stream().filter(o -> o.getTotalIncrease().compareTo(BigDecimal.ZERO) > 0);
        groupedData.clear();
        cbsInstanceAndRegionShareDTOStream.forEach(o->{
            String key=o.getCustomhouseTitle()+"@"
                    +o.getRegionName();
            groupedData.computeIfAbsent(key, k -> new ArrayList<>()).add(o);
        });
        List<CbsInstanceAndRegionShareDTO> temp2=new ArrayList<>();

        for (Map.Entry<String, List<CbsInstanceAndRegionShareDTO>> entry : groupedData.entrySet()) {
            String[] key = entry.getKey().split("@");
            CbsInstanceAndRegionShareDTO dto=new CbsInstanceAndRegionShareDTO();
            List<CbsInstanceAndRegionShareDTO> value = entry.getValue();
            dto.setCustomhouseTitle(key[0]);
            dto.setRegionName(key[1]);
            dto.setStatTime(value.get(0).getStatTime());
            dto.setTotalIncrease(value.stream().map(CbsInstanceAndRegionShareDTO::getTotalIncrease).reduce(BigDecimal.ZERO, BigDecimal::add));
            temp2.add(dto);
        }
        BigDecimal total = temp2.stream().map(CbsInstanceAndRegionShareDTO::getTotalIncrease)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        temp2.forEach(o->{
            o.setRatio(o.getTotalIncrease().divide(total,6,RoundingMode.HALF_UP));
        });
        temp2.forEach(o->{
            String key=o.getCustomhouseTitle()+"@"
                    +o.getRegionName();
            result.putIfAbsent(key,o.getRatio());
        });
        return result;
    }

    @SneakyThrows
    @HiSpeedCache(expireSecond = 3600)
    public BigDecimal queryCbsFinished(Map<String, Object> params) {
        String sql=IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_scale_predict_finish.sql");
        BigDecimal raw = ckstdcrpDBHelper.getRawOne(BigDecimal.class, sql, params);
        return raw==null?BigDecimal.ZERO:raw;
    }
}
