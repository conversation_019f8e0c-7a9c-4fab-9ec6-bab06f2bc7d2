package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import org.springframework.stereotype.Service;

/**
 * 查询COS中长期预测任务信息
 */
@Service
public interface CosQueryPredictTaskService {

    /**
     * 查询已经存在的COS预测任务列表
     */
    QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req);

}
