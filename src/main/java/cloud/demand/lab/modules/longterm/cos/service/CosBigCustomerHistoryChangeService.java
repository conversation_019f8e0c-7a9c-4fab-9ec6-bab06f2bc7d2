package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.SaveBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.SaveBigCustomerHistoryChangeResp;

public interface CosBigCustomerHistoryChangeService {

    /**
     * 查询大客户历史变动数据
     * @param req 查询请求，必须包含categoryId，taskId可选（为0时查询默认数据）
     * @return 大客户历史变动数据列表
     */
    QueryBigCustomerHistoryChangeResp queryBigCustomerHistoryChange(QueryBigCustomerHistoryChangeReq req);

    /**
     * 保存大客户历史变动数据
     * 采用覆盖策略：先删除已存在的数据，再全量插入新数据
     * @param req 保存请求，必须包含categoryId，taskId可选（未传时填0）
     * @return 保存结果
     */
    SaveBigCustomerHistoryChangeResp saveBigCustomerHistoryChange(SaveBigCustomerHistoryChangeReq req);
}
