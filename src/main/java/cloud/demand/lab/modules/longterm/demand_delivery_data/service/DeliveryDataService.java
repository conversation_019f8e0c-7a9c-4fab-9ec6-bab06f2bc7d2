package cloud.demand.lab.modules.longterm.demand_delivery_data.service;

import cloud.demand.lab.modules.longterm.demand_delivery_data.dto.DeliveryDataDTO;
import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.ResPlanHolidayWeekDO;

import java.util.List;

/**
 * 负责交付数据的提供
 */
public interface DeliveryDataService {

    /**
     * 查询当年度，在指定日期之前的交付数据
     * @param utilDate 包含当天23:59:59
     */
    List<DeliveryDataDTO> getPhysicalData(int year, String utilDate, List<ResPlanHolidayWeekDO> weeks);


    /**
     * 查询CVM的数据
     */
    List<DeliveryDataDTO> getCvmData(int year, String utilDate, List<ResPlanHolidayWeekDO> weeks);

}
