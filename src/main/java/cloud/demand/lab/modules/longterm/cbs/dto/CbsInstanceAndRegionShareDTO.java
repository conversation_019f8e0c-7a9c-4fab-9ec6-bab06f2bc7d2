package cloud.demand.lab.modules.longterm.cbs.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CbsInstanceAndRegionShareDTO {

    @Column("stat_time")
    private String statTime;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("region_name")
    private String regionName;

    @Column("increase")
    private BigDecimal increase;

    @Column("total_increase")
    private BigDecimal totalIncrease;

    @Column("ratio")
    private BigDecimal ratio;

    @Column("instance_type")
    private String instanceType;

    @Column("cur")
    private BigDecimal curDisk;

}
