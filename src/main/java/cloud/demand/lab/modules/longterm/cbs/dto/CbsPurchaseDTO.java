package cloud.demand.lab.modules.longterm.cbs.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class CbsPurchaseDTO {
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "year")
    private Integer year;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "finish_purchase_core")
    private BigDecimal finishPurchaseCore;

    @Column(value = "predict_purchase_core")
    private BigDecimal predictPurchaseCore;

    @Column(value = "finish_purchase_disk")
    private BigDecimal finishPurchaseDisk;

    @Column(value = "predict_purchase_disk")
    private BigDecimal predictPurchaseDisk;

    @Column(value = "cvm_instance_type")
    private String cvmInstanceType;

    @Column(value = "cbs_instance_family")
    private String cbsInstanceFamily;
}
