package cloud.demand.lab.modules.longterm.predict.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum LongtermPredictTaskStatusEnum {

    NEW("NEW", "待预测"),

    RUNNING("RUNNING", "运行中"),

    PREDICT_FAIL("PREDICT_FAIL", "预测运行失败"),

    SPLIT_FAIL("SPLIT_FAIL", "拆分预测失败"),

    PREDICT_SUCCESS("PREDICT_SUCCESS", "预测成功,待拆分"),

    SUCCESS("SUCCESS", "运行成功")

    ;

    final private String code;
    final private String name;

    LongtermPredictTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LongtermPredictTaskStatusEnum getByCode(String code) {
        for (LongtermPredictTaskStatusEnum e : LongtermPredictTaskStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        LongtermPredictTaskStatusEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}