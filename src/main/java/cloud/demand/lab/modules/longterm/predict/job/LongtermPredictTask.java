package cloud.demand.lab.modules.longterm.predict.job;

import cloud.demand.lab.modules.longterm.predict.dto.SplitMsgDTO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.Constants;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictCategoryTypeEnum;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermDecisionPredictService;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermPredictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 负责中长期模型预测的任务
 */
@Slf4j
@Service
public class LongtermPredictTask {

    @Resource
    RedisHelper redisHelper;
    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private CreateLongtermPredictService createLongtermPredictService;
    @Resource
    private CreateLongtermDecisionPredictService createLongtermDecisionPredictService;

    /**
     * 处理来自消息队列中的任务。这个可以保证任务实时被处理。
     * 为什么用定时任务，因为生产的任务节点和web节点分来，确保任务执行异常不影响web节点。
     * 说明，这里会长期占用schedule池中的一条线程，schedule的线程数已由20调整为30，故没有影响。
     */
   @Scheduled(fixedDelay = 1L)
    public void fromRedisMq() {
        RedisMsg msg = redisHelper.receive(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK);
        if (msg != null) {
            Long taskId = NumberUtils.parseLong(msg.getMsg());
            try {
                if (taskId != null) {
                    LongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(LongtermPredictTaskDO.class, taskId);
                    if (taskDO == null) {
                        log.error("task id {} not exist, ignore task", taskId);
                    }
                    if (Objects.equals(taskDO.getCategoryType(), LongtermPredictCategoryTypeEnum.DECISION.getCode())) {
                        createLongtermDecisionPredictService.doRunTask(taskId);
                    } else {
                        // 默认是原始的存量预测模型
                        createLongtermPredictService.doRunTask(taskId);
                    }
                    sendSplitMsg(taskId);
                }
            } catch (Exception e) {
                log.error("run longterm predict task fail, taskId:{}", taskId, e);
            } finally {
                redisHelper.ack(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK, msg.getUuid()); // 只处理一次
            }
        }
    }

    private void sendSplitMsg(Long taskId) {
        SplitMsgDTO splitMsgDTO = new SplitMsgDTO();
        splitMsgDTO.setTaskId(taskId);
        splitMsgDTO.setName(Constants.DEFAULT_SPIT_VERSION);
        redisHelper.send(Constants.REDIS_QUEUE_LONGTERM_SPLIT_TASK, JSON.toJson(splitMsgDTO), 600);
    }

    /**
     * 定期从数据库里扫描进行处理
     */
    @Scheduled(fixedDelay = 30000)
    public void fromDB() {
        List<LongtermPredictTaskDO> toRunTask = cdLabDbHelper.getAll(LongtermPredictTaskDO.class,
                "where task_status in (?)",
                ListUtils.of(LongtermPredictTaskStatusEnum.NEW.getCode()));
        for (LongtermPredictTaskDO task : toRunTask) {
            try {
                if (Objects.equals(task.getCategoryType(), LongtermPredictCategoryTypeEnum.DECISION.getCode())) {
                    createLongtermDecisionPredictService.doRunTask(task.getId());
                } else {
                    // 默认是原始的存量预测模型
                    createLongtermPredictService.doRunTask(task.getId());
                }
                sendSplitMsg(task.getId());
            } catch (Exception e) {
                log.error("run longterm predict task fail, taskId:{}", task.getId(), e); // 不影响其他任务执行
            }
        }
    }

}
