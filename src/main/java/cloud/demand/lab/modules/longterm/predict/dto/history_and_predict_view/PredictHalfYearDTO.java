package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class PredictHalfYearDTO {
    @Column(value = "cur_core")
    private BigDecimal curCore;

    @Column("year_month_str")
    private String yearMonthStr;

    @Column("strategy_type")
    private String strategyType;
}
