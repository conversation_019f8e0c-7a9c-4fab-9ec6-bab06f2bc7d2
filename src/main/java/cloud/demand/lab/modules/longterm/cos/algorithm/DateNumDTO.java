package cloud.demand.lab.modules.longterm.cos.algorithm;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DateNumDTO {

    private String date;
    private BigDecimal value;

    public DateNumDTO(String date, Integer value) {
        this.date = date;
        this.value = new BigDecimal(value);
    }

    public DateNumDTO(String date, BigDecimal value) {
        this.date = date;
        this.value = value;
    }

}
