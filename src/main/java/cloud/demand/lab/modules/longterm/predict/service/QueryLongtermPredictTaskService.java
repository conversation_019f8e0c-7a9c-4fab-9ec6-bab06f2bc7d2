package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.OperateInputArgsTrendNoteReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryInputArgsTrendReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryPredictTaskInfoReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryTaskStatusReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.OperateInputArgsTrendNoteResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryInputArgsTrendResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryPredictTaskInfoResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryTaskStatusResp;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 查询中长期的任务信息
 */
public interface QueryLongtermPredictTaskService {

    /**
     * 查询已经存在的预测任务列表
     */
    QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req);

    /**
     * 查询预测任务的概要信息和输入参数
     */
    QueryPredictTaskInfoResp queryPredictTaskInfo(QueryPredictTaskInfoReq req);

    /**
     * 查询输入参数趋势
     */
    QueryInputArgsTrendResp queryInputArgsTrend(QueryInputArgsTrendReq req);

    /**
     * 操作输入参数备注
     */
    OperateInputArgsTrendNoteResp operateInputArgsTrendNote(@JsonrpcParam OperateInputArgsTrendNoteReq req);

    /**
     * 查询任务运行状态
     */
    QueryTaskStatusResp queryTaskStatus(QueryTaskStatusReq req);
}
