package cloud.demand.lab.modules.longterm.predict.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 预测的来源的枚举值
 */
@Getter
public enum LongtermPredictSourceEnum {

    MODEL_HISTORY("MODEL_HISTORY", "历史实际"),

    MODEL_PREDICT("MODEL_PREDICT", "模型预测"),

    INDUSTRY_INPUT("INDUSTRY_INPUT", "行业提报"),

    PRODUCT_COMMITTEE("PRODUCT_COMMITTEE", "产委会"),

    PRODUCT_DECISION("PRODUCT_DECISION", "产品决策"),

    SOP_DEMAND("SOP_DEMAND", "SOP全年需求")

    ;

    final private String code;
    final private String name;

    LongtermPredictSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LongtermPredictSourceEnum getByCode(String code) {
        for (LongtermPredictSourceEnum e : LongtermPredictSourceEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        LongtermPredictSourceEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}