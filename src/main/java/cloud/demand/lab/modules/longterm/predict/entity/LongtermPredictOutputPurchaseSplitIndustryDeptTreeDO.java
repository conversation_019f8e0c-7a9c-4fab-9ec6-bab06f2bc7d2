package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_output_purchase_split_industry_dept_tree")
public class LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO extends BaseDO {

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "split_version_id")
    private Long splitVersionId;

    @Column(value = "output_id")
    private Long outputId;

    @Column(value = "input_args_id")
    private Long inputArgsId;

    @Column(value = "tree_parent")
    private Long treeParent;

    @Column(value = "tree_id")
    private Long treeId;

    @Column(value = "tree_index")
    private Long treeIndex;

    @Column(value = "tree_level")
    private Long treeLevel;

    @Column(value = "tree_note")
    private String treeNote;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "customer_type")
    private String customerType;

    @Column(value = "start_date")
    private LocalDate startDate;

    @Column(value = "end_date")
    private LocalDate endDate;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "half_year")
    private Integer halfYear;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "year")
    private Integer year;

    /** 内外部<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "country_name")
    private String countryName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    @Column(value = "purchase_core_origin")
    private BigDecimal purchaseCoreOrigin;

}