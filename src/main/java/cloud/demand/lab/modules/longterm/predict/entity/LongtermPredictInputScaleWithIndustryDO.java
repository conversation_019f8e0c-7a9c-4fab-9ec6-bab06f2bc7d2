package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("longterm_predict_input_scale_with_industry")
public class LongtermPredictInputScaleWithIndustryDO extends BaseDO {

    public static DBHelperWithClz<LongtermPredictInputScaleWithIndustryDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }

    @Column(value = "task_id")
    private Long taskId;

    /** 日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    /** 存量核心数，外部计费内部服务<br/>Column: [cur_core] */
    @Column(value = "cur_core")
    private BigDecimal curCore;

    /** 内外部<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

}