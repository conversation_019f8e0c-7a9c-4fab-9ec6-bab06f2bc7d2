package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

@Component
public class CosCreatePredictTaskServiceImpl implements CosCreatePredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<CosLongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(ListUtils.transform(categoryConfigs, o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            LocalDate startDate = getPredictStartDate(o);
            LocalDate endDate = getPredictEndDate(o, startDate);
            item.setPredictStart(DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(DateUtils.format(endDate, "yyyy-MM"));
            item.setInputArgDateRanges(getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(ListUtils.transform(StrategyTypeEnum.values(), QueryCategoryForCreateResp.StrategyType::from));
            return item;
        }));
        return resp;
    }

    @Override
    public CosLongtermPredictCategoryConfigDO getCategoryById(Long id) {
        if (id == null) {
            return null;
        }
        return cdLabDbHelper.getByKey(CosLongtermPredictCategoryConfigDO.class, id);
    }

    private List<QueryCategoryForCreateResp.InputArgDateRange> getDateRange(LocalDate startDate, LocalDate endDate,
                                                                            int intervalMonth) {
        if (intervalMonth == 6) { // 每半年作为1个录入周期
            if (startDate.getMonthValue() >= 7) {
                startDate = LocalDate.of(startDate.getYear(), 6, 30);
            } else {
                startDate = LocalDate.of(startDate.getYear() - 1, 12, 31);
            }
            if (endDate.getMonthValue() >= 7) {
                endDate = LocalDate.of(endDate.getYear(), 12, 31);
            } else {
                endDate = LocalDate.of(endDate.getYear(), 6, 30);
            }

            List<QueryCategoryForCreateResp.InputArgDateRange> ranges = new ArrayList<>();

            while (startDate.isBefore(endDate)) {
                QueryCategoryForCreateResp.InputArgDateRange range = new QueryCategoryForCreateResp.InputArgDateRange();
                if (startDate.getMonthValue() == 6) {
                    range.setDateName(startDate.getYear() + "下半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(DateUtils.format(rangeStartDate));
                    range.setEndDate(DateUtils.format(rangeStartDate.plusMonths(6).minusDays(1)));
                } else {
                    range.setDateName((startDate.getYear() + 1) + "上半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(DateUtils.format(rangeStartDate));
                    range.setEndDate(DateUtils.format(rangeStartDate.plusMonths(6).minusDays(1)));
                }
                ranges.add(range);

                startDate = startDate.plusDays(1).plusMonths(6).minusDays(1);
            }

            return ranges;
        } else if (intervalMonth == 12) { // 每年作为1个录入周期
            startDate = LocalDate.of(startDate.getYear(), 1, 1);
            endDate = LocalDate.of(endDate.getYear(), 12, 31);
            List<QueryCategoryForCreateResp.InputArgDateRange> ranges = new ArrayList<>();
            while (startDate.isBefore(endDate)) {
                QueryCategoryForCreateResp.InputArgDateRange range = new QueryCategoryForCreateResp.InputArgDateRange();
                range.setDateName(startDate.getYear() + "年");
                range.setStartDate(DateUtils.format(startDate));
                range.setEndDate(DateUtils.format(LocalDate.of(startDate.getYear(), 12, 31)));
                ranges.add(range);

                startDate = startDate.plusYears(1);
            }
            return ranges;
        } else {
            return new ArrayList<>();
        }
    }

    /**都以月末为表示*/
    protected LocalDate getPredictStartDate(CosLongtermPredictCategoryConfigDO categoryConfigDO) {
        if ("CUR_MONTH".equals(categoryConfigDO.getPredictStart())) {
            return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        } else {
            LocalDate startDate = DateUtils.parseLocalDate(categoryConfigDO.getPredictStart());
            if (startDate == null) {
                return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()); // 默认当月
            }
            return startDate.with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**都以月末为表示*/
    protected LocalDate getPredictEndDate(CosLongtermPredictCategoryConfigDO categoryConfigDO, LocalDate startDate) {
        if (categoryConfigDO.getPredictEnd() != null && categoryConfigDO.getPredictEnd().startsWith("LAST_MONTH_")) {
            String month = categoryConfigDO.getPredictEnd().substring("LAST_MONTH_".length());
            Integer m = NumberUtils.parseInt(month);
            if (m != null && m >= 0) {
                return startDate.plusMonths(m).with(TemporalAdjusters.lastDayOfMonth());
            } else {
                return startDate.plusMonths(18).with(TemporalAdjusters.lastDayOfMonth()); // 默认18个月
            }
        } else {
            return DateUtils.parseLocalDate(categoryConfigDO.getPredictEnd()).with(TemporalAdjusters.lastDayOfMonth());
        }
    }

}
