package cloud.demand.lab.modules.longterm.cbs.web;


import cloud.demand.lab.modules.longterm.cbs.service.CbsRatioService;
import cloud.demand.lab.modules.longterm.cbs.web.req.ratio.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.ratio.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.Set;

/**
CBS中长期配比方案相关接口
 */
@JsonrpcController("/cbs_longterm_predict")
@Slf4j
public class CbsRatioController {
    @Resource
    private CbsRatioService cbsRatioService;

    ///longtermpredict一起去掉 方法名 req resp
    /**
     * 查询配比方案列表
     */
    @RequestMapping
    public QueryCbsRatioCategoryResp queryCbsRatioCategory(@JsonrpcParam QueryCbsRatioCategoryReq req) {
        return cbsRatioService.queryCbsRatioCategory(req);
    }


    /**
     * 创建配比方案
     */
    @RequestMapping
    public CreateCbsRatioResp createCbsRatio(@JsonrpcParam CreateCbsRatioReq req) {
        return cbsRatioService.createCbsRatio(req);
    }

    /**
     * 查看配比方案详情
     */
    @RequestMapping
    public QueryCbsRatioDetailResp queryCbsRatioDetail(@JsonrpcParam QueryCbsRatioDetailReq req) {
        return cbsRatioService.queryCbsRatioDetail(req);
    }
    /**
     * 删除配比方案
     */
    @RequestMapping
    public DeleteCbsRatioResp deleteCbsRatio(@JsonrpcParam DeleteCbsRatioReq req) {
        return cbsRatioService.deleteCbsRatio(req);
    }
    /**
     * 更新配比方案
     */
    @RequestMapping
    public UpdateCbsRatioResp UpdateCbsRatio(@JsonrpcParam UpdateCbsRatioReq req) {
        return cbsRatioService.updateCbsRatio(req);
    }

    /**
     * 复制配比方案
     */
    @RequestMapping
    public CopyCbsRatioResp copyCbsRatio(@JsonrpcParam CopyCbsRatioReq req) {
        return cbsRatioService.copyCbsRatio(req);
    }
    /**
     * 查询cbs机型大类对应的机型族映射
     */
    @RequestMapping
    public convertCbsInstanceFamilyToCommonInstanceTypeResp queryCbsInstanceFamilytoCommonInstanceType(@JsonrpcParam convertCbsInstanceFamilyToCommonInstanceTypeReq req) {
        return cbsRatioService.queryCbsInstanceFamilyToCommonInstanceType(req);
    }

    /**
     * 查询配比历史趋势图
     */
    @RequestMapping
    public QueryCbsRatioHistoryResp queryCbsRatioHistory(@JsonrpcParam QueryCbsRatioHistoryReq req) {
        return cbsRatioService.queryCbsRatioHistory(req);
    }

    @RequestMapping
    public GetCommonRatioResp getAllCommonRatios(@JsonrpcParam GetCommonRatioReq req) {
        return cbsRatioService.getAllCommonRatios(req);
    }


    @RequestMapping
    public SetRatioResp setCustomhouseRatio(@JsonrpcParam SetRatioReq req){
       return cbsRatioService.setCustomhouseRatio(req);
    }

//    @RequestMapping
//    public SetRatioResp updateEnhanceYearRatio(@JsonrpcParam SetRatioReq req){
//        return cbsRatioService.updateEnhanceYearRatio(req);
//    }

    @RequestMapping
    public SetRatioResp createEnhanceYearRatio(@JsonrpcParam SetRatioReq req){
        return cbsRatioService.createEnhanceYearRatio(req);
    }

}
