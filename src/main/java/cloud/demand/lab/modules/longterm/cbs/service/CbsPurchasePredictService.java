package cloud.demand.lab.modules.longterm.cbs.service;

import cloud.demand.lab.modules.longterm.cbs.dto.CbsDemandMarketDTO;
import cloud.demand.lab.modules.longterm.cbs.dto.CbsPurchaseDTO;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.QueryCbsPurchaseTotalReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseHalfYearAccumulateResp;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseTotalResp;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;

import java.util.List;
import java.util.Map;

/**
 * CBS中长期采购量及其预测
 */
public interface CbsPurchasePredictService {
    /**
     * 右边两个框，采购量预测
     */
    QueryCbsPurchaseTotalResp queryCbsPurchaseTotal(QueryCbsPurchaseTotalReq req);


    /**
     * 半年累计预测
     */
    QueryCbsPurchaseHalfYearAccumulateResp queryCbsPurchaseHalfYearAccumulate(QueryCbsPurchaseTotalReq req);

    /**
     * 将核心数转换为磁盘书
     * @param categoryId
     * @param cbsPredictDTOList
     * @return
     */
    List<CbsPurchaseDTO> convertSumCoreToSumDisk(Long categoryId, List<CbsPurchaseDTO> cbsPredictDTOList);

    /**
     * 合并计算预测dto和完成量dto
     * @param cbsPredictDTOList
     * @param cbsFinishDTOList
     * @return
     */
    List<CbsPurchaseDTO> mergeAndCalculate(List<CbsPurchaseDTO> cbsPredictDTOList, List<CbsPurchaseDTO> cbsFinishDTOList);

    /**
     * 处理策略item
     * @param strategyItemList
     * @param params
     * @return
     */
    Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> processStrategyItems(List<CbsPurchaseDTO> strategyItemList, Map<String, Object> params);

    /**
     * 创建年item
     * @param strategyItemMap
     * @return
     */
    List<QueryCbsPurchaseTotalResp.CbsYearItem> buildYearItems(Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap);


}
