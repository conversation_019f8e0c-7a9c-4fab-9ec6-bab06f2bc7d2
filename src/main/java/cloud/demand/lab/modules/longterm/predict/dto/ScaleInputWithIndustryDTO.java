package cloud.demand.lab.modules.longterm.predict.dto;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleWithIndustryDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ScaleInputWithIndustryDTO {

    /** 日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 存量核心数，外部计费内部服务<br/>Column: [cur_core] */
    @Column(value = "cur_core")
    private BigDecimal curCore;

    /** 内外部<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    public static LongtermPredictInputScaleWithIndustryDO transFrom(ScaleInputWithIndustryDTO scaleInput, Long taskId) {
        LongtermPredictInputScaleWithIndustryDO longtermPredictInputScaleDO = new LongtermPredictInputScaleWithIndustryDO();
        longtermPredictInputScaleDO.setTaskId(taskId);
        longtermPredictInputScaleDO.setStatTime(scaleInput.getStatTime());
        longtermPredictInputScaleDO.setYearMonthStr(DateUtils.format(scaleInput.getStatTime(), "yyyy-MM"));
        longtermPredictInputScaleDO.setCurCore(scaleInput.getCurCore());
        longtermPredictInputScaleDO.setBizRangeType(scaleInput.getBizRangeType());
        longtermPredictInputScaleDO.setCustomhouseTitle(scaleInput.getCustomhouseTitle());
        longtermPredictInputScaleDO.setIndustryDept(scaleInput.getIndustryDept());
        return longtermPredictInputScaleDO;
    }

}
