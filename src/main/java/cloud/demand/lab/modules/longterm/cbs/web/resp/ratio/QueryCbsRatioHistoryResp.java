package cloud.demand.lab.modules.longterm.cbs.web.resp.ratio;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryCbsRatioHistoryResp {

    List<TypeItem> typeItems = new ArrayList<>();

    @Data
    public static class TypeItem{
        private String type;
        List<RatioItem> ratioItemList= new ArrayList<>();

    }


    @Data
    public static class RatioItem {
        private String statTime;
        private String cbsInstanceFamily;
        private BigDecimal ratio;
        private String customhouseTitle;
        //private String bizRangeType;
    }
}
