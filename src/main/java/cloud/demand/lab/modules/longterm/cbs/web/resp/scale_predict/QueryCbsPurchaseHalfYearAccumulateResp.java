package cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryCbsPurchaseHalfYearAccumulateResp {

    List<StrategyItem> strategyItemList=new ArrayList<>();


    @Data
    public static class StrategyItem{
        private String strategyType;
        private List<HalfYearAccumulateItem> halfYearAccumulateItems=new ArrayList<>();
    }

    @Data
    public static class HalfYearAccumulateItem{
        private String startDate;
        private String endDate;
        private BigDecimal purchasePredictAccumulate;
    }
}
