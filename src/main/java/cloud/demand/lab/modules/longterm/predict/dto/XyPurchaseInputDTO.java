package cloud.demand.lab.modules.longterm.predict.dto;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputPurchaseWithIndustryDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

/**
 * 星云的采购数据
 */
@Data
public class XyPurchaseInputDTO {

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "campus")
    private String campus;

    @Column(value = "device_type")
    private String deviceType;

    @Column("industry")
    private String industry;

    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    @Column(value = "purchase_core_by_demand_month_delivered")
    private BigDecimal purchaseCoreByDemandMonthDelivered;

    @Column(value = "purchase_core_by_demand_month_total")
    private BigDecimal purchaseCoreByDemandMonthTotal;

    public static LongtermPredictInputPurchaseWithIndustryDO trans(XyPurchaseInputDTO input, Long taskId,
                                                                      Map<String, ZoneRegionInfoDTO> campus2ZoneInfoMap,
                                                                      Map<String, String> regionToCountryMap,
                                                                      Map<String, String> deviceTypeToInstanceTypeMap,
                                                                      Map<String, String> instanceTypeToInstanceFamily) {
        LongtermPredictInputPurchaseWithIndustryDO longtermPredictInputPurchaseDO = new LongtermPredictInputPurchaseWithIndustryDO();
        longtermPredictInputPurchaseDO.setTaskId(taskId);
        longtermPredictInputPurchaseDO.setStatTime(input.getStatTime());
        longtermPredictInputPurchaseDO.setYearMonthStr(DateUtils.format(input.getStatTime(), "yyyy-MM"));
        longtermPredictInputPurchaseDO.setCampus(input.getCampus());
        longtermPredictInputPurchaseDO.setIndustryDept(input.getIndustry());

        ZoneRegionInfoDTO zone = campus2ZoneInfoMap.get(input.getCampus());
        longtermPredictInputPurchaseDO.setZoneName(zone == null ? "未知" : zone.getZoneName());
        longtermPredictInputPurchaseDO.setRegionName(zone == null ? "未知" : zone.getRegionName());
        longtermPredictInputPurchaseDO.setCountryName(regionToCountryMap.getOrDefault(longtermPredictInputPurchaseDO.getRegionName(), "未知"));
        longtermPredictInputPurchaseDO.setCustomhouseTitle(zone == null ? "未知" : zone.getCustomhouseTitle());
        longtermPredictInputPurchaseDO.setDeviceType(input.getDeviceType());
        longtermPredictInputPurchaseDO.setInstanceType(deviceTypeToInstanceTypeMap.getOrDefault(input.getDeviceType(), "未知"));
        longtermPredictInputPurchaseDO.setInstanceFamily(instanceTypeToInstanceFamily.getOrDefault(longtermPredictInputPurchaseDO.getInstanceType(), "未知"));
        longtermPredictInputPurchaseDO.setPurchaseCore(input.getPurchaseCore());
        longtermPredictInputPurchaseDO.setPurchaseCoreByDemandMonthDelivered(input.getPurchaseCoreByDemandMonthDelivered());
        longtermPredictInputPurchaseDO.setPurchaseCoreByDemandMonthTotal(input.getPurchaseCoreByDemandMonthTotal());
        return longtermPredictInputPurchaseDO;
    }

}
