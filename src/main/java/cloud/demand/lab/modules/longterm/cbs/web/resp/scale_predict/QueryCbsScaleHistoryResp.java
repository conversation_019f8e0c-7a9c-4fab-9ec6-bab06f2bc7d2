package cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryCbsScaleHistoryResp {

    List<Item> itemList=new ArrayList<>();
    List<IncreaseRate> increaseRates=new ArrayList<>();
    @Data
    public static class Item{
        private String yearMonth;
        private BigDecimal curDisk;
        private String dimsName;
        private BigDecimal rate;
        private BigDecimal halfYearIncreaseRate;
    }

    @Data
    public static class IncreaseRate{
        private String halfYearName;
        private String startYearMonth;
        private String endYearMonth;
        private BigDecimal increaseRate;

        public IncreaseRate(String halfYearName, String startYearMonth, String endYearMonth, BigDecimal increaseRate) {
            this.halfYearName = halfYearName;
            this.startYearMonth = startYearMonth;
            this.endYearMonth = endYearMonth;
            this.increaseRate = increaseRate;
        }
    }
}
