package cloud.demand.lab.modules.longterm.cbs.service.Impl;

import cloud.demand.lab.modules.longterm.cbs.service.CbsCreateTaskAndSplitVersionService;
import cloud.demand.lab.modules.longterm.cbs.web.req.task_and_split.CreateTaskAndSplitVersionReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.task_and_split.CreateTaskAndSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermPredictService;
import cloud.demand.lab.modules.longterm.predict.service.QueryLongtermPredictTaskService;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import cloud.demand.lab.modules.longterm.predict.web.req.TaskIdAndSplitIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.CreateSplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QuerySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CbsCreateTaskAndSplitVersionServiceImpl implements CbsCreateTaskAndSplitVersionService {

    @Resource
    private SplitService splitService;

    @Resource
    private CreateLongtermPredictService createLongtermPredictService;

    @Resource
    private QueryLongtermPredictTaskService queryLongtermPredictTaskService;

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public CreateTaskAndSplitVersionResp createTaskAndSplitVersion(CreateTaskAndSplitVersionReq req) {

        //1.创建预测任务
        CreatePredictTaskReq createPredictTaskReq=new CreatePredictTaskReq();
        LongtermPredictCategoryConfigDO categoryConfigDO = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class, "where category like '方案300%'");
        createPredictTaskReq.setCategoryId(categoryConfigDO.getId());
        createPredictTaskReq.setIsEnable(true);
        createPredictTaskReq.setIsUseCache(req.getIsUseCache());
        createPredictTaskReq.setInputArgs(req.getInputArgs());
        CreatePredictTaskResp createPredictTaskResp = createLongtermPredictService.createPredictTask(createPredictTaskReq);
        Long cbsTaskId = createPredictTaskResp.getTaskId();


//        //2.查询cvm拆分版本
//        String predictStartMonth= req.getPredictStart();
//        QueryCategoryAndTaskListReq queryCategoryAndTaskListReq = new QueryCategoryAndTaskListReq();
//        //获取category为1的信息及其所属task
//        QueryCategoryAndTaskListResp queryCategoryAndTaskListResp = queryLongtermPredictTaskService.queryCategoryAndTaskList(queryCategoryAndTaskListReq);
//        Optional<QueryCategoryAndTaskListResp.Category> categoryOptional = queryCategoryAndTaskListResp.getCategoryList().stream().filter(o -> o.getCategoryId() == 1).findFirst();
//        QueryCategoryAndTaskListResp.Category cvmCategory;
//        QueryCategoryAndTaskListResp.Task cvmTask;
//        if (categoryOptional.isPresent()) {
//            // 获取cvm的Category
//            cvmCategory = categoryOptional.get();
//
//            // 在该 Category 下的 predictTaskList 中查找 yearMonth 等于 predictStartMonth 的 Task
//            Optional<QueryCategoryAndTaskListResp.Task> taskOptional = cvmCategory.getPredictTaskList()
//                    .stream()
//                    .filter(task -> task.getYearMonth().equals(predictStartMonth))  // 过滤出 yearMonth 等于 predictStartMonth 的 Task
//                    .findFirst();  // 获取第一个符合条件的 Task
//
//            // 如果找到了对应的 Task
//            if (taskOptional.isPresent()) {
//                cvmTask = taskOptional.get();
//            } else {
//                // 如果没有找到 yearMonth 等于 predictStartMonth 的 Task，找到最新的 Task（yearMonth 最新的）
//                Optional<QueryCategoryAndTaskListResp.Task> latestTaskOptional = cvmCategory.getPredictTaskList()
//                        .stream()
//                        .max(Comparator.comparing(QueryCategoryAndTaskListResp.Task::getYearMonth));  // 按 yearMonth 降序找最新的 Task
//                if (latestTaskOptional.isPresent()) {
//                    cvmTask = latestTaskOptional.get();
//                } else {
//                    throw new RuntimeException("没有找到对应的 Task");
//                }
//            }
//        } else {
//            throw new RuntimeException("没有找到 categoryId 为 1 的 Category");
//        }
//        // 3. 等待拆分版本创建
//        boolean splitVersionCreated = false;
//        int attemptCount = 0;
//        final int maxAttempts = 20;  // 设置最多检查次数
//        final int waitIntervalMillis = 3000;  // 每次检查间隔 3 秒
//        while (attemptCount < maxAttempts && !splitVersionCreated) {
//            LongtermPredictOutputSplitVersionDO splitVersionDO = cdLabDbHelper.getOne(LongtermPredictOutputSplitVersionDO.class, "where task_id=? and name =?",
//                    cbsTaskId, "默认拆分版本");
//            // 如果已有拆分版本，跳出循环
//            if (splitVersionDO!=null) {
//                splitVersionCreated = true;
//            } else {
//                attemptCount++;
//                try {
//                    Thread.sleep(waitIntervalMillis);  // 等待一段时间再检查
//                    log.info("等待创建拆分版本，当前检查次数：" + attemptCount);
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                }
//            }
//        }
//
//
//            //获取cvmtask下最新的拆分版本
//        QuerySplitVersionReq querySplitVersionReq = new QuerySplitVersionReq();
//        querySplitVersionReq.setTaskId(cvmTask.getTaskId());
//        QuerySplitVersionResp querySplitVersionResp = splitService.querySplitVersionList(querySplitVersionReq);
//        List<QuerySplitVersionResp.SplitVersion> cvmSplitVersionList = querySplitVersionResp.getSplitVersionList();
//        if(cvmSplitVersionList.isEmpty())throw new RuntimeException("没有找到对应的拆分版本");
//        QuerySplitVersionResp.SplitVersion cvmSplitVersion = cvmSplitVersionList.get(0);
//
//        CreateSplitVersionReq createSplitVersionReq = splitService.queryCreateSplitVersionReq(cvmSplitVersion.getSplitVersionId());
////        createSplitVersionReq.setTaskId(cbsTaskId);
//        //3.新建一份cbs关联的拆分版本
//        createSplitVersionReq.setTaskId(cbsTaskId);
//        createSplitVersionReq.setName("CBS拆分");
//        //4.执行拆分
//        CreateSplitVersionResp createSplitVersionResp = splitService.splitData(createSplitVersionReq);
//
        CreateTaskAndSplitVersionResp resp=new CreateTaskAndSplitVersionResp();
//        resp.setSplitVersionId(createSplitVersionResp.getSplitVersionId());
        resp.setTaskId(cbsTaskId);
        return resp;
    }

    @Override
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req) {
        // 从服务中获取响应结果
        QueryCategoryAndTaskListResp resp = queryLongtermPredictTaskService.queryCategoryAndTaskList(req);

        // 对类别列表进行排序，优先显示"方案300"（如果有相同最新日期）
        resp.getCategoryList().sort((category1, category2) -> {
            // 获取每个类别的最新任务日期（yearMonth）
            String latestDate1 = getLatestDate(category1);
            String latestDate2 = getLatestDate(category2);

            // 如果两个类别的最新日期相同，优先将“方案300”排在前面
            if (latestDate1.equals(latestDate2)) {
                if (category1.getCategoryName().equals("方案300：CBS预测专用") && !category2.getCategoryName().equals("方案300：CBS预测专用")) {
                    return -1; // "方案300"排在前面
                } else if (category2.getCategoryName().equals("方案300：CBS预测专用") && !category1.getCategoryName().equals("方案300：CBS预测专用")) {
                    return 1;
                } else {
                    return 0; // 如果都不是“方案300”，保持原顺序
                }
            }

            // 如果最新日期不同，按日期排序（降序）
            return latestDate2.compareTo(latestDate1);
        });

        // 对每个类别内的任务按`yearMonth`降序排序
        for (QueryCategoryAndTaskListResp.Category category : resp.getCategoryList()) {
            category.getPredictTaskList().sort((task1, task2) -> task2.getYearMonth().compareTo(task1.getYearMonth()));
        }

        resp.getCategoryList().removeIf(o->
                (!o.getCategoryName().equals("方案300：CBS预测专用"))&&(!o.getCategoryName().equals("方案100：全量不分客户"))
        );
        return resp;

    }

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        QueryCategoryForCreateResp resp= createLongtermPredictService.queryCategoryForCreate(req);
        List<QueryCategoryForCreateResp.Item> categoryList = resp.getCategoryList();
        categoryList.removeIf(o->
                (!o.getCategoryName().equals("方案300：CBS预测专用"))
        );


        return resp;
    }

    // 获取类别最新的预测日期（yearMonth）
    private String getLatestDate(QueryCategoryAndTaskListResp.Category category) {
        if (category.getPredictTaskList() == null || category.getPredictTaskList().isEmpty()) {
            return ""; // 如果没有任务，返回空字符串
        }

        // 返回最新的任务日期
        return category.getPredictTaskList().get(0).getYearMonth();
    }
}
