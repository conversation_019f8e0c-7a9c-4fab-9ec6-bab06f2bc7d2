package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRateExcelDTO;
import cloud.demand.lab.modules.longterm.predict.web.entity.InstanceToDeviceItem;
import cloud.demand.lab.modules.longterm.predict.web.req.TaskIdAndSplitIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.BigCustomerExcelDTO;
import cloud.demand.lab.modules.longterm.predict.web.req.split.ConfigSplitRateDiffReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.CreateSplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DeviceTypePurchaseDataSplit;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.IndustryDeptPurchaseDataSplit;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.ScaleDataSplit;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.longterm.predict.web.req.split.InstanceToDeviceReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QueryDeviceTypeSplitDetailReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QuerySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.UpdateSplitRstReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.DownLoadExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.Instance2DeviceRateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.InstanceToDeviceResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ParseBigCustomInfoExcelResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QueryDeviceTypeSplitDetailResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

public interface SplitService {


    /**
     * 默认数据拆分3份数据
     * 任务完成调用这个接口
     *
     * @param req req参数
     */
    void splitWithOutRet(CreateSplitVersionReq req);


    /**
     * 前端调用这个接口
     *
     * @param req req参数
     */
    CreateSplitVersionResp splitData(CreateSplitVersionReq req);


    /**
     * 查询拆分版本列表
     * @param req req
     * @return list
     */
    QuerySplitVersionResp querySplitVersionList(QuerySplitVersionReq req);
    /**
     * 拆分行业那份数据
     *
     * @param versionDO version
     * @return list
     */
    List<IndustryDeptPurchaseDataSplit> splitIndustryDeptPurchase(
            LongtermPredictOutputSplitVersionDO versionDO, CreateSplitVersionReq req);

    /**
     * 拆分机型的数据
     *
     * @param versionDO version
     * @param req 指定拆分比例，和大客户数据
     * @return list
     */
    List<DeviceTypePurchaseDataSplit> splitDevicePurchase(
            LongtermPredictOutputSplitVersionDO versionDO, CreateSplitVersionReq req);


    /**
     * 拆分历史规模数据
     *
     * @return list
     */
    List<ScaleDataSplit> splitScale(LongtermPredictOutputSplitVersionDO splitVersion, CreateSplitVersionReq req);

    /**
     * 查询cvm到物理机的拆分比例
     * @return resp
     */
    List<Instance2DeviceRateResp> queryInstance2DeviceRate();

    /**
     * excel 导入配置数据
     * @param req req
     * @param excelDataList excel parsed
     * @return resp
     */
    InstanceToDeviceResp uploadInstance2DeviceRateExcel(InstanceToDeviceReq req, List<InstanceToDeviceItem> excelDataList);

    /**
     * 更新拆分后的结果数据
     *
     * @param req req
     * @return info
     */
    ImmutableMap<String, Integer> updateSplitRstById(UpdateSplitRstReq req);

    /**
     *
     * @param req req
     * @return ret
     */
    QueryDeviceTypeSplitDetailResp queryDeviceTypeSplitDetail(QueryDeviceTypeSplitDetailReq req);

    /**
     * 查询拆分后的三份数据
     * @param req req
     * @return resp
     */
    CreateSplitVersionTableResp querySplitData(TaskIdAndSplitIdReq req);


    /**
     * 返回不同的数据
     * @param req req
     * @return diff
     */
    InstanceToDeviceResp saveInstance2DeviceRateToDbPreview(InstanceToDeviceReq req);


    /**
     * 查询4份配置表
     * @return resp
     */
    ConfigSplitRateResp queryConfigSplitRate(Long taskId);


    /**
     * excel 是动态的列,和 task 映射
     * @param excel excel
     * @param taskId task
     * @return list
     */
    List<InstanceFamilyToDeviceTypeRate> tansByTask(List<InstanceFamilyToDeviceTypeRateExcelDTO> excel, Long taskId);

    /**
     * 比较2个数据的差异
     * @param web 页面当前的数据
     * @param excel excel 的数据
     * @return resp
     */
    ConfigSplitRateDiffResp diffExcelConfigSplitRate(ConfigSplitRateDiffReq web, ConfigSplitRateDiffReq excel);

    /**
     * 比较和db中的差异
     * @param req web 页面的数据
     * @return resp
     */
    ConfigSplitRateDiffResp diffDbConfigSplitRate(ConfigSplitRateDiffReq req);

    /**
     * 全量覆盖保存到db中
     * @param req req
     * @return ret
     */
    ConfigSplitRateDiffResp saveConfigSplitRate(ConfigSplitRateDiffReq req);

    /**
     * 可用区到 regionOrCountry
     * @return map
     */
    Map<String, String> zoneNameToRegionOrCountry();

    /**
     * 解析大客户的excel 数据
     *
     * @param excelDataList excel
     * @param taskId 当前任务id
     * @return resp
     */
    ParseBigCustomInfoExcelResp parseBigCustomInfoExcel(MultipartFile excelFile, Long taskId);

    /**
     *  查询大客户的数据
     * @param req req
     * @return resp
     */
    ParseBigCustomInfoExcelResp queryBigCustomInfo(TaskIdAndSplitIdReq req);

    /**
     * 查询拆分时候的请求参数
     * @param splitVersionId id
     * @return req
     */
    CreateSplitVersionReq queryCreateSplitVersionReq(Long splitVersionId);

    /**
     * 删除一个拆分版本
     * @param splitVersionId id
     * @return ret
     */
    ImmutableMap<String, Object> deleteSplitVersionById(Long splitVersionId);

    /**
     * 生产大客户的excel 数据
     * @param req req
     * @return ret
     */
    DownloadBean downloadBigCustomInfoExcel(DownLoadExcelReq req);
}
