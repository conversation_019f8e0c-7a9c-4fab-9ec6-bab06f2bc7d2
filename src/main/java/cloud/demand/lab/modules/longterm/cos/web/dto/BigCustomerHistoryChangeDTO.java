package cloud.demand.lab.modules.longterm.cos.web.dto;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 大客户历史变动数据DTO
 * 只包含主要业务字段，不包含软删除字段、createTime、updateTime等
 */
@Data
public class BigCustomerHistoryChangeDTO {

    /**
     * 方案id
     */
    private Long categoryId;

    /**
     * 任务id，当值为0时，表示是当前category_id方案下的默认大客户历史数据
     */
    private Long taskId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 是否外部客户，0表示否，1表示是
     */
    private Boolean isOutCustomer;

    /**
     * 变动开始时间
     */
    private LocalDate startDate;

    /**
     * 变动结束时间
     */
    private LocalDate endDate;

    /**
     * 变化量(单位PB)
     */
    private BigDecimal netChange;

    /**
     * 从DO转换为DTO
     */
    public static BigCustomerHistoryChangeDTO fromDO(CosLongtermPredictInputBigCustomerChangeDO dO) {
        if (dO == null) {
            return null;
        }
        
        BigCustomerHistoryChangeDTO dto = new BigCustomerHistoryChangeDTO();
        dto.setCategoryId(dO.getCategoryId());
        dto.setTaskId(dO.getTaskId());
        dto.setCustomerName(dO.getCustomerName());
        dto.setIsOutCustomer(dO.getIsOutCustomer());
        dto.setStartDate(dO.getStartDate());
        dto.setEndDate(dO.getEndDate());
        dto.setNetChange(dO.getNetChange());
        return dto;
    }

    /**
     * 转换为DO
     */
    public CosLongtermPredictInputBigCustomerChangeDO toDO() {
        CosLongtermPredictInputBigCustomerChangeDO dO = new CosLongtermPredictInputBigCustomerChangeDO();
        dO.setCategoryId(this.categoryId);
        dO.setTaskId(this.taskId);
        dO.setCustomerName(this.customerName);
        dO.setIsOutCustomer(this.isOutCustomer);
        dO.setStartDate(this.startDate);
        dO.setEndDate(this.endDate);
        dO.setNetChange(this.netChange);
        return dO;
    }
}
