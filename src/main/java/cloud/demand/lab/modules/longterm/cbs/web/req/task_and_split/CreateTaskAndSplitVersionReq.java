package cloud.demand.lab.modules.longterm.cbs.web.req.task_and_split;

import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.InputArgsDTO;
import lombok.Data;

import java.util.List;

@Data
public class CreateTaskAndSplitVersionReq {

    /**用于查询cvm对应的task版本与拆分版本,yyyy-mm*/
    private String predictStart;

    /*createPredictTask 的全部输入，用于创建预测任务*/
    /**方案id*/
    private Long categoryId;
    /**是否直接设置为启用，如果没有传，默认不启用*/
    private Boolean isEnable;
    /**是否使用缓存，默认true使用，如果需要强制重新读取input数据，请设置为false*/
    private Boolean isUseCache;

    /**方案输入参数*/
    private List<InputArgsDTO> inputArgs;
}
