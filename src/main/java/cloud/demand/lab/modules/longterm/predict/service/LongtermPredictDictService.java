package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.longterm.predict.dto.ZoneRegionInfoDTO;

import cloud.demand.lab.modules.longterm.predict.service.impl.LongtermPredictDictServiceImpl.FullCampusBaseDictData;
import java.util.Map;

/**
 * 专门用于中长期预测的字典表，一般是特别处理的，补全了数据
 */
public interface LongtermPredictDictService {

    /**
     * 查询物理机设备类型到实例类型的映射map，例如 T0-CS81X-100GS -> S8
     *
     * 这个表加了特殊逻辑,会取用配置表的映射关系
     */
    Map<String, String> getDeviceTypeToInstanceTypeMap();

    /**
     * 查询物理机设备类型到实例类型的映射map，例如 T0-CS81X-100GS -> 标准型
     *
     * 这个表是配置表的特殊逻辑
     */
    Map<String, String> getDeviceTypeToInstanceFamilyMap();

    /**
     * 查询campus到可用区信息的映射map，例如 上海-万荣 -> 上海四区
     */
    Map<String, ZoneRegionInfoDTO> getCampusToZoneInfo();

    /**
     * 增加爱了特殊处理
     * 新开区的映射后续会做到这里
     *
     * @return map
     */
    Map<String, String> getZoneName2RegionName();

    /**
     * 增加了特殊处理
     * 新开区的配置后续会做到这里
     *
     * @return map
     */
    Map<String, SoeRegionNameCountryDO> getRegionNameInfoMap();

    /**
     * 查询 zoneName 到 campus 的映射map ， 例如 广州一区 -> 广州-清远
     *
     * @return map
     */
    Map<String, String> getZoneNameToCampus(Long taskId);

    /**
     * 查询物理机的核心数
     */
    Map<String, Integer> getDeviceTypeToCoreNumMap();


    /**
     * 获取 campus 到 ERP zone 和 region 的映射信息
     *
     * @return map
     */
    Map<String, FullCampusBaseDictData> getCampusToErpRegionInfoMap();
}
