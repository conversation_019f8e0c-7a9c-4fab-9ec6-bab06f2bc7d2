package cloud.demand.lab.modules.longterm.cos.web.resp;

import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class QueryCategoryForCreateResp {

    /**方案列表*/
    private List<Item> categoryList;

    @Data
    public static class Item {
        /**方案id*/
        private Long categoryId;
        /**方案名称*/
        private String categoryName;

        /**预测开始日期，yyyy-MM格式*/
        private String predictStart;
        /**预测结束日期，yyyy-MM格式*/
        private String predictEnd;

        /**输入参数的日期范围列表*/
        private List<InputArgDateRange> inputArgDateRanges;

        /**策略枚举*/
        private List<StrategyType> strategyTypes;
    }

    @Data
    public static class InputArgDateRange {
        /**日期名称*/
        private String dateName;
        /**开始日期*/
        private String startDate;
        /**结束日期*/
        private String endDate;
    }

    @Data
    public static class StrategyType {
        /**策略代号*/
        private String strategyTypeCode;
        /**策略名称，例如激进、中立、保守*/
        private String strategyTypeName;

        public static StrategyType from(StrategyTypeEnum e) {
            StrategyType strategyType = new StrategyType();
            strategyType.setStrategyTypeCode(e.getCode());
            strategyType.setStrategyTypeName(e.getName());
            return strategyType;
        }
    }
}
