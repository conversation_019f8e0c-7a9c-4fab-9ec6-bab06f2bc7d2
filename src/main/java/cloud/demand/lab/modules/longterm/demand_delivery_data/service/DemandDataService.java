package cloud.demand.lab.modules.longterm.demand_delivery_data.service;

import cloud.demand.lab.modules.longterm.demand_delivery_data.entity.LongtermDemandAndExecuteDataDO;

import java.util.List;

public interface DemandDataService {

    /**
     * 查询到所有需求的数据
     */
    List<LongtermDemandAndExecuteDataDO> getAllDemand();


    /**
     * 查询到所有退回的数据
     */
    List<LongtermDemandAndExecuteDataDO> getAllReturn();

}
