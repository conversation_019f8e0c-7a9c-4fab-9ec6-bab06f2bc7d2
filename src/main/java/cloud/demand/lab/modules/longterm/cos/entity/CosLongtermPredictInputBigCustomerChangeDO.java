package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * cos中长期大客户执行量
 */
@Data
@ToString
@Table("cos_longterm_predict_input_big_customer_change")
public class CosLongtermPredictInputBigCustomerChangeDO extends BaseDO  {

    /** 方案id<br/>Column: [category_id] */
    @Column(value = "category_id")
    private Long categoryId;

    /** 任务id，当值为0时，表示是当前category_id方案下的默认大客户历史数据<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 是否外部客户，0表示否，1表示是<br/>Column: [is_out_customer] */
    @Column(value = "is_out_customer")
    private Boolean isOutCustomer;

    /** 变动开始时间<br/>Column: [start_date] */
    @Column(value = "start_date")
    private LocalDate startDate;

    /** 变动结束时间<br/>Column: [end_date] */
    @Column(value = "end_date")
    private LocalDate endDate;

    /** 变化量(单位PB)<br/>Column: [net_change] */
    @Column(value = "net_change")
    private BigDecimal netChange;

}