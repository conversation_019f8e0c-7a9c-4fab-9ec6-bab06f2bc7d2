package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;

@Data
@ToString
public class PurchaseHistoryAndPredictTotalDTO {

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "year")
    private Integer year;

    @Column(value = "strategy_type")
    private String strategyType;

    @Column(value = "finish_purchase_core")
    private BigDecimal finishPurchaseCore;

    @Column(value = "predict_purchase_core")
    private BigDecimal predictPurchaseCore;

}