package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_output_scale_split_tree")
public class LongtermPredictOutputScaleSplitTreeDO extends BaseDO {

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "input_args_id")
    private Long inputArgsId;

    @Column(value = "split_version_id")
    private Long splitVersionId;

    @Column(value = "strategy_type")
    private String strategyType;

    /** scale id<br/>Column: [scale_id] */
    @Column(value = "scale_id")
    private Long scaleId;

    @Column(value = "tree_parent")
    private Long treeParent;

    @Column(value = "tree_id")
    private Long treeId;

    @Column(value = "tree_index")
    private Long treeIndex;

    @Column(value = "tree_level")
    private Long treeLevel;

    @Column(value = "tree_note")
    private String treeNote;

    @Column(value = "date")
    private LocalDate date;

    @Column(value = "year")
    private Integer year;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "half_year")
    private Integer halfYear;

    @Column(value = "cur_core")
    private BigDecimal curCore;

    @Column(value = "cur_core_origin")
    private BigDecimal curCoreOrigin;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 国家名字<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 内外部客户<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** 机型大类<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /**
     * 机型大类<br/>Column: [instance_family]
     */
    @Column(value = "instance_type")
    private String instanceType;
    
}