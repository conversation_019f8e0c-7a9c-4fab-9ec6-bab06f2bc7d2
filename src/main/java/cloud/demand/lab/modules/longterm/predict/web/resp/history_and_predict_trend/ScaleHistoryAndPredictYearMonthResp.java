package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view.PredictHistoryYearMonthVO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScaleHistoryAndPredictYearMonthResp {
    private List<Item> items=new ArrayList<>();

    private String message;



    @Data
    public static class Item{
        private String yearMonth;
        private long taskId;
        private List<splitVersionItem> splitVersionItems;
    }

    @Data
    public static class splitVersionItem{
        private long splitVersionId;
        private String splitVersionName;
    }

    public List<Item> transItem(List<PredictHistoryYearMonthVO> dtos){
        List<Item> ret = new ArrayList<>();
        for (PredictHistoryYearMonthVO dto: dtos){
            Item item=new Item();
            item.setTaskId(Long.valueOf(dto.getId()));
            item.setYearMonth(dto.getPredictStart().toString().substring(0,7));
            item.setSplitVersionItems(transSplitVersionItem(dto.getLongtermPredictOutputSplitVersionDOs()));
            ret.add(item);
        }
        return ret;
    }

    public List<splitVersionItem> transSplitVersionItem(List<LongtermPredictOutputSplitVersionDO> versionDOS){
        List<splitVersionItem> ret = new ArrayList<>();
        for(LongtermPredictOutputSplitVersionDO versionDO: versionDOS){
            splitVersionItem item=new splitVersionItem();
            item.setSplitVersionId(Long.valueOf(versionDO.getId()));
            item.setSplitVersionName(versionDO.getName());
            ret.add(item);
        }
        return ret;
    }

    public ScaleHistoryAndPredictYearMonthResp(String message) {
        this.message = message;
    }

    public ScaleHistoryAndPredictYearMonthResp() {
    }
}
