package cloud.demand.lab.modules.longterm.demand_delivery_data.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;
import java.time.LocalDate;

@Data
@ToString
@Table("res_plan_holiday_week")
public class ResPlanHolidayWeekDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 月份<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 周次<br/>Column: [week] */
    @Column(value = "week")
    private Integer week;

    /** 起始日<br/>Column: [start] */
    @Column(value = "start")
    private LocalDate start;

    /** 结束日<br/>Column: [end] */
    @Column(value = "end")
    private LocalDate end;

    /** 是否节假日， 1为是，0为否<br/>Column: [is_holiday] */
    @Column(value = "is_holiday")
    private Integer isHoliday;

    @Column(value = "create_time")
    private Timestamp createTime;

    /** 生成到哪一年的基准值<br/>Column: [bench_year] */
    @Column(value = "bench_year")
    private Integer benchYear;

    /** 生成到哪一月的基准值<br/>Column: [bench_month] */
    @Column(value = "bench_month")
    private Integer benchMonth;

}