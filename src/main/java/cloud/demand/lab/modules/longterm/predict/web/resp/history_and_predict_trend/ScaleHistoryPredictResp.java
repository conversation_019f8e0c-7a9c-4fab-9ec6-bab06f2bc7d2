package cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 添加预测部分返回体
 */
@Data
public class ScaleHistoryPredictResp {


    private List<PredictStrategyTypeItem> predictStrategyTypeItems=new ArrayList<>();

    private String message;



    @Data
    public static class PredictStrategyTypeItem{
        private String strategyType;
        private List<Item> items=new ArrayList<>();
        private List<IncreaseRate> increaseRates =new ArrayList<>();
    }

    @Data
    public static class Item{
        private String yearMonth;
        private BigDecimal curCore;
        private String dimsName;
        //占比
        private BigDecimal rate;
    }

    //半年的增速
    @Data
    public static class IncreaseRate{
        private String startYearMonth;
        private String endYearMonth;
        private BigDecimal increaseRate;
        //上下半年展示名称
        private String dateName;
    }
    public ScaleHistoryPredictResp() {
    }

    public ScaleHistoryPredictResp(String message) {
        this.message = message;
    }

}
