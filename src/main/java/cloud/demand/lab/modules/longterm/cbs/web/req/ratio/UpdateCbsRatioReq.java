package cloud.demand.lab.modules.longterm.cbs.web.req.ratio;

import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class UpdateCbsRatioReq {
    //要更改的配比方案id
    private Long ratioCategoryId;
    //配比方案名称
    private String name;
    //备注
    private String note=null;
    //明细
    private List<CbsRatioDetailDO> dos=new ArrayList<>();

}
