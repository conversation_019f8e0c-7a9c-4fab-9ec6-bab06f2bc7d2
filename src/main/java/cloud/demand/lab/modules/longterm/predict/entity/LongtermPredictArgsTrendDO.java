package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("longterm_predict_args_trend")
public class LongtermPredictArgsTrendDO extends BaseDO {

    @Column(value = "category_id")
    private Long categoryId;

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 统计周期的起始时间点<br/>Column: [start_date] */
    @Column(value = "start_date")
    private LocalDate startDate;

    /** 统计周期的结束时间点<br/>Column: [end_date] */
    @Column(value = "end_date")
    private LocalDate endDate;

    /** 存量核心数，开始时间点<br/>Column: [start_cur_core] */
    @Column(value = "start_cur_core")
    private BigDecimal startCurCore;

    /** 存量核心数，结束时间点<br/>Column: [end_cur_core] */
    @Column(value = "end_cur_core")
    private BigDecimal endCurCore;

    /** 净增核心数，即end_cur_core-start_cur_core<br/>Column: [net_change_core] */
    @Column(value = "net_change_core")
    private BigDecimal netChangeCore;

    /** 机型规格+可用区维度下的新增核心数<br/>Column: [new_core_by_instance_model_and_zone] */
    @Column(value = "new_core_by_instance_model_and_zone")
    private BigDecimal newCoreByInstanceModelAndZone;

    /** 采购核心数<br/>Column: [purchase_core] */
    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    /** 期初的线上库存核心数<br/>Column: [start_online_stock_core] */
    @Column(value = "start_online_stock_core")
    private Integer startOnlineStockCore;

    /** 期末的线上库存核心数<br/>Column: [end_online_stock_core] */
    @Column(value = "end_online_stock_core")
    private Integer endOnlineStockCore;

    /** 新旧替换比例<br/>Column: [replace_rate] */
    @Column(value = "replace_rate")
    private BigDecimal replaceRate;

    /** 采购系数<br/>Column: [purchase_rate] */
    @Column(value = "purchase_rate")
    private BigDecimal purchaseRate;

    /** 端到端利用率，这个是没有百分百的<br/>Column: [p2p_rate] */
    @Column(value = "p2p_rate")
    private BigDecimal p2pRate;

}