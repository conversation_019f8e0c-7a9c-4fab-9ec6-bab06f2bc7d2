package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ToString
@Table("cos_longterm_predict_task")
public class CosLongtermPredictTaskDO extends BaseDO  {

    /** 方案id<br/>Column: [category_id] */
    @Column(value = "category_id")
    private Long categoryId;

    /** 方案名称<br/>Column: [category_name] */
    @Column(value = "category_name")
    private String categoryName;

    /** 是否启用<br/>Column: [is_enable] */
    @Column(value = "is_enable")
    private Boolean isEnable;

    /** 预测构成，逗号分隔，例如：快手,非快手<br/>Column: [parts] */
    @Column(value = "parts")
    private String parts;

    /** 用于模型预测的部分的名称，只有一个，例如：非快手<br/>Column: [model_part] */
    @Column(value = "model_part")
    private String modelPart;

    /** 是否区分内外部，0=不分开，1=分开；主要用于后续自研COS这种无法区分内外部的用<br/>Column: [is_separate_in_out] */
    @Column(value = "is_separate_in_out")
    private Boolean isSeparateInOut;

    /** 任务状态<br/>Column: [task_status] */
    @Column(value = "task_status")
    private String taskStatus;

    /** 创建者<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 预测开始日期，一般以月末日期为代表<br/>Column: [predict_start] */
    @Column(value = "predict_start")
    private LocalDate predictStart;

    /** 预测结束日期，一般以月末日期为代表<br/>Column: [predict_end] */
    @Column(value = "predict_end")
    private LocalDate predictEnd;

    /** 条件sql，一般情况下是不含时间范围的，它仅是业务范围<br/>Column: [condition_sql] */
    @Column(value = "condition_sql")
    private String conditionSql;

    /** 实际产生输入的sql<br/>Column: [input_sql] */
    @Column(value = "input_sql")
    private String inputSql;

    /** 采购量的输入数据实际sql<br/>Column: [purchase_input_sql] */
    @Column(value = "purchase_input_sql")
    private String purchaseInputSql;

    /** 输入数据是否使用缓存，0不使用（即等于强制更新），1使用（默认）<br/>Column: [use_cache] */
    @Column(value = "use_cache")
    private Boolean useCache;

    /** 规模输入是否实际用了缓存<br/>Column: [input_actual_use_cache] */
    @Column(value = "input_actual_use_cache")
    private Boolean inputActualUseCache;

    /** 采购量输入是否实际用了缓存<br/>Column: [purchase_input_actual_use_cache] */
    @Column(value = "purchase_input_actual_use_cache")
    private Boolean purchaseInputActualUseCache;

    /** 预测粒度<br/>Column: [dims_name] */
    @Column(value = "dims_name")
    private String dimsName;

    /** 资源池<br/>Column: [scope_resource_pool] */
    @Column(value = "scope_resource_pool")
    private String scopeResourcePool;

    /** 客户范围<br/>Column: [scope_customer] */
    @Column(value = "scope_customer")
    private String scopeCustomer;

    /** 预测时间颗粒度，6=半年<br/>Column: [interval_month] */
    @Column(value = "interval_month")
    private Integer intervalMonth;

    /** 任务运行失败的错误信息<br/>Column: [err_msg] */
    @Column(value = "err_msg")
    private String errMsg;

    /** 任务运行开始时间<br/>Column: [task_start_time] */
    @Column(value = "task_start_time")
    private LocalDateTime taskStartTime;

    /** 任务运行结束时间<br/>Column: [task_end_time] */
    @Column(value = "task_end_time")
    private LocalDateTime taskEndTime;

    /** 预测任务执行（不含拆分）的毫秒数，只有成功跑完才写入数据，失败不写<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 拆分的耗时，只有成功跑完才写入数据，失败不写，只记录第一次默认拆分的耗时<br/>Column: [split_cost_ms] */
    @Column(value = "split_cost_ms")
    private Integer splitCostMs;

    /** 运行任务的ip地址<br/>Column: [run_ip] */
    @Column(value = "run_ip")
    private String runIp;

    /** 任务备注<br/>Column: [run_note] */
    @Column(value = "run_note")
    private String runNote;

}