package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ScaleHistoryAndPredictTotalPredictDTO {

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("cur_core")
    private BigDecimal curCore;

    @Column("biz_range_type")
    private String bizRangeType;

    @Column("year_month_str")
    private String yearMonthStr;

    @Column("strategy_type")
    private String strategyType;

    @Column("date")
    private String statTime;
}
