package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@ToString
@Table("longterm_predict_input_args")
public class LongtermPredictInputArgsDO extends BaseDO {

    /** 预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 策略类型，激进中立保守<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 日期范围名称<br/>Column: [date_name] */
    @Column(value = "date_name")
    private String dateName;

    /** 日期开始，含当天<br/>Column: [start_date] */
    @Column(value = "start_date")
    private LocalDate startDate;

    /** 日期结束，含当天<br/>Column: [end_date] */
    @Column(value = "end_date")
    private LocalDate endDate;

    /** 国家名称<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 净增长，不含百分比<br/>Column: [scale_growth_rate] */
    @Column(value = "scale_growth_rate")
    private BigDecimal scaleGrowthRate;

    /** 净增长核心数<br/>Column: [net_growth_core] */
    @Column(value = "net_growth_core")
    private Integer netGrowthCore;

    /** 替换比例，不含百分比<br/>Column: [replace_rate] */
    @Column(value = "replace_rate")
    private BigDecimal replaceRate;

    /** 采购系数，不含百分比<br/>Column: [purchase_rate] */
    @Column(value = "purchase_rate")
    private BigDecimal purchaseRate;

    /** 备注<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 附件，["",""] json格式<br/>Column: [attachment] */
    @Column(value = "attachment", isJSON = true)
    private List<Attachment> attachment;

    @Data
    public static class Attachment {
        private String filename;
        private String url;
    }

    public static DBHelperWithClz<LongtermPredictInputArgsDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }
}