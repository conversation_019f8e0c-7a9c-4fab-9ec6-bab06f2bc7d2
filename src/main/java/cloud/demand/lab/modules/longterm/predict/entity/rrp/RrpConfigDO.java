package cloud.demand.lab.modules.longterm.predict.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ToString
@Table("rrp_config")
public class RrpConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "startTime")
    private LocalDate startTime;

    @Column(value = "endTime")
    private LocalDate endTime;

    @Column(value = "required_month")
    private String requiredMonth;

    @Column(value = "optional_month")
    private String optionalMonth;

    /** 上个版本有，当前版本没有的需求月份，即需要自动延期的月份<br/>Column: [auto_extend_month] */
    @Column(value = "auto_extend_month")
    private String autoExtendMonth;

    @Column(value = "description")
    private String description;

    /** 0是1否<br/>Column: [status] */
    @Column(value = "status")
    private Integer status;

    @Column(value = "updateTime")
    private LocalDateTime updateTime;

    @Column(value = "plan_version")
    private String planVersion;

    /** 是否通知产品录单人<br/>Column: [is_notify] */
    @Column(value = "is_notify")
    private Integer isNotify;

    /** 是否自动根据执行量修复预测量<br/>Column: [is_fix_executed_num] */
    @Column(value = "is_fix_executed_num")
    private Integer isFixExecutedNum;

    /** 13周行业版本号<br/>Column: [industry_version] */
    @Column(value = "industry_version")
    private String industryVersion;

    /** 创建人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 最近一次更新人<br/>Column: [updator] */
    @Column(value = "updator")
    private String updator;

    /** DB 的创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDateTime createTime;

}