package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class PurchaseHistoryTrendViewDTO {

    @Column(value = "year_month_str")
    private String yearMonth;

    @Column(value = "purchase_core")
    private BigDecimal purchaseCore;

    @Column(value = "dim")
    private String dim;

}
