package cloud.demand.lab.modules.longterm.cbs.service.Impl;

import cloud.demand.lab.modules.longterm.cbs.dto.*;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import cloud.demand.lab.modules.longterm.cbs.service.CbsPurchasePredictService;
import cloud.demand.lab.modules.longterm.cbs.service.CbsRatioService;
import cloud.demand.lab.modules.longterm.cbs.service.CbsScalePredictService;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsScalePredictTotalResp.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryScalePredictResp.PredictStrategyTypeItem;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsScaleHistoryResp.Item;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
@Slf4j
@Service
public class CbsScalePredictServiceImpl implements CbsScalePredictService {

    @Resource
    private CbsRatioService cbsRatioService;

    @Resource
    private CbsPurchasePredictService cbsPurchasePredictService;

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper ckstdcrpDBHelper;


    /**
     * 左侧方框
     * @param req
     * @return
     */
    @SneakyThrows
    @Override
    public QueryCbsScalePredictTotalResp queryCbsScalePredictTotal(QueryCbsScalePredictTotalReq req) {
        // 构建响应对象
        QueryCbsScalePredictTotalResp resp = new QueryCbsScalePredictTotalResp();
        //1. 获取长期预测任务信息
        LongtermPredictTaskDO longtermPredictTaskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id = ?", req.getTaskId());
        if(longtermPredictTaskDO==null)throw new RuntimeException("查询不到预测认为信息，请检查categoryId"+req.getRatioCategoryId());
        //生成sql查询的参数
        Map<String, Object> params = getPurchaseParams(req, longtermPredictTaskDO);
        Map<String, BigDecimal> scaleRatioMap = cbsRatioService.getScaleRatios(req.getTaskId(), req.getSplitVersionId());
        // 获取配比方案明细
        List<CbsRatioDetailDO> ratioDetailList = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id=?", req.getRatioCategoryId());
        if(ratioDetailList==null||ratioDetailList.isEmpty())throw new RuntimeException("查询不到配比方案明细"+req.getRatioCategoryId()+"下的配比明细");
        Map<String, BigDecimal> ratioMap = cbsRatioService.getCommonRatioMap(req.getTaskId().toString());
        //2. 查询cvm的预测采购量
        List<CbsPurchaseDTO> cbsPredictDTOList = queryCvmPurchasePredictDTO(params);

        //3. 查询cvm的完成采购量
        List<CbsPurchaseDTO> cbsFinishDTOList = queryCvmPurchaseFinishDTO(params);

        //4. 合并cvm预测采购量与cvm存量采购量
        List<CbsPurchaseDTO> strategyItemList = cbsPurchasePredictService.mergeAndCalculate(cbsPredictDTOList, cbsFinishDTOList);
        //去除所有计算完成的存量数据
        strategyItemList.removeIf(dto->dto.getStrategyType()==null&&!dto.getCvmInstanceType().equals("(默认)"));
        //5. 将核心数转换为磁盘数
        List<CbsPurchaseDTO> convertedlist = cbsPurchasePredictService.convertSumCoreToSumDisk(req.getRatioCategoryId(), strategyItemList);
        Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap=cbsPurchasePredictService.processStrategyItems(convertedlist,params);
        List<QueryCbsPurchaseTotalResp.CbsYearItem> purchaseYearItems =cbsPurchasePredictService.buildYearItems(strategyItemMap);
        //将采购量转换为增量
        List<QueryCbsScalePredictTotalResp.CbsYearItem> scaleYearItems=new ArrayList<>();
        for(QueryCbsPurchaseTotalResp.CbsYearItem pYearItem:purchaseYearItems){
            QueryCbsScalePredictTotalResp.CbsYearItem sYearItem = new CbsYearItem();
            sYearItem.setYear(pYearItem.getYear());
            int year = pYearItem.getYear();
            pYearItem.getCbsStrategyItems().forEach(o->{
                BigDecimal cvmRatio=ratioMap.getOrDefault("cvm_ratio",BigDecimal.ONE);
                BigDecimal emptyRatio=BigDecimal.ONE.subtract(ratioMap.getOrDefault("empty_ratio",BigDecimal.ZERO));
                String strategyType = o.getStrategyType();
                String key=year+"@"+strategyType;
                BigDecimal increaseRatio=scaleRatioMap.getOrDefault(key,BigDecimal.ZERO);
                if(increaseRatio.compareTo(BigDecimal.ZERO)==0)log.warn("数据类型："+key+"的存量换算比例为："+increaseRatio);
                QueryCbsScalePredictTotalResp.CbsStrategyItem item=new QueryCbsScalePredictTotalResp.CbsStrategyItem();
                item.setStrategyType(o.getStrategyType());
                item.setIncreasePredictDomestic(o.getPurchasePredictDomestic().multiply(increaseRatio).divide(cvmRatio,4,RoundingMode.HALF_UP).divide(emptyRatio,4,RoundingMode.HALF_UP));
                item.setIncreasePredictAboard(o.getPurchasePredictAboard().multiply(increaseRatio).divide(cvmRatio,4,RoundingMode.HALF_UP).divide(emptyRatio,4,RoundingMode.HALF_UP));
                item.setIncreasePredictTotal(o.getPurchasePredictTotal().multiply(increaseRatio).divide(cvmRatio,4,RoundingMode.HALF_UP).divide(emptyRatio,4,RoundingMode.HALF_UP));
                sYearItem.getCbsStrategyItems().add(item);
            });
            scaleYearItems.add(sYearItem);
        }

        //6. 处理完成量部分，只处理当年
        String finishEndDate=params.get("finishEndDate").toString();
        String scaleLastYear=params.get("scaleLastYear").toString();
        List<CbsScaleFinishAndLatestDTO> scaleFinishAndLatest = cbsRatioService.getScaleFinishAndLatest(finishEndDate,  scaleLastYear);
        List<CbsScaleFinishAndLatestDTO> finishList = scaleFinishAndLatest.stream().filter(o -> o.getStatTime().equals(finishEndDate)).collect(Collectors.toList());
        scaleYearItems.sort(Comparator.comparing(QueryCbsScalePredictTotalResp.CbsYearItem::getYear));
        processFinishedTotal(scaleYearItems.get(0),params.get("finishEndDate").toString(),finishList);

        //7. 处理最新日期已完成
        List<CbsScaleFinishAndLatestDTO> latestList=scaleFinishAndLatest.stream().filter(o->!o.getStatTime().equals(finishEndDate)).collect(Collectors.toList());
        processLatestTotal(scaleYearItems.get(0),latestList);

        //8. 调整境内外占比
        BigDecimal customhouseRatio = ratioMap.getOrDefault("customhouse_ratio", BigDecimal.ZERO);
        if(customhouseRatio==null||customhouseRatio.compareTo(BigDecimal.ZERO)==0)throw new RuntimeException("查询不到境内外配比");
        scaleYearItems.forEach(yearItem->{
            yearItem.getCbsStrategyItems().forEach(item->{
                item.setIncreasePredictDomestic(item.getIncreasePredictTotal().multiply(customhouseRatio));
                item.setIncreasePredictAboard(item.getIncreasePredictTotal().multiply(BigDecimal.ONE.subtract(customhouseRatio)));
            });
        });

        //9. 提升当年/次年数据
        String currentYear = params.get("currentYear").toString();
        String nextYear = params.get("nextYear").toString();
        scaleYearItems.forEach(yearItem->{
            int year=yearItem.getYear();
            if(year==Integer.parseInt(currentYear)){
                BigDecimal currentYearRatio = ratioMap.getOrDefault("current_enhance_year_ratio",BigDecimal.ONE);
                yearItem.getCbsStrategyItems().forEach(item->{
                    item.setIncreasePredictTotal(item.getIncreasePredictTotal().multiply(currentYearRatio));
                    item.setIncreasePredictAboard(item.getIncreasePredictAboard().multiply(currentYearRatio));
                    item.setIncreasePredictDomestic(item.getIncreasePredictDomestic().multiply(currentYearRatio));
                });
            }else if(year==Integer.parseInt(nextYear)){
                BigDecimal nextYearRatio = ratioMap.getOrDefault("next_enhance_year_ratio",BigDecimal.ONE);
                yearItem.getCbsStrategyItems().forEach(item->{
                    item.setIncreasePredictTotal(item.getIncreasePredictTotal().multiply(nextYearRatio));
                    item.setIncreasePredictAboard(item.getIncreasePredictAboard().multiply(nextYearRatio));
                    item.setIncreasePredictDomestic(item.getIncreasePredictDomestic().multiply(nextYearRatio));
                });
            }
        });
        resp.setCbsYearItems(scaleYearItems);
        return resp;
    }

    //处理净增量大框的最新日期已完成
    private void processLatestTotal(QueryCbsScalePredictTotalResp.CbsYearItem currentYearItem
            ,List<CbsScaleFinishAndLatestDTO> latestList){
        BigDecimal latestTotal= latestList.stream()
                .map(CbsScaleFinishAndLatestDTO::getSumDisk)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal latestAboard = latestList.stream().
                filter(o->o.getCustomhouseTitle().equals("境外"))
                .map(CbsScaleFinishAndLatestDTO::getSumDisk).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal latestDomestic = latestList.stream().
                filter(o->o.getCustomhouseTitle().equals("境内"))
                .map(CbsScaleFinishAndLatestDTO::getSumDisk).reduce(BigDecimal.ZERO, BigDecimal::add);
        //按照一台物理机55tb进行计算
        //BigDecimal ratio=BigDecimal.valueOf(55).multiply(BigDecimal.valueOf(1024));
        currentYearItem.getCbsStrategyItems().forEach(item->{
            item.setLatestClosingDate(latestList.get(0).getStatTime());
            item.setLatestFinishedDomestic(latestDomestic);
            item.setLatestFinishedAboard(latestAboard);
            item.setLatestFinishedTotal(latestTotal);
            item.setLatestFinishedRate(item.getLatestFinishedTotal().divide(item.getIncreasePredictTotal(),4, RoundingMode.HALF_UP));
        });
    }


    /**
     * 处理增量的大框完成量
     * @param currentYearItem
     * @param closingDate
     */
    private void processFinishedTotal(QueryCbsScalePredictTotalResp.CbsYearItem currentYearItem
            ,String closingDate
            ,List<CbsScaleFinishAndLatestDTO> finishList ){
        BigDecimal finishDomestic =finishList.stream()
                .filter(o -> o.getCustomhouseTitle().equals("境内"))
                .map(CbsScaleFinishAndLatestDTO::getSumDisk)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal finishAboard = finishList.stream()
                .filter(o->o.getCustomhouseTitle().equals("境外"))
                .map(CbsScaleFinishAndLatestDTO::getSumDisk)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal finishTotal= finishList.stream()
                .map(CbsScaleFinishAndLatestDTO::getSumDisk)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //BigDecimal ratio=BigDecimal.valueOf(1-0.18);
        currentYearItem.getCbsStrategyItems().forEach(item->{
            item.setFinishedClosingDate(closingDate);
            item.setFinishedDomestic(finishDomestic);
            item.setFinishedAboard(finishAboard);
            item.setFinishedTotal(finishTotal);
            item.setFinishedRate(item.getFinishedTotal().divide(item.getIncreasePredictTotal(),4, RoundingMode.HALF_UP));
        });
    }

    /**
     * 查询cvm已经完成的采购量
     * @param params
     * @return
     */
    @SneakyThrows
    private List<CbsPurchaseDTO> queryCvmPurchaseFinishDTO(Map<String, Object> params) {
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_purchase_finish_total.sql");
        List<CbsPurchaseDTO> all = cdLabDbHelper.getRaw(CbsPurchaseDTO.class, sql, params);
        if(all==null||all.isEmpty())throw new RuntimeException("查询预测采购量为空");
        return all;
    }

    /**
     * 查询预测部分CVM的采购量，用于转化
     * @return
     */
    @SneakyThrows
    private List<CbsPurchaseDTO> queryCvmPurchasePredictDTO(Map<String, Object> params){
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_purchase_predict_total.sql");
        List<CbsPurchaseDTO> all = cdLabDbHelper.getRaw(CbsPurchaseDTO.class, sql, params);
        if(all==null||all.isEmpty())throw new RuntimeException("查询预测采购量为空");
        return all;
    }



    /**
     * 获取查询的参数
     * @param req
     * @param taskDO
     * @return
     */
    private Map<String, Object> getPurchaseParams(QueryCbsScalePredictTotalReq req,
                                                  LongtermPredictTaskDO taskDO) {
        LocalDate predictStart = taskDO.getPredictStart();
        int currentYear = taskDO.getPredictStart().getYear();
        int nextYear = currentYear + 1;
        String yearMonthStr = currentYear+"-%";
//        String predStartDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).toString();
//        String predEndDate = YearMonth.of(predictStart.getYear() , 12).plusYears(1).toString();
//        String finishStartDate = YearMonth.of(predictStart.getYear(), 1).toString();
//        String finishEndDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue())
//                .minusMonths(1).toString();
        //上个月底
        String finishEndDate= predictStart.minusMonths(1).withDayOfMonth(predictStart.minusMonths(1).lengthOfMonth()).toString();
        //去年十二月
        String scaleLastYear= predictStart.minusYears(1).getYear()+"-12";
        // 当年的采购量， 当年的预测量，明年的预测量
        Map<String, Object> params = new HashMap<>();
        params.put("splitVersionId", req.getSplitVersionId());
        params.put("taskId", req.getTaskId());
        params.put("currentYear", currentYear);
        params.put("nextYear", nextYear);
        params.put("yearMonthStr", yearMonthStr);
        params.put("finishEndDate", finishEndDate);
        params.put("scaleLastYear", scaleLastYear);
//        params.put("predStartDate", predStartDate);
//        params.put("predEndDate", predEndDate);
        return params;
    }




    /**
     * 查询字典
     * @param req
     * @return
     */
    @HiSpeedCache(expireSecond = 3600,keyScript = "args[0]")
    @Override
    public QueryCbsScalePredictDictResp queryCbsScalePredictDict(QueryCbsScalePredictDictReq req) {
        QueryCbsScalePredictDictResp resp = new QueryCbsScalePredictDictResp();
        //resp.setBizRangeType(ListUtils.newArrayList("内部业务","外部业务"));
        resp.setCbsInstanceFamily(ListUtils.newArrayList("SA9", "SA9e", "SA5", "SA5t", "SA4t", "MA5",
                "MA4t", "IA5se", "SA4", "MA4", "IA4se", "SA3", "MA3", "SA3se", "SA2", "MA2", "HSA20", "S9", "S8",
                "M8", "M8m", "S7", "S6", "S6t", "C6", "C5", "M6", "M6ce", "M6p", "M6mp", "M6m", "M5", "TM5", "HM50",
                "S5", "TS5", "S5se", "HS50", "S5t", "ITA5", "ITA4t", "ITA5st", "ITA4", "ITA4a", "DA4", "DA4m", "IT5", "IT5c"));
        resp.setCustomhouseTitle(ListUtils.newArrayList("境内","境外"));
        List<String> cbsRegionOrCountry = ckstdcrpDBHelper.getRaw(String.class,"select if(customhouse_title = '境内' or country_name='美国', region_name, country_name) as region_name\n" +
                "                from std_crp.dwd_txy_cbs_scale_agg_df\n" +
                "                where stat_time in\n" +
                "                                 (SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month\n" +
                "FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))\n" +
                "                group by region_name,country_name;");
        List<String> cvmRegionOrCountry = cdLabDbHelper.getRaw(String.class,"select DISTINCT\n" +
                "    CASE\n" +
                "        WHEN a.customhouse_title = '境外' and a.country_name!='美国' THEN a.country_name\n" +
                "        ELSE a.region_name\n" +
                "        END as regionOrCountry\n" +
                "FROM longterm_predict_output_scale_split a\n" +
                "WHERE task_id = ?;",req.getTaskId());
        List<String> regionOrCountry = Stream.concat(cbsRegionOrCountry.stream(), cvmRegionOrCountry.stream())
                .distinct()  // 去重
                .collect(Collectors.toList());
        resp.setRegionOrCountry(regionOrCountry);
//        String start= ckstdcrpDBHelper.getRawOne(String.class,"SELECT min(year_month)\n" +
//                "FROM std_crp.dwd_txy_cbs_scale_df;");
        //todo
        resp.setStartYearMonth("2021-01");//查询全表太慢了，暂定写死
        String end = cdLabDbHelper.getRawOne(String.class, "select max(year_month_str)\n" +
                "from longterm_predict_output_purchase_split\n" +
                "where task_id=?;",req.getTaskId());
        resp.setEndYearMonth(end);
        return resp;
    }

    /**
     * 查询存量
     * @param req
     * @return
     */
    @Override
    public QueryCbsScaleHistoryResp queryCbsScaleScaleHistory(QueryCbsScaleScaleHistoryReq req) {
        // 初始化返回对象
        QueryCbsScaleHistoryResp resp = new QueryCbsScaleHistoryResp();

        String endYearMonth = adjustEndYearMonth(req.getEndYearmonth());

        List<CbsLongtermPredictScaleDTO> scaleDTOS = queryCbsScaleHistoryDTO(req.getStartYearmonth(),
                endYearMonth,req.getCustomhouseTitle(),req.getRegionOrCountry(),false);

        // 根据当前的年月，获取最近的存量年月
        String latestYearMonth = getLatestYearMonth(scaleDTOS);  // 获取最近的年月
        // 获取最近三个完整半年的增速
        List<HalfYear> halfYears = getPastHalfYears(latestYearMonth, 3); // 从最新的年月倒推3个完整半年
        // 将增速信息和半年详细信息添加到响应中
        List<QueryCbsScaleHistoryResp.IncreaseRate> increaserateDetails = calculateIncreaseRatesWithDetails(scaleDTOS, halfYears);

        // 将计算得到的增速和半年信息加入到 resp 中的 increaserateDetails 下
        resp.setIncreaseRates(increaserateDetails);

        if(StringTools.isBlank(req.getDims())||req.getDims().equals("yearMonth")){
            //按照年月聚合cbsLongtermPredictScaleDTOS，sumdisk相加
            Map<String, BigDecimal> yearMonthAggregation = new HashMap<>();

            // 按照 yearMonthStr 聚合并对 sumDisk 求和
            for (CbsLongtermPredictScaleDTO dto :scaleDTOS) {
                yearMonthAggregation.merge(dto.getYearMonthStr(), dto.getSumDisk(), BigDecimal::add);
            }

            // 将聚合结果添加到响应中
            for (Map.Entry<String, BigDecimal> entry : yearMonthAggregation.entrySet()) {
                Item item = new Item();
                item.setYearMonth(entry.getKey());
                item.setCurDisk(entry.getValue());
                item.setDimsName("yearMonth");
                resp.getItemList().add(item);
            }
            List<Item> items = resp.getItemList();
            items.sort(Comparator.comparing(Item::getYearMonth));
            // 大于6个月的数据才有近半年的增速
            if (!Lang.isEmpty(items) && items.size() > 6) {
                for (int i = items.size() - 1; i - 6 >= 0; i--) {
                    Item curItem = items.get(i);
                    Item curSub6Item = items.get(i - 6);
                    BigDecimal rate;
                    BigDecimal curDiskValue = curSub6Item.getCurDisk();
                    if (curDiskValue.compareTo(BigDecimal.ZERO) == 0||curItem.getCurDisk().compareTo(BigDecimal.ZERO) == 0) {
                        // 如果 curSub6Item.getCurDisk() 为零，设置 rate 为默认值，例如 0 或其他值
                        rate = BigDecimal.ZERO;
                    } else {
                        rate = curItem.getCurDisk().subtract(curDiskValue)
                                .divide(curDiskValue, 3, RoundingMode.HALF_UP);
                    }
                    curItem.setHalfYearIncreaseRate(rate);
                }
            }

        }
        else if(req.getDims().equals("regionOrCountry")){
            //按照年月+城市/国家聚合，dimsname为国家/城市字段
            Map<String, Map<String, BigDecimal>> regionCountryAggregation = new HashMap<>();

            // 按照 yearMonthStr 和 regionName 聚合，并对 sumDisk 求和
            for (CbsLongtermPredictScaleDTO dto : scaleDTOS) {
                regionCountryAggregation
                        .computeIfAbsent(dto.getYearMonthStr(), k -> new HashMap<>())
                        .merge(dto.getRegionName(), dto.getSumDisk(), BigDecimal::add);
            }

            // 将聚合结果添加到响应中
            for (Map.Entry<String, Map<String, BigDecimal>> yearMonthEntry : regionCountryAggregation.entrySet()) {
                BigDecimal totalDiskForMonth = yearMonthEntry.getValue().values().stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add);  // 计算当月所有地区的sumDisk总和

                for (Map.Entry<String, BigDecimal> regionEntry : yearMonthEntry.getValue().entrySet()) {
                    QueryCbsScaleHistoryResp.Item item = new QueryCbsScaleHistoryResp.Item();
                    item.setYearMonth(yearMonthEntry.getKey());
                    item.setDimsName(regionEntry.getKey()); // 城市/国家作为 dimsName
                    item.setCurDisk(regionEntry.getValue());

                    // 计算占比并设置到 rate 字段
                    BigDecimal rate;

                    if (totalDiskForMonth.compareTo(BigDecimal.ZERO) == 0||regionEntry.getValue().compareTo(BigDecimal.ZERO) == 0) {
                        // 如果 totalDiskForMonth 为零，设置 rate 为默认值，例如 BigDecimal.ZERO 或其他值
                        rate = BigDecimal.ZERO;
                    } else {
                        rate = regionEntry.getValue().divide(totalDiskForMonth, 4, RoundingMode.HALF_UP);
                    }
                    item.setRate(rate);
                    resp.getItemList().add(item);
                }
            }
            resp.getItemList().sort(Comparator.comparing(Item::getYearMonth)
                            .thenComparing(Comparator.comparing(Item::getCurDisk).reversed()));
        }
        else{
            throw new RuntimeException("不支持");
        }
        return resp;
    }

    /**
     * scale_cbs预测量查询
     * @param req
     * @return
     */
    @Override
    public QueryScalePredictResp queryCbsScalePredict(QueryCbsScalePredictReq req) {
        QueryScalePredictResp resp = new QueryScalePredictResp();
        //1.获取task信息+配比map
        LongtermPredictTaskDO taskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id = ?", req.getTaskId());
        String predictStart=taskDO.getPredictStart().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        int currentYear = taskDO.getPredictStart().getYear();
        int nextYear =currentYear+1;
        req.setStartYearmonth(predictStart);
        Map<String, BigDecimal> scaleRatioMap = cbsRatioService.getScaleRatios(req.getTaskId(), req.getSplitVersionId());
        // 获取配比方案明细
        List<CbsRatioDetailDO> ratioDetailList = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id=?", req.getRatioCategoryId());
        if(ratioDetailList==null||ratioDetailList.isEmpty())throw new RuntimeException("查询不到配比方案明细"+req.getRatioCategoryId()+"下的配比明细");
        Map<String, BigDecimal> ratioMap = cbsRatioService.getCommonRatioMap(req.getTaskId().toString());
        //2.在sql中直接计算出cvm采购量+聚合,先算增量，再进行累加

        //算出采购总量，换算增量总量，再减去已完成的增量，将剩下的增量分配给每个月
        Map<String, Object> params = getPredictPurchaseParams(req, taskDO);
        List<CbsPurchaseDTO> cbsFinishDTOList = queryCvmPurchaseFinishDTO(params);
        List<CbsPurchaseDTO> cbsPredictDTOList = queryCvmPurchasePredictDTO(params);
        List<CbsPurchaseDTO> strategyItemList = cbsPurchasePredictService.mergeAndCalculate(cbsPredictDTOList, cbsFinishDTOList);
        strategyItemList.removeIf(dto->dto.getStrategyType()==null&&!dto.getCvmInstanceType().equals("(默认)"));
        List<CbsPurchaseDTO> convertedPurchaseList = cbsPurchasePredictService.convertSumCoreToSumDisk(req.getRatioCategoryId(), strategyItemList);
        Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap=cbsPurchasePredictService.processStrategyItems(convertedPurchaseList,params);
        List<QueryCbsPurchaseTotalResp.CbsYearItem> purchaseYearItems =cbsPurchasePredictService.buildYearItems(strategyItemMap);

        Map<Integer,Map<String,BigDecimal>> purchasetotalMap=new HashMap<>();
        BigDecimal finished = cbsRatioService.queryCbsFinished(params);

        BigDecimal cvmRatio = ratioMap.getOrDefault("cvm_ratio", BigDecimal.ONE);
        BigDecimal emptyRatio=BigDecimal.ONE.subtract(ratioMap.getOrDefault("empty_ratio",BigDecimal.ZERO));
        BigDecimal currentYearRatio = ratioMap.getOrDefault("current_enhance_year_ratio",BigDecimal.ONE);
        BigDecimal nextYearRatio = ratioMap.getOrDefault("next_enhance_year_ratio",BigDecimal.ONE);
        purchaseYearItems.forEach(cbsYearItem -> {
            int year = cbsYearItem.getYear();
            purchasetotalMap.putIfAbsent(year,new HashMap<>());
            cbsYearItem.getCbsStrategyItems().forEach(cbsStrategyItem -> {
                BigDecimal purchasePredictTotal = cbsStrategyItem.getPurchasePredictTotal();
                String strategyType = cbsStrategyItem.getStrategyType();
                BigDecimal scaleRatio=scaleRatioMap.getOrDefault(year+"@"+strategyType,BigDecimal.valueOf(0.5));
                purchasePredictTotal=purchasePredictTotal.multiply(scaleRatio).divide(cvmRatio,4,RoundingMode.HALF_UP).divide(emptyRatio,4,RoundingMode.HALF_UP);
                if(year==currentYear){
                    purchasePredictTotal=purchasePredictTotal.multiply(currentYearRatio).subtract(finished);
                }else if(year==nextYear){
                    purchasePredictTotal=purchasePredictTotal.multiply(nextYearRatio);
                }else{
                    purchasePredictTotal=purchasePredictTotal.multiply(nextYearRatio);
                }
                purchasetotalMap.get(year).putIfAbsent(strategyType, purchasePredictTotal);
            });
        });
        Map<String, BigDecimal> currentYearMap = purchasetotalMap.get(currentYear);
        Map<String, BigDecimal> nextYearMap = purchasetotalMap.get(nextYear);

        List<CbsLongtermPredictScaleDTO> currentYearIncrement = distributeIncrement(currentYearMap, currentYear, taskDO.getPredictStart().getMonthValue(), true);
        List<CbsLongtermPredictScaleDTO> nextYearIncrement = distributeIncrement(nextYearMap, nextYear, 1, false);
        //次年净增数据要累加上当年最后一个月的增量
        nextYearIncrement.forEach(dto->{
            List<CbsLongtermPredictScaleDTO> collect = currentYearIncrement.stream().filter(o -> o.getStrategyType().equals(dto.getStrategyType())
                    && o.getYearMonthStr().equals(currentYear + "-12")).collect(Collectors.toList());
            dto.setSumDisk(dto.getSumDisk().add(collect.get(0).getSumDisk()));
        });
        List<CbsLongtermPredictScaleDTO> increaseDTOS = new ArrayList<>();
        increaseDTOS.addAll(currentYearIncrement);
        increaseDTOS.addAll(nextYearIncrement);

        // 按照策略、年月分组并计算组内总量
        Map<List<String>, List<CbsLongtermPredictScaleDTO>> grouped = increaseDTOS.stream()
                .collect(Collectors.groupingBy(dto -> Arrays.asList(dto.getStrategyType(),
                        dto.getYearMonthStr())));  // 按策略、机型、年月分组
        Map<String, BigDecimal> shareRatioMap = cbsRatioService.getRegionAndInstanceTypeIncreaseShareRatio();
        List<CbsLongtermPredictScaleDTO> processedList = new ArrayList<>();
        grouped.forEach((key, group) -> {
            // 计算每个组的总和
            BigDecimal totalSumDisk = group.stream()
                    .map(CbsLongtermPredictScaleDTO::getSumDisk)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 按比例分配
            shareRatioMap.forEach((k,v)->{
                CbsLongtermPredictScaleDTO dto=new CbsLongtermPredictScaleDTO();
                dto.setStrategyType(key.get(0));
                dto.setYearMonthStr(key.get(1));
                String[] split = k.split("@");
                dto.setCustomhouseTitle(split[0]);
                dto.setRegionName(split[1]);
                dto.setSumDisk(totalSumDisk.multiply(v));
                processedList.add(dto);
            });
        });

        //先计算出来境内外
        List<CbsLongtermPredictScaleDTO> customhouseFilterList=new ArrayList<>();
        if(!req.getCustomhouseTitle().isEmpty()){
            customhouseFilterList = processedList.stream().filter(dto -> req.getCustomhouseTitle().contains(dto.getCustomhouseTitle())).collect(Collectors.toList());
        }else{
            customhouseFilterList =new ArrayList<>(processedList);
        }
        //再计算区域
        List<CbsLongtermPredictScaleDTO> regionFilterList =new ArrayList<>(customhouseFilterList);
        if(!req.getRegionOrCountry().isEmpty()){
            regionFilterList = customhouseFilterList.stream().filter(dto -> req.getRegionOrCountry().contains(dto.getRegionName())).collect(Collectors.toList());
        }else{
            regionFilterList =new ArrayList<>(customhouseFilterList);
        }
        //resp.setCbsPredictIncreaseDTOS(convertedList);
        //3.获得cbs的存量
        String scaleEnd = YearMonth.parse(taskDO.getPredictStart().format(DateTimeFormatter.ofPattern("yyyy-MM"))).minusMonths(1).toString();
        List<CbsLongtermPredictScaleDTO> scaleDTOS = queryCbsScaleHistoryDTO(scaleEnd, scaleEnd, req.getCustomhouseTitle(), req.getRegionOrCountry(),false);
        //4.cbs存量+cvm增量获得cbs预测量
        //
        List<CbsLongtermPredictScaleDTO> cbsPredictDTOS=mergeAndCalculate(scaleDTOS,regionFilterList);

        //5.组合数据
        List<QueryScalePredictResp.PredictStrategyTypeItem> strategyList=processStrategyItems(cbsPredictDTOS,req.getDims(),req.getStartYearmonth(),req.getEndYearmonth());
        strategyList.removeIf(item->item.getStrategyType().equals("存量"));
        strategyList.forEach(list->{
            list.getItems().sort(Comparator.comparing(QueryScalePredictResp.Item::getYearMonth)
                    .thenComparing(Comparator.comparing(QueryScalePredictResp.Item::getSumDisk).reversed()));
        });
        resp.setPredictStrategyTypeItems(strategyList);
        return resp;
    }


    /**
     * 根据月份与总量生成增量dto
     * @param yearMap
     * @param year
     * @param month
     * @param isCurrentYear
     * @return
     */
    private List<CbsLongtermPredictScaleDTO> distributeIncrement(Map<String, BigDecimal> yearMap, int year,int month, boolean isCurrentYear) {
        List<CbsLongtermPredictScaleDTO> scaleDTOList = new ArrayList<>();
        int remainingMonths = isCurrentYear ? (12 - month)+1 : 12;

        yearMap.forEach((strategy,total)->{
            BigDecimal monthlyIncrement = total.divide(BigDecimal.valueOf(remainingMonths), 4, RoundingMode.HALF_UP);
            BigDecimal cumulativeIncrement = BigDecimal.ZERO;

            for (int m = month; m <= 12; m++) {
                // 累加每个月的增量
                cumulativeIncrement = cumulativeIncrement.add(monthlyIncrement);

                // 创建CbsLongtermPredictScaleDTO对象并设置属性
                CbsLongtermPredictScaleDTO dto = new CbsLongtermPredictScaleDTO();
                dto.setYearMonthStr(YearMonth.of(year,m).toString());  // 设置年份和月份
                dto.setStrategyType(strategy);  // 设置策略类型
                dto.setSumDisk(cumulativeIncrement);  // 设置累计增量

                scaleDTOList.add(dto);  // 添加到结果列表
            }
        });
        return scaleDTOList;
    }


    private Map<String,Object> getPredictPurchaseParams(QueryCbsScalePredictReq req,
                                                        LongtermPredictTaskDO taskDO) {
        LocalDate predictStart = taskDO.getPredictStart();
        int currentYear = taskDO.getPredictStart().getYear();
        int nextYear = currentYear + 1;
        String yearMonthStr = currentYear+"-%";
//        String predStartDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).toString();
//        String predEndDate = YearMonth.of(predictStart.getYear() , 12).plusYears(1).toString();
//        String finishStartDate = YearMonth.of(predictStart.getYear(), 1).toString();
//        String finishEndDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue())
//                .minusMonths(1).toString();
        //上个月底
        String finishEndDate= predictStart.minusMonths(1).withDayOfMonth(predictStart.minusMonths(1).lengthOfMonth()).toString();
        //去年十二月
        String scaleLastYear= predictStart.minusYears(1).getYear()+"-12";
        String lastYearEnd= scaleLastYear+"-31";
        // 当年的采购量， 当年的预测量，明年的预测量
        Map<String, Object> params = new HashMap<>();
        params.put("splitVersionId", req.getSplitVersionId());
        params.put("taskId", req.getTaskId());
        params.put("currentYear", currentYear);
        params.put("nextYear", nextYear);
        params.put("yearMonthStr", yearMonthStr);
        params.put("finishEndDate", finishEndDate);
        params.put("scaleLastYear", scaleLastYear);
        params.put("lastYearEnd", lastYearEnd);
//        params.put("predStartDate", predStartDate);
//        params.put("predEndDate", predEndDate);
        return params;
    }


    private List<CbsLongtermPredictScaleDTO> convertScaleSumCoreToSumDisk(Long categoryId,List<CbsLongtermPredictScaleDTO> increaseDTOS) {
        List<CbsRatioDetailDO> cbsRatioDetailDOS = cbsRatioService.buildCvmInstanceRatioList(categoryId);
        for(CbsLongtermPredictScaleDTO dto : increaseDTOS) {
            List<CbsRatioDetailDO> validRatios = filterMatchingRatios(dto, cbsRatioDetailDOS);
            BigDecimal ratio;
            if(!validRatios.isEmpty()){
                ratio=validRatios.get(0).getRatioNum();
                dto.setSumDisk(dto.getSumCore().multiply(ratio));
            }else{
                ratio = BigDecimal.ZERO;
                log.warn("没有找到对应的cvmInstanceType："+dto.getCvmInstanceType());
                dto.setSumDisk(dto.getSumCore().multiply(ratio));
            }
        }
        return increaseDTOS;
    }

    private List<PredictStrategyTypeItem> processStrategyItems(List<CbsLongtermPredictScaleDTO> cbsPredictDTOS,String dims,
                                                               String startYearMonth,String endYearMonth) {
                List<PredictStrategyTypeItem> strategyList = new ArrayList<>();

        //按年月+策略类型聚合
        if(dims.equals("yearMonth")){
            //key为策略的map
            Map<String,List<QueryScalePredictResp.Item>> itemMap= new HashMap<>();
            Map<String,List<CbsLongtermPredictScaleDTO>> yearMonthMap = cbsPredictDTOS.stream()
                    .collect(Collectors.groupingBy(dto -> dto.getStrategyType() + "@" + dto.getYearMonthStr()));
            for (Map.Entry<String,List<CbsLongtermPredictScaleDTO>> entry : yearMonthMap.entrySet()) {
                String[] split = entry.getKey().split("@");
                List<CbsLongtermPredictScaleDTO> value = entry.getValue();
                String strategy = split[0];
                String yearMonth = split[1];
                List<QueryScalePredictResp.Item> itemList = itemMap.getOrDefault(strategy, new ArrayList<>());
                QueryScalePredictResp.Item item = new QueryScalePredictResp.Item();
                item.setYearMonth(yearMonth);
                item.setDimsName(yearMonth);
                BigDecimal sumDisk = value.stream().map(CbsLongtermPredictScaleDTO::getSumDisk).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setSumDisk(sumDisk);
                itemList.add(item);
                itemList.sort(Comparator.comparing(QueryScalePredictResp.Item::getYearMonth));
                itemMap.put(strategy, itemList);
            }
            for(Map.Entry<String,List<QueryScalePredictResp.Item>> entry : itemMap.entrySet()){
                PredictStrategyTypeItem strategyTypeItem = new PredictStrategyTypeItem();
                strategyTypeItem.setStrategyType(entry.getKey());
                strategyTypeItem.setItems(entry.getValue());
                strategyList.add(strategyTypeItem);
            }

            //将预测量与存量相加
            if(itemMap.containsKey("存量")){
                strategyList.forEach(item->{
                    if(!item.getStrategyType().equals("存量")){
                        item.getItems().forEach(o->{
                            o.setSumDisk(o.getSumDisk().add(itemMap.get("存量").get(0).getSumDisk()));
                        });
                    }
                });
            }
            return strategyList;
        }
        //按年月+国家/城市+策略类型聚合
        else if(dims.equals("regionOrCountry")){
            //key为策略的map
            Map<String,List<QueryScalePredictResp.Item>> itemMap= new HashMap<>();
            Map<String, List<CbsLongtermPredictScaleDTO>> regionYearMonthMap = cbsPredictDTOS.stream().collect(Collectors.groupingBy(dto ->
                    dto.getStrategyType() + "-" + dto.getYearMonthStr() + "-" + dto.getRegionName()));
            for (Map.Entry<String, List<CbsLongtermPredictScaleDTO>> entry : regionYearMonthMap.entrySet()) {
                String[] split = entry.getKey().split("-");
                List<CbsLongtermPredictScaleDTO> value = entry.getValue();
                String strategy = split[0];
                String yearMonth = split[1]+"-"+split[2];
                String regionName=split[3];
                List<QueryScalePredictResp.Item> itemList = itemMap.getOrDefault(strategy, new ArrayList<>());
                QueryScalePredictResp.Item item = new QueryScalePredictResp.Item();
                item.setYearMonth(yearMonth);
                item.setDimsName(regionName);
                BigDecimal sumDisk = value.stream().map(CbsLongtermPredictScaleDTO::getSumDisk).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setSumDisk(sumDisk);
                itemList.add(item);
                itemMap.put(strategy, itemList);
            }
            // 对策略类型为“存量”的数据进行合并与对比
            List<QueryScalePredictResp.Item> stockItems = itemMap.getOrDefault("存量", new ArrayList<>());
            for (QueryScalePredictResp.Item stockItem : stockItems) {
                for (Map.Entry<String, List<QueryScalePredictResp.Item>> entry : itemMap.entrySet()) {
                    String strategy = entry.getKey();
                    List<QueryScalePredictResp.Item> strategyItems = entry.getValue();

                    if (!"存量".equals(strategy)) {
                        // 遍历其他策略类型下的 items，进行 dimsName 的对比
                        for (QueryScalePredictResp.Item strategyItem : strategyItems) {
                            if (stockItem.getDimsName().equals(strategyItem.getDimsName())) {
                                // 如果 dimsName 相等，则累加 sumDisk
                                strategyItem.setSumDisk(strategyItem.getSumDisk().add(stockItem.getSumDisk()));
                            }
                        }
                        // 如果没有找到对应的 dimsName，则直接将存量数据添加到 items 中
                        if (!strategyItems.stream().anyMatch(item -> item.getDimsName().equals(stockItem.getDimsName()))) {

                            YearMonth start=YearMonth.parse(startYearMonth);
                            YearMonth end=YearMonth.parse(endYearMonth);
                            for(YearMonth yearMonth=start;yearMonth.isBefore(end.plusMonths(1));yearMonth=yearMonth.plusMonths(1)){
                            QueryScalePredictResp.Item item = new QueryScalePredictResp.Item();
                            item.setSumDisk(stockItem.getSumDisk());
                            item.setDimsName(stockItem.getDimsName());
                            item.setYearMonth(yearMonth.toString());
                            strategyItems.add(item);
                            }
                        }
                    }
                }
            }

            for(Map.Entry<String,List<QueryScalePredictResp.Item>> entry : itemMap.entrySet()){
                PredictStrategyTypeItem strategyTypeItem = new PredictStrategyTypeItem();
                strategyTypeItem.setStrategyType(entry.getKey());
                strategyTypeItem.setItems(entry.getValue());
                strategyList.add(strategyTypeItem);
            }
            //处理占比
            strategyList.forEach(item->{
                Map<String, BigDecimal> yearMonthSumDiskMap = item.getItems().stream()
                        .collect(Collectors.groupingBy(QueryScalePredictResp.Item::getYearMonth,
                                Collectors.mapping(QueryScalePredictResp.Item::getSumDisk, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                item.getItems().forEach(o->{
                    String yearMonth = o.getYearMonth();
                    BigDecimal total = yearMonthSumDiskMap.get(yearMonth);
                    if(!(o.getSumDisk().compareTo(BigDecimal.ZERO)==0))o.setRate(o.getSumDisk().divide(total, 4, RoundingMode.HALF_UP));
                    else o.setRate(BigDecimal.ZERO);
                });
                System.out.println(yearMonthSumDiskMap);
            });

            return strategyList;
        }
        else{
            throw new RuntimeException("不支持");
        }
    }

    private List<CbsLongtermPredictScaleDTO> mergeAndCalculate(List<CbsLongtermPredictScaleDTO> cbsScaleDTOS, List<CbsLongtermPredictScaleDTO> cbsPredictIncreaseDTOS) {
        Map<String, CbsLongtermPredictScaleDTO> scaleMap = new HashMap<>();
        Map<String, CbsLongtermPredictScaleDTO> predictMap = new HashMap<>();
        //处理存量
        for(CbsLongtermPredictScaleDTO cbsScaleDTO:cbsScaleDTOS){
            String keys=generateScaleKey(cbsScaleDTO);
            if(!scaleMap.containsKey(keys)){
                CbsLongtermPredictScaleDTO scaleDTO = new CbsLongtermPredictScaleDTO(cbsScaleDTO);
                scaleMap.put(keys,scaleDTO);
                continue;
            }
            CbsLongtermPredictScaleDTO existingScaleDTO =scaleMap.get(keys);
            existingScaleDTO.setSumDisk(existingScaleDTO.getSumDisk().add(cbsScaleDTO.getSumDisk()));
        }
        //添加预测量
        for(CbsLongtermPredictScaleDTO cbsIncreaseDTO:cbsPredictIncreaseDTOS){
            String keyS=generateScaleKey(cbsIncreaseDTO);
            String keyP=generatePredictKey(cbsIncreaseDTO);
            if(predictMap.containsKey(keyP)){
                CbsLongtermPredictScaleDTO existingPredictDTO = predictMap.get(keyP);
                existingPredictDTO.setSumDisk(existingPredictDTO.getSumDisk().add(cbsIncreaseDTO.getSumDisk()));
            }else{
                CbsLongtermPredictScaleDTO scaleDTO =scaleMap.getOrDefault(keyS, null);
                if(scaleDTO!=null){
                    CbsLongtermPredictScaleDTO increaseDTO=new CbsLongtermPredictScaleDTO(cbsIncreaseDTO);
                    increaseDTO.setSumDisk(increaseDTO.getSumDisk().add(scaleDTO.getSumDisk()));
                    predictMap.put(keyP,increaseDTO);
                }else{
                    CbsLongtermPredictScaleDTO increaseDTO=new CbsLongtermPredictScaleDTO(cbsIncreaseDTO);
                    predictMap.put(keyP,increaseDTO);
                }
            }
        }
        //去除已经累加过的存量，将剩余存量加到预测量中
        for(CbsLongtermPredictScaleDTO increaseDTO:cbsPredictIncreaseDTOS){
            String keyS=generateScaleKey(increaseDTO);
            scaleMap.remove(keyS);
        }
        if(!scaleMap.isEmpty())predictMap.putAll(scaleMap);
        return new ArrayList<>(predictMap.values());
    }

    private String generatePredictKey(CbsLongtermPredictScaleDTO dto) {
        return  dto.getYearMonthStr()+"-"+
                dto.getStrategyType()+"-"+
                //dto.getCustomhouseTitle()+"-"+
                //dto.getBizRangeType()+"-"+
                dto.getRegionName();
                //dto.getCvmInstanceType();
    }

    private String generateScaleKey(CbsLongtermPredictScaleDTO dto) {
        return //dto.getCustomhouseTitle()+"-"+
                //dto.getBizRangeType()+"-"+
                dto.getRegionName();
                //dto.getCvmInstanceType();
    }


    /**
     * 查询预测量dto
     * @param req
     * @return
     */
    @SneakyThrows
    private List<CbsLongtermPredictScaleDTO> queryCbsPredictDTO(QueryCbsScalePredictReq req,LongtermPredictTaskDO taskDO) {

        String sqltemp=IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_scale_predict_bymonth.sql") ;
        String Sql = getPredictSqlConditon(sqltemp, req);
        HashMap<String, Object> params = new HashMap<>();
        params.put("task_id",req.getTaskId());
        params.put("split_version_id",req.getSplitVersionId());
        params.put("predict_start",req.getStartYearmonth());
        String predEnd = YearMonth.of(taskDO.getPredictStart().getYear() , 12).plusYears(1).toString();
        params.put("predict_end",predEnd);
        params.put("customhouse_title",req.getCustomhouseTitle());
        params.put("region_or_country",req.getRegionOrCountry());
        //String scaleEnd = YearMonth.parse(req.getStartYearmonth()).minusMonths(1).toString();
        //params.put("scale_year_month",scaleEnd);
        List<CbsLongtermPredictScaleDTO> raw = cdLabDbHelper.getRaw(CbsLongtermPredictScaleDTO.class, Sql, params);
        return raw;
    }

    /**查询存量dto
     * @param startYearMonth
     * @param endYearMonth
     * @param customhouseTitle
     * @param regionOrCountry
     * @return
     */
    private List<CbsLongtermPredictScaleDTO> queryCbsScaleHistoryDTO(String startYearMonth, String endYearMonth,
                                                                     List<String> customhouseTitle,
                                                                     List<String> regionOrCountry,boolean flag) {
        WhereSQL where = new WhereSQL();
        where.andIf(StringTools.isNotBlank(startYearMonth),"year_month>=?",startYearMonth);
        where.andIf(StringTools.isNotBlank(endYearMonth),"year_month<=?",endYearMonth);
        where.andIf(ListUtils.isNotEmpty(customhouseTitle),"customhouse_title in (?)",customhouseTitle);
        where.andIf(ListUtils.isNotEmpty(regionOrCountry),"(country_name in (?) or region_name in (?))",regionOrCountry,regionOrCountry);
        //where.addGroupBy("year_month, customhouse_title,instance_type,region_name");
        where.addGroupBy("year_month,instance_type,region_name,customhouse_title");
        String sqlTemplate = "SELECT\n" +
                "    SUM(cur_service_disk) AS `sum_disk`,\n" +
                "    year_month as `year_month_str`,\n" +
                "    customhouse_title,\n" +
                "    instance_type as cvm_instance_type,\n" +
                "    if(customhouse_title = '境内' or country_name='美国', region_name, country_name) as region_name,\n" +
                "    '存量' as strategy_type \n"+
                "FROM\n" +
                "    std_crp.dwd_txy_cbs_scale_agg_df %s";
        where.and("stat_time>='2021-01-31' and stat_time in\n" +
                "                                 (SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month\n" +
                "FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))\n" );
        //是否剔除
        if(flag)where.and(
                "       and  app_id not in (1258344706, 1251316161)\n" +
                "       and instance_type global in (  select distinct instance_type\n" +
                "                                  from dwd_txy_scale_df\n" +
                "                                  where stat_time=(select max(stat_time) from dwd_txy_scale_df) and cpu_or_gpu='CPU' and biz_type='cvm' )\n" +
                "       and instance_type not like 'RS%' and instance_type not like 'RM%'");
        String sql = String.format(sqlTemplate, where.getSQL());
        //List<CbsLongtermPredictScaleDTO> all = ckstdcrpDBHelper.getRaw(CbsLongtermPredictScaleDTO.class, sql, where.getParams());
        List<CbsScaleDTO> all = ckstdcrpDBHelper.getRaw(CbsScaleDTO.class, sql, where.getParams());
        if(all==null||all.isEmpty())throw new RuntimeException("存量数据为空");
        List<CbsLongtermPredictScaleDTO> result = CbsLongtermPredictScaleDTO.createlist(all);

        return result;
    }


    /**
     * 筛选出符合条件的配比条目
     * @param dto
     * @param ratioDetailList
     * @return
     */
    private List<CbsRatioDetailDO> filterMatchingRatios(CbsLongtermPredictScaleDTO dto,
                                                        List<CbsRatioDetailDO> ratioDetailList) {
        List<CbsRatioDetailDO> validRatios = new ArrayList<>();

        // 遍历每个配比条目
        for (CbsRatioDetailDO ratio : ratioDetailList) {
            boolean matches = true;

            // 检查每个字段的匹配条件，优先匹配有值的字段
            if (!"(默认)".equals(ratio.getCbsInstanceFamily()) && !ratio.getCbsInstanceFamily().equals(dto.getCvmInstanceType())) {
                matches = false;
            }
            if (!"(默认)".equals(ratio.getCustomhouseTitle()) && !ratio.getCustomhouseTitle().equals(dto.getCustomhouseTitle())) {
                matches = false;
            }
            if (!"(默认)".equals(ratio.getStrategyType()) && !ratio.getStrategyType().equals(StrategyTypeEnum.getNameByCode(dto.getStrategyType()))) {
                matches = false;
            }

            // 如果 cbsInstanceFamily 是 null，匹配 "默认"
            if (dto.getCvmInstanceType() == null && "(默认)".equals(ratio.getCbsInstanceFamily())) {
                matches = true;
            }

            // 如果匹配，添加到有效配比列表
            if (matches) {
                validRatios.add(ratio);
            }
        }
        // 排序逻辑：优先选择没有 "(默认)" 的条目，若有多个 "(默认)"，选择字段使用 "(默认)" 最少的条目
        validRatios.sort((ratio1, ratio2) -> {
            int defaultCount1 = countDefaultFields(ratio1);
            int defaultCount2 = countDefaultFields(ratio2);
            return Integer.compare(defaultCount1, defaultCount2); // 默认字段越少，优先级越高
        });

        return validRatios;
    }

    /**
     * 计算一个配比条目中有多少个字段是 "(默认)"
     * @param ratio
     * @return 默认字段的数量
     */
    private int countDefaultFields(CbsRatioDetailDO ratio) {
        int defaultCount = 0;

        if ("(默认)".equals(ratio.getCbsInstanceFamily())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getCustomhouseTitle())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getBizRangeType())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getStrategyType())) {
            defaultCount++;
        }

        return defaultCount;
    }

    private String getPredictSqlConditon(String sql, QueryCbsScalePredictReq req) {
        if (req.getCustomhouseTitle() != null && !req.getCustomhouseTitle().isEmpty()) {
            sql = sql.replace("${customhouse_title_condition}", "AND  customhouse_title in (:customhouse_title)");
        } else {
            sql = sql.replace("${customhouse_title_condition}", "");
        }
//        if (req.getCbsInstanceFamily() != null && !req.getCbsInstanceFamily().isEmpty()) {
//            sql = sql.replace("${instance_family_condition}", "AND  instance_family in (:instance_family)");
//        } else {
//            sql = sql.replace("${instance_family_condition}", "");
//        }
        if (req.getRegionOrCountry() != null && !req.getRegionOrCountry().isEmpty()) {
            if ("regionOrCountry".equals(req.getDims())) {
                sql = sql.replace("${region_condition}", "AND  region_name in (:region_or_country)");
                sql = sql.replace("${country_condition}", "AND country_name in(:region_or_country)");
                sql = sql.replace("${region_or_country_condition}",
                        "AND  (country_name in (:region_or_country) or region_name in (:region_or_country))");
            } else {
                sql = sql.replace("${region_or_country_condition}",
                        "AND  (country_name in (:region_or_country) or region_name in (:region_or_country))");
            }
        } else {
            sql = sql.replace("${region_or_country_condition}", "");
            sql = sql.replace("${region_condition}", "");
            sql = sql.replace("${country_condition}", "");
        }
        return sql;
    }


    public static List<HalfYear> getPastHalfYears(String currentYearMonth, int numHalfYears) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentDate = LocalDate.parse(currentYearMonth + "-01", formatter);

        List<HalfYear> halfYears = new ArrayList<>();

        for (int i = 0; i < numHalfYears; i++) {
            currentDate = currentDate.minusMonths(6); // 每次退回6个月

            int year = currentDate.getYear();
            int month = currentDate.getMonthValue();

            // 判断是 H1 还是 H2
            String halfYearName = (month <= 6) ? year + "H1" : year + "H2";
            LocalDate startMonth = (month <= 6) ? LocalDate.of(year, 1, 1) : LocalDate.of(year, 7, 1);
            LocalDate endMonth = (month <= 6) ? LocalDate.of(year, 6, 30) : LocalDate.of(year, 12, 31);

            HalfYear halfYear = new HalfYear(halfYearName, startMonth, endMonth);
            halfYears.add(halfYear);
        }

        return halfYears;
    }
    // 半年数据的存储类
    @Data
    public static class HalfYear {
        private String halfYearName;
        private LocalDate startMonth;
        private LocalDate endMonth;

        public HalfYear(String halfYearName, LocalDate startMonth, LocalDate endMonth) {
            this.halfYearName = halfYearName;
            this.startMonth = startMonth;
            this.endMonth = endMonth;
        }

        @Override
        public String toString() {
            return "HalfYear{" +
                    "halfYearName='" + halfYearName + '\'' +
                    ", startMonth=" + startMonth +
                    ", endMonth=" + endMonth +
                    '}';
        }
    }

    // 计算增速并返回详细的半年信息
    private List<QueryCbsScaleHistoryResp.IncreaseRate> calculateIncreaseRatesWithDetails(List<CbsLongtermPredictScaleDTO> cbsLongtermPredictScaleDTOS, List<HalfYear> halfYears) {
        List<QueryCbsScaleHistoryResp.IncreaseRate> increaserateDetails = new ArrayList<>();
        for (HalfYear halfYear : halfYears) {
            // 获取当前半年对应的累计sumDisk
            BigDecimal sumDiskStart = getSumDiskForHalfYear(cbsLongtermPredictScaleDTOS, halfYear.getStartMonth());
            BigDecimal sumDiskEnd = getSumDiskForHalfYear(cbsLongtermPredictScaleDTOS, halfYear.getEndMonth());

            // 计算增速
            BigDecimal rate;
            if (sumDiskStart.compareTo(BigDecimal.ZERO) == 0 || sumDiskEnd.compareTo(BigDecimal.ZERO) == 0) {
                rate = BigDecimal.ZERO;
            } else {
                rate = sumDiskEnd.subtract(sumDiskStart).divide(sumDiskStart, 4, RoundingMode.HALF_UP);
            }

            // 创建并添加增速和半年信息
            QueryCbsScaleHistoryResp.IncreaseRate detail = new QueryCbsScaleHistoryResp.IncreaseRate(
                    halfYear.getHalfYearName(),
                    halfYear.getStartMonth().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                    halfYear.getEndMonth().format(DateTimeFormatter.ofPattern("yyyy-MM")),
                    rate
            );
            increaserateDetails.add(detail);
        }
        return increaserateDetails;
    }

    // 获取某个半年的sumDisk
    private BigDecimal getSumDiskForHalfYear(List<CbsLongtermPredictScaleDTO> cbsLongtermPredictScaleDTOS, LocalDate month) {
        return cbsLongtermPredictScaleDTOS.stream()
                .filter(dto -> dto.getYearMonthStr().equals(month.format(DateTimeFormatter.ofPattern("yyyy-MM"))))
                .map(CbsLongtermPredictScaleDTO::getSumDisk)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 获取最近的存量年月
    private String getLatestYearMonth(List<CbsLongtermPredictScaleDTO> cbsLongtermPredictScaleDTOS) {
        return cbsLongtermPredictScaleDTOS.stream()
                .map(CbsLongtermPredictScaleDTO::getYearMonthStr)
                .max(String::compareTo)
                .orElseThrow(() -> new RuntimeException("无法找到最新的存量年月"));
    }

    public static String adjustEndYearMonth(String endYearMonth) {
        // 获取当前日期，并计算当前月的前一个月
        LocalDate currentDate = LocalDate.now();
        LocalDate previousMonth = currentDate.minusMonths(1);

        // 设置当前月的前一个月为字符串格式 yyyy-MM
        String previousMonthStr = previousMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 解析传入的 endYearMonth
        LocalDate endDate = LocalDate.parse(endYearMonth + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 判断 endYearMonth 是否在当前月的前一个月之后
        if (endDate.isAfter(previousMonth)) {
            // 如果 endYearMonth 在当前月的前一个月之后，返回当前月的前一个月
            return previousMonthStr;
        } else {
            // 否则保留原始的 endYearMonth
            return endYearMonth;
        }
    }
}
