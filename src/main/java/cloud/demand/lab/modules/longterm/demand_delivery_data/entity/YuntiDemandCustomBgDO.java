package cloud.demand.lab.modules.longterm.demand_delivery_data.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("yunti_demand_custom_bg")
public class YuntiDemandCustomBgDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 事业群id<br/>Column: [bg_id] */
    @Column(value = "bg_id")
    private Integer bgId;

    /** 事业群名称<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 部门id<br/>Column: [dept_id] */
    @Column(value = "dept_id")
    private Integer deptId;

    /** 部门名称<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品ID<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品名称<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 自定义事业群id<br/>Column: [custom_bg_id] */
    @Column(value = "custom_bg_id")
    private Integer customBgId;

    /** 自定义事业群<br/>Column: [custom_bg] */
    @Column(value = "custom_bg")
    private String customBg;

    /** 自定义部门id<br/>Column: [custom_dept_id] */
    @Column(value = "custom_dept_id")
    private Integer customDeptId;

    /** 自定义部门<br/>Column: [custom_dept] */
    @Column(value = "custom_dept")
    private String customDept;

    /** 自定义规划产品id<br/>Column: [custom_plan_product_id] */
    @Column(value = "custom_plan_product_id")
    private Integer customPlanProductId;

    /** 自定义规划产品<br/>Column: [custom_plan_product] */
    @Column(value = "custom_plan_product")
    private String customPlanProduct;

}