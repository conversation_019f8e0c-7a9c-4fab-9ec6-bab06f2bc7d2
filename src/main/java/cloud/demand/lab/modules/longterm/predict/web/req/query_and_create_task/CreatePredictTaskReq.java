package cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task;

import lombok.Data;

import java.util.List;

@Data
public class CreatePredictTaskReq {

    /**方案id*/
    private Long categoryId;
    /**是否直接设置为启用，如果没有传，默认不启用*/
    private Boolean isEnable;
    /**是否使用缓存，默认true使用，如果需要强制重新读取input数据，请设置为false*/
    private Boolean isUseCache;

    /**方案输入参数*/
    private List<InputArgsDTO> inputArgs;

}
