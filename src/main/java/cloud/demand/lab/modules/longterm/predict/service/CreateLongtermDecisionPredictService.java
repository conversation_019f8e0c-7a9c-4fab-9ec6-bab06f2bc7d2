package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.longterm.predict.web.req.decision_task.CreateDecisionPredictTaskReq;
import cloud.demand.lab.modules.longterm.predict.web.req.decision_task.QueryCreateDecisionTaskInfoReq;
import cloud.demand.lab.modules.longterm.predict.web.req.decision_task.QueryLatestTaskArgsInfoReq;
import cloud.demand.lab.modules.longterm.predict.web.req.decision_task.QueryTaskArgsInfoReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.decision_task.CalPurchaseRealtimeResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.decision_task.CreateDecisionPredictTaskResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.decision_task.QueryDecisionTaskInfoResp;

/**
 * 用于创建决策类型的预测任务，执行决策型预测任务
 */
public interface CreateLongtermDecisionPredictService {

    /**
     * 查询决策类型的预测任务创建信息
     */
    QueryDecisionTaskInfoResp queryCreateDecisionTaskInfo(QueryCreateDecisionTaskInfoReq req);

    /**
     * 提交决策的模型输入信息，需要加上当时选择的关联的方案任务id和行业中长期版本
     */
    CreateDecisionPredictTaskResp createDecisionPredictTask(CreateDecisionPredictTaskReq req);

    /**
     * 实时计算各区域的采购量
     */
    CalPurchaseRealtimeResp calPurchaseRealtime(CreateDecisionPredictTaskReq req);

    /**
     * 运行任务
     */
    void doRunTask(Long taskId);

    /**
     * 查询指定任务的输入信息
     */
    QueryDecisionTaskInfoResp queryDecisionTaskArgsInfo(QueryTaskArgsInfoReq req);

    /**
     * 查询最后一次任务输入参数
     */
    QueryDecisionTaskInfoResp queryDecisionTaskLatestArgs(QueryLatestTaskArgsInfoReq req);

}
