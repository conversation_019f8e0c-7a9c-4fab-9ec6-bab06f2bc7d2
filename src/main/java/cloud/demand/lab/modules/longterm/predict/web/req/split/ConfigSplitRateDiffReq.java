package cloud.demand.lab.modules.longterm.predict.web.req.split;

import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.BizRangeTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.CustomhouseTitleRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToZoneNameRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.MonthRate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigSplitRateDiffReq {

    Long taskId;

    // 一共6份配置表
    List<CustomhouseTitleRate> customhouseTitleRate;
    List<BizRangeTypeRate> bizRangeTypeRate;
    List<InstanceFamilyRate> instanceFamilyRate;
    List<InstanceFamilyToDeviceTypeRate> instanceFamilyToDeviceTypeRate;
    List<InstanceFamilyToZoneNameRate> instanceFamilyToZoneNameRate;
    List<MonthRate> monthRate;


    List<String> instanceFamilyToDeviceTypeRateExcelHeader;

}
