package cloud.demand.lab.modules.longterm.cos.algorithm;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.nutz.lang.Lang;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class ARIMA {

    public static PredictResult predict(List<DateNumDTO> data, int n, List<Integer> params) {
        return predict(data, n, params, true);
    }

    public static PredictResult predict(List<DateNumDTO> data, int n,
                                        List<Integer> params, boolean isTryQ) {

        // 当参数只有1个且值为-1时，表示自动选参，目前直接根据下一个点的准确率为最优来选参数
        if (params.size() == 1 && Objects.equals(params.get(0), -1)) {
            params = findParam(data);
        }

        if (params.size() < 3) { // ARIMA需要至少3个输入
            return new PredictResult(Lang.list());
        }

        Integer p = params.get(0);
        Integer d = params.get(1);
        Integer q = params.get(2);

        try {
            List<DateNumDTO> result = invokeArima(data, n, p, d, q, isTryQ);
            return new PredictResult(result);
        } catch (Throwable e) {
            log.error("forecast call fail", e);
            throw e;
        }
    }

    private static List<Integer> findParam(List<DateNumDTO> data) {
        if (data.size() < 4) { // 数据点少于4个无法自动确定参数，用默认参数
            return ListUtils.of(0, 1, 3);
        }
        // 如果最新数据为0，那么不用测了，准确率肯定是0，选不出最优
        if (data.get(data.size() - 1).getValue().compareTo(BigDecimal.ZERO) == 0) {
            return ListUtils.of(0, 1, 3);
        }

        BigDecimal maxAccuracyRatePercent = BigDecimal.ZERO;
        int bestP = -1, bestD = -1, bestQ = -1;
        for (int p = 0; p <= 3; p++) {
            for (int d = 0; d <= 2; d++) {
                for (int q = 0; q <= 6; q++) {
                    try {
                        List<DateNumDTO> ret = invokeArima(data.subList(0, data.size() - 1), 1, p, d, q, false);
                        if (!ret.isEmpty()) {
                            BigDecimal predictValue = ret.get(0).getValue();
                            BigDecimal realValue = data.get(data.size() - 1).getValue();
                            BigDecimal accuracyRatePercent = calAccuracyRatePercent(predictValue, realValue);
                            if (accuracyRatePercent.compareTo(maxAccuracyRatePercent) > 0) {
                                maxAccuracyRatePercent = accuracyRatePercent;
                                bestP = p;
                                bestD = d;
                                bestQ = q;
                            }
                        }
                    } catch (Throwable ignored) { // 自动选参数的异常忽略
                    }
                }
            }
        }

        if (bestP == -1 || bestD == -1 || bestQ == -1) {
            return ListUtils.of(0, 1, 3); // 确定不了的用默认参数
        }
        return ListUtils.of(bestP, bestD, bestQ);
    }

    /**
     * 调用arima
     * @param isTryQ 当arima预测失败时，是否尝试q+1，q-1，q+2，q-2
     */
    @SneakyThrows
    private static List<DateNumDTO> invokeArima(List<DateNumDTO> data, int n, int p, int d, int q, boolean isTryQ) {
        String arimaUrl ="http://11.134.155.181";
        String url = String.format("%s?n=%d&p=%d&d=%d&q=%d",arimaUrl, n, p, d, q);

        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("text/plain");
        String collect = data.stream()
                .map((o) -> o.getDate() + "," + o.getValue())
                .collect(Collectors.joining("\n"));
        RequestBody body = RequestBody.create(mediaType, "Date,Value\n" + collect);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "text/plain")
                .build();

        Response response = null;
        try {
            response = client.newCall(request).execute();
            while (response.code() == 502) {
                response = client.newCall(request).execute();
            }
        } catch (Exception e) {
            response = client.newCall(request).execute();
        }

        String retsString = Objects.requireNonNull(response.body()).string();

        if (isTryQ) {
            int[] tmpArray = {1,-1,2,-2};
            int tmpCur = 0;
            while (retsString.contains("NaN")) { // 当返回了NaN，则尝试q+1，q-1，q+2，q-2
                url = String.format("%s?n=%d&p=%d&d=%d&q=%d",arimaUrl, n, p, d, q + tmpArray[tmpCur]);
                response = getResponse(url, client, body);
                retsString = Objects.requireNonNull(response.body()).string();
                tmpCur += 1;
                if (tmpCur == 4) {
                    if (retsString.contains("NaN")) {
                        throw Lang.makeThrow("arima 预测返回了 NaN: %s\n, url： %s\n, 参数：\n%s ", retsString, url,
                                "Date,Value\n" + collect);
                    }
                    break;
                }
            }
        }

        String[] split = retsString.split("\n");

        if (split.length != n + 1) {
            throw Lang.makeThrow("arima 返回行数不一致,返回：%s\n, url： %s\n, 参数：\n%s ", retsString, url,
                    "Date,Value\n" + collect);
        }
        List<String> retData = Lang.list(split);
        retData = retData.subList(0, retData.size() - 1);
        try {
            return retData.stream().map((o) -> {
                        String[] tmp = o.split("\\s+");
                        return new DateNumDTO(tmp[0], new BigDecimal(tmp[1]));
                    })
                    .map((o) -> {
                        if (o.getValue().compareTo(BigDecimal.ZERO) < 0) { // 目前预测的序列都是>=0，因此小于0的会被处理为0
                            return new DateNumDTO(o.getDate(), BigDecimal.ZERO);
                        }
                        return o;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            throw Lang.makeThrow(String.valueOf(retData));
        }
    }

    private static Response getResponse(String url, OkHttpClient client, RequestBody body) throws IOException {
        Request request;
        Response response;
        request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "text/plain")
                .build();

        response = null;
        try {
            response = client.newCall(request).execute();
            while (response.code() == 502) {
                response = client.newCall(request).execute();
            }
        } catch (Exception e) {
            response = client.newCall(request).execute();
        }
        return response;
    }

    /**
     * 计算预测准确率，min max算法
     */
    private static BigDecimal calAccuracyRatePercent(BigDecimal predictValue, BigDecimal realValue) {
        BigDecimal max = NumberUtils.max(predictValue, realValue);
        if (max.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal min = NumberUtils.min(predictValue, realValue);
        return NumberUtils.divide(min, max, 6).multiply(BigDecimal.valueOf(100));
    }
}
