package cloud.demand.lab.modules.longterm.predict.entity;

import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DBList.DBHelperWithClz;
import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_predict_input_instance2device_rate")
public class LongtermPredictInputInstance2deviceRateDO extends BaseDO {

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 机型大类<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 占比<br/>Column: [rate] */
    @Column(value = "rate")
    private BigDecimal rate;

    public static DBHelperWithClz<LongtermPredictInputInstance2deviceRateDO> db() {
        return DBHelperWithClz.create(DBList.cdLabDbHelper);
    }
}