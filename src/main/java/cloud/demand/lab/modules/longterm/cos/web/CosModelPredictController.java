package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.longterm.cos.service.CosModelPredictService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责cos中长期算法预测
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosModelPredictController {

    @Resource
    private CosModelPredictService cosModelPredictService;

    /**
     * 查询预测结果，包含实时计算和查询已经计算好的结果两种情况
     */
    @RequestMapping
    public QueryPredictResultResp queryPredictResult(@JsonrpcParam QueryPredictResultReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }

        if (req.getTaskId() == null) {
            req.setTaskId(0L);
        }

        return cosModelPredictService.queryPredictResult(req);
    }

}
