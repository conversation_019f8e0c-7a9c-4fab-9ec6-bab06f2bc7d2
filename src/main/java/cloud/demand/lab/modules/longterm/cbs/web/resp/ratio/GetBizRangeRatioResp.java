package cloud.demand.lab.modules.longterm.cbs.web.resp.ratio;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
@Data
public class GetBizRangeRatioResp {
    private List<regionItem> regionItems=new ArrayList<>();

    @Data
    public static class regionItem{
        private String regionName;
        private List<Item> item = new ArrayList<>();
    }
    @Data
    public static class Item{
        private String yearMonth;

        private BigDecimal inner;
        private BigDecimal outer;
        //外部占比
        private BigDecimal ratio;
    }
}
