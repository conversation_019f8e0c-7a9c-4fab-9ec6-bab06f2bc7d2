package cloud.demand.lab.modules.longterm.cbs.dto;

import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
//@Table("cbs_longterm_predict_ratio_detail")
public class CbsRatioDetailDTO {
//    @Column(value = "id",isKey = true,isAutoIncrement = true)
//    private Long id;
    /** 配比方案id */
    //@Column(value = "ratio_category_id")
    private Long ratioCategoryId;
    /** 机型族（AMD，Intel，其他） */
    //@Column(value = "cbs_instance_family")
    private String cbsInstanceFamily;
    /** 境内外（境内境外，可选）<br/>Column: [customhouse_title] */
    //@Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 内外部（内部外部，可选）<br/>Column: [biz_range_type] */
    //@Column(value = "biz_range_type")
    private String bizRangeType;

    /** 策略（EXTREME、MIDDLE、CAUTIOUS，可选）<br/>Column: [strategy_type] */
    //@Column(value = "strategy_type")
    private String strategyType;

    /** 一核对多少GB<br/>Column: [ratio_num] */
    //@Column(value = "ratio_num")
    private BigDecimal ratioNum;

    public static List<CbsRatioDetailDTO> convertToDTO(List<CbsRatioDetailDO> detailDOS) {
        List<CbsRatioDetailDTO> detailDTOList = ListUtils.transform(detailDOS, (detailDO) -> {
            CbsRatioDetailDTO dto = new CbsRatioDetailDTO();
            dto.setRatioCategoryId(detailDO.getRatioCategoryId());
            dto.setCbsInstanceFamily(detailDO.getCbsInstanceFamily());
            dto.setCustomhouseTitle(detailDO.getCustomhouseTitle());
            dto.setBizRangeType(detailDO.getBizRangeType());
            dto.setStrategyType(detailDO.getStrategyType());
            dto.setRatioNum(detailDO.getRatioNum());
            return dto;
        });
        return detailDTOList;
    }
}
