package cloud.demand.lab.modules.longterm.predict.web.req.split;

import cloud.demand.lab.modules.longterm.predict.web.resp.split.Instance2DeviceRateResp;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class UpdateSplitRstReq {

    private Long splitVersionId;
    private String note;

    private List<Item> items;

    @Data
    public static class Item{
        Long updateId;
        BigDecimal purchaseCore;
    }

}
