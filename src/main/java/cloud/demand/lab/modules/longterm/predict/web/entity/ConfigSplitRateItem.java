package cloud.demand.lab.modules.longterm.predict.web.entity;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigBizRangeTypeRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigCumstomhouseTitleRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO.YearMonthItem;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToZoneNameRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigMonthRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitDeviceTypeKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitInstanceFamilyKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitZoneNameKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitMonthKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.RateGetter;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.RegionOrCountrySetter;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.List;
import lombok.Data;

public class ConfigSplitRateItem {

    @Data
    public static class BizRangeTypeRate implements RateGetter {

        private String bizRangeType;
        private BigDecimal rate;

        public static BizRangeTypeRate from(LongtermPredictConfigBizRangeTypeRateDO o) {
            BizRangeTypeRate one = new BizRangeTypeRate();
            one.setBizRangeType(o.getBizRangeType());
            one.setRate(o.getRate());
            return one;
        }

        public static LongtermPredictConfigBizRangeTypeRateDO toDo(BizRangeTypeRate o) {
            LongtermPredictConfigBizRangeTypeRateDO one = new LongtermPredictConfigBizRangeTypeRateDO();
            one.setBizRangeType(o.getBizRangeType());
            one.setRate(o.getRate());
            return one;
        }
    }

    @Data
    public static class CustomhouseTitleRate implements RateGetter {

        private String customhouseTitle;
        private BigDecimal rate;

        public static CustomhouseTitleRate from(LongtermPredictConfigCumstomhouseTitleRateDO o) {
            CustomhouseTitleRate one = new CustomhouseTitleRate();
            one.setCustomhouseTitle(o.getCustomhouseTitle());
            one.setRate(o.getRate());
            return one;
        }

        public static LongtermPredictConfigCumstomhouseTitleRateDO toDo(CustomhouseTitleRate o) {
            LongtermPredictConfigCumstomhouseTitleRateDO one = new LongtermPredictConfigCumstomhouseTitleRateDO();
            one.setCustomhouseTitle(o.getCustomhouseTitle());
            one.setRate(o.getRate());
            return one;
        }
    }

    @Data
    public static class InstanceFamilyRate implements SplitInstanceFamilyKeyGetter, RateGetter {

        private String customhouseTitle;
        private String instanceFamily;
        private BigDecimal rate;

        public static LongtermPredictConfigInstanceFamilyRateDO toDo(InstanceFamilyRate source) {
            LongtermPredictConfigInstanceFamilyRateDO ret = new LongtermPredictConfigInstanceFamilyRateDO();
            ret.setCustomhouseTitle(source.getCustomhouseTitle());
            ret.setInstanceFamily(source.getInstanceFamily());
            ret.setRate(source.getRate());
            return ret;
        }
    }

    @Data
    public static class InstanceFamilyToDeviceTypeRate implements SplitDeviceTypeKeyGetter, RateGetter {

        private String customhouseTitle;
        private String instanceFamily;
        private String deviceType;
        private BigDecimal rate;
        private List<YearMonthItem> yearMonthItemList;

        public static LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO toDo(
                InstanceFamilyToDeviceTypeRate source) {
            LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO ret =
                    new LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO();
            ret.setCustomhouseTitle(source.getCustomhouseTitle());
            ret.setInstanceFamily(source.getInstanceFamily());
            ret.setDeviceType(source.getDeviceType());
            ret.setRate(source.getRate());
            ret.setYearMonthItem(JSON.toJson(source.getYearMonthItemList()));
            return ret;
        }
    }

    @Data
    public static class InstanceFamilyToDeviceTypeRateExcelDTO implements RateGetter {

        private String customhouseTitle;
        private String instanceFamily;
        private String deviceType;
        private BigDecimal rate;
        private BigDecimal rate1;
        private BigDecimal rate2;
        private BigDecimal rate3;
        private BigDecimal rate4;
        private BigDecimal rate5;
        private BigDecimal rate6;
        private BigDecimal rate7;
        private BigDecimal rate8;
        private BigDecimal rate9;
        private BigDecimal rate10;
        private BigDecimal rate11;
        private BigDecimal rate12;
        private BigDecimal rate13;
        private BigDecimal rate14;
        private BigDecimal rate15;
        private BigDecimal rate16;
        private BigDecimal rate17;
        private BigDecimal rate18;

        public static void setByNum(YearMonthItem yearMonthItem, InstanceFamilyToDeviceTypeRateExcelDTO o, int i) {
            switch (i) {
                case 0:
                    yearMonthItem.setRate(o.getRate1());
                    break;
                case 1:
                    yearMonthItem.setRate(o.getRate2());
                    break;
                case 2:
                    yearMonthItem.setRate(o.getRate3());
                    break;
                case 3:
                    yearMonthItem.setRate(o.getRate4());
                    break;
                case 4:
                    yearMonthItem.setRate(o.getRate5());
                    break;
                case 5:
                    yearMonthItem.setRate(o.getRate6());
                    break;
                case 6:
                    yearMonthItem.setRate(o.getRate7());
                    break;
                case 7:
                    yearMonthItem.setRate(o.getRate8());
                    break;
                case 8:
                    yearMonthItem.setRate(o.getRate9());
                    break;
                case 9:
                    yearMonthItem.setRate(o.getRate10());
                    break;
                case 10:
                    yearMonthItem.setRate(o.getRate11());
                    break;
                case 11:
                    yearMonthItem.setRate(o.getRate12());
                    break;
                case 12:
                    yearMonthItem.setRate(o.getRate13());
                    break;
                case 13:
                    yearMonthItem.setRate(o.getRate14());
                    break;
                case 14:
                    yearMonthItem.setRate(o.getRate15());
                    break;
                case 15:
                    yearMonthItem.setRate(o.getRate16());
                    break;
                case 16:
                    yearMonthItem.setRate(o.getRate17());
                    break;
                case 17:
                    yearMonthItem.setRate(o.getRate18());
                    break;
            }
        }

    }


    @Data
    public static class InstanceFamilyToZoneNameRate
            implements SplitZoneNameKeyGetter, RateGetter, RegionOrCountrySetter {

        private String customhouseTitle;
        private String instanceFamily;
        private String regionOrCountry;
        private String zoneName;
        private BigDecimal rate;

        public static LongtermPredictConfigInstanceFamilyToZoneNameRateDO toDo(InstanceFamilyToZoneNameRate source) {
            LongtermPredictConfigInstanceFamilyToZoneNameRateDO ret
                    = new LongtermPredictConfigInstanceFamilyToZoneNameRateDO();
            ret.setCustomhouseTitle(source.getCustomhouseTitle());
            ret.setInstanceFamily(source.getInstanceFamily());
            ret.setRegionOrCountry(source.getRegionOrCountry());
            ret.setZoneName(source.getZoneName());
            ret.setRate(source.getRate());
            return ret;
        }
    }

    @Data
    public static class MonthRate
            implements SplitMonthKeyGetter, RateGetter {

        private String customhouseTitle;
        private Integer year;
        private Integer month;
        private BigDecimal rate;

        public YearMonth getYm() {
            return YearMonth.of(year, month);
        }

        public static LongtermPredictConfigMonthRateDO toDo(MonthRate source) {
            LongtermPredictConfigMonthRateDO ret = new LongtermPredictConfigMonthRateDO();
            ret.setCustomhouseTitle(source.getCustomhouseTitle());
            ret.setYear(source.getYear());
            ret.setMonth(source.getMonth());
            ret.setRate(source.getRate());
            return ret;
        }
    }

}
