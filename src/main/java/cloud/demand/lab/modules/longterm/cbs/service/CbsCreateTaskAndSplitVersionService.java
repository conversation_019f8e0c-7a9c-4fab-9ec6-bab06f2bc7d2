package cloud.demand.lab.modules.longterm.cbs.service;

import cloud.demand.lab.modules.longterm.cbs.web.req.task_and_split.CreateTaskAndSplitVersionReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.task_and_split.CreateTaskAndSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryForCreateResp;

public interface CbsCreateTaskAndSplitVersionService {

    /**
     * 创建预测和拆分版本
     * @param req
     * @return
     */
    CreateTaskAndSplitVersionResp createTaskAndSplitVersion(CreateTaskAndSplitVersionReq req);

    /**
     * cbs页面查询cate和task，取方案100和方案300的最新预测日期，
     * 如果两者都有当前最新的日期（例如2025-07）那么取方案300；否则取那个有最新预测日期的方案
     * @param req
     * @return
     */
    QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req);

    /**
     * 模型输入参数页面的方案框做筛选
     * @param req
     * @return
     */
    QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req);
}
