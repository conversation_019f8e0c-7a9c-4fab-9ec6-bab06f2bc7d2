package cloud.demand.lab.modules.longterm.predict.service;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitAdjustDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.CopySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.DownLoadExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.ParseImportExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.TransToAnnualReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.TransToAnnualResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.CopySplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.DownloadExcelResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.ImportFromExcelResp;
import java.util.List;

public interface TransToAnnualService {


    /**
     * 解析导出的excel ，全量覆盖的形式去解析
     * @param req req
     * @return ret
     */
    ImportFromExcelResp importFromExcel(ParseImportExcelReq req);

    /**
     * 前端调用这个接口表示已经下发给全年了，这里由前端来处理一致性数据问题，不过不关键
     * @param req req
     * @return resp
     */
    TransToAnnualResp transToAnnual(TransToAnnualReq req);

    /**
     * 下载excel,指定一个 versionId, 全量覆盖的形式
     * @param req req
     * @return resp
     */
    DownloadExcelResp downloadExcel(DownLoadExcelReq req);

    /**
     * 转换成Adjust 的DO类
     * @param data data
     * @return list
     */
    List<LongtermPredictOutputPurchaseSplitAdjustDO> tranFrom(List<LongtermPredictOutputPurchaseSplitDO> data);

    /**
     * copy 一个split 数据
     * @param req req
     * @return rrt
     */
    CopySplitVersionResp copySplitVersion(CopySplitVersionReq req);
}
