package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.predict.service.impl.SplitServiceImpl.SplitInfo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class DataSplitTreeNode<T extends SplitValCheckGetter> {

    private int level = 0;
    // 拆分debug 信息
    private String msg;
    // 实际拆分数据
    private T data;
    // 父节点
    @ToString.Exclude
    private DataSplitTreeNode<T> parent;

    // 子节点
    private List<DataSplitTreeNode<T>> children = new ArrayList<>();
    private BigDecimal childrenSum = BigDecimal.ZERO;

    // debug 的时候用的，拆分历史数据，todo 参数传递
    private SplitInfo splitInfo;

    // needSplit 大客户的需求不需要拆分， 默认都要拆分的
    private Boolean needSplit = true;


    protected DataSplitTreeNode(String msg) {
        this.msg = msg;
    }

    protected DataSplitTreeNode(String msg, T data) {
        this.msg = msg;
        this.data = data;
    }

    public static <K extends SplitValCheckGetter>
    DataSplitTreeNode<K> newNode(String msg, K data) {
        return new DataSplitTreeNode<>(msg, data);
    }

    public BigDecimal getChildrenSum(Function<T, BigDecimal> o) {
        return this.getChildren().stream()
                .map(child -> o.apply(child.getData()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 通过 tree 来添加节点，防止没有层级引用
     *
     * @param child chi
     */
    protected void addChild(DataSplitTreeNode<T> child) {
        child.parent = this;
        this.children.add(child);
        this.childrenSum = this.childrenSum.add(child.getData().getSplitCheckVal());
    }

}