package cloud.demand.lab.modules.longterm.predict.dto.data_split;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleWithIndustryDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptDO;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class IndustryDeptPurchaseDataSplitOrder implements DataSplitOrder {

    int i = -1;
    private static final List<Function<LongtermPredictInputScaleWithIndustryDO, String>> splitKey = new ArrayList<>();
    private static final List<Function<LongtermPredictOutputPurchaseSplitIndustryDeptDO, String>> historySplitKey = new ArrayList<>();
    private static final List<BiConsumer<LongtermPredictOutputPurchaseSplitIndustryDeptDO, String>> splitKeySetter = new ArrayList<>();
    private static final List<String> splitName = new ArrayList<>();

    public static String YEAR_MONTH = "拆分年月";
    public static String CUSTOM_HOUSE_TITLE_SPLIT = "境内外";
    public static String BIZ_RANGE_TYPE_SPLIT = "内外部拆分";

    public static String CUSTOMER_SPLIT = "客户类型拆分";


    static {
        splitKey.add(LongtermPredictInputScaleWithIndustryDO::getCustomhouseTitle);
        splitKey.add((o) -> "total");
        splitKey.add(LongtermPredictInputScaleWithIndustryDO::getBizRangeType);
        splitKey.add((o) -> "total");
        splitKey.add(LongtermPredictInputScaleWithIndustryDO::getIndustryDept);

        historySplitKey.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getCustomhouseTitle);
        historySplitKey.add((o) -> "total");
        historySplitKey.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getBizRangeType);
        historySplitKey.add((o) -> "total");
        historySplitKey.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getIndustryDept);

        splitKeySetter.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::setCustomhouseTitle);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::setYearMonthStr);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::setBizRangeType);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::setCustomerType);
        splitKeySetter.add(LongtermPredictOutputPurchaseSplitIndustryDeptDO::setIndustryDept);

        splitName.add(CUSTOM_HOUSE_TITLE_SPLIT);
        splitName.add(YEAR_MONTH);
        splitName.add(BIZ_RANGE_TYPE_SPLIT);
        splitName.add(CUSTOMER_SPLIT);
        splitName.add("拆分行业部门");
    }

    @Override
    public String getSplitPrefixKey(Object data) {
        if (data instanceof LongtermPredictOutputPurchaseSplitIndustryDeptDO) {
            return historySplitKey.subList(0, i).stream()
                    .map(func -> func.apply((LongtermPredictOutputPurchaseSplitIndustryDeptDO) data))
                    .collect(Collectors.joining("@"));
        } else if (data instanceof LongtermPredictInputScaleWithIndustryDO) {
            return splitKey.subList(0, i).stream()
                    .map(func -> func.apply((LongtermPredictInputScaleWithIndustryDO) data))
                    .collect(Collectors.joining("@"));
        } else {
            throw new RuntimeException("data type error");
        }
    }

    @Override
    public String getSplitCurKey(Object data) {
        if (data instanceof LongtermPredictOutputPurchaseSplitIndustryDeptDO) {
            return historySplitKey.get(i).apply((LongtermPredictOutputPurchaseSplitIndustryDeptDO) data);
        } else if (data instanceof LongtermPredictInputScaleWithIndustryDO) {
            return splitKey.get(i).apply((LongtermPredictInputScaleWithIndustryDO) data);
        } else {
            throw new RuntimeException("data type error");
        }
    }

    @Override
    public void setSplitCurKey(Object data, String key) {
        splitKeySetter.get(i).accept((LongtermPredictOutputPurchaseSplitIndustryDeptDO) data, key);
    }

    @Override
    public String getName() {
        return splitName.get(i);
    }

    @Override
    public boolean hasNext() {
        return i < splitName.size() - 1;
    }

    @Override
    public DataSplitOrder next() {
        i++;
        return this;
    }
}
