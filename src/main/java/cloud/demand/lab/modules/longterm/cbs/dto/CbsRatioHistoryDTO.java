package cloud.demand.lab.modules.longterm.cbs.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CbsRatioHistoryDTO {
    @Column("stat_time")
    private String statTime;
    @Column("type")
    private String type;
    @Column("instance_type")
    private String instanceType;
    @Column("cur")
    private BigDecimal cur;
    @Column("sum_cur_core_12")
    private BigDecimal sumCurCore12;
//    @Column("biz_range_type")
//    private String bizRangeType;
    @Column("customhouse_title")
    private String customhouseTitle;
}
