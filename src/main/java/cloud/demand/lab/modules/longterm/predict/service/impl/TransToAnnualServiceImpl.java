package cloud.demand.lab.modules.longterm.predict.service.impl;

import cloud.demand.lab.common.utils.EStream;
import cloud.demand.lab.common.utils.ListUtils2;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigExcludeRegionDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitAdjustDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleSplitDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleSplitTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import cloud.demand.lab.modules.longterm.predict.service.LongtermPredictDictService;
import cloud.demand.lab.modules.longterm.predict.service.TransToAnnualService;
import cloud.demand.lab.modules.longterm.predict.service.excel.DownloadExcelStyleHandler;
import cloud.demand.lab.modules.longterm.predict.service.excel.ExcelDownloadDTO;
import cloud.demand.lab.modules.longterm.predict.service.impl.LongtermPredictDictServiceImpl.FullCampusBaseDictData;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.CopySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.DownLoadExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.ParseImportExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.TransToAnnualReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.TransToAnnualResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.CopySplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.DownloadExcelResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.trans_to_annual.ImportFromExcelResp;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class TransToAnnualServiceImpl implements TransToAnnualService {


    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private DBQuery dbQuery;
    @Resource
    private LongtermPredictDictService longtermPredictDictService;

    @Resource
    private DBHelper purchasereportDBHelper;


    static List<String> SHEET_NAMES = Lang.list("激进", "中立", "保守");


    @Override
    @Transactional(value = "cdlabTransactionManager")
    public ImportFromExcelResp importFromExcel(ParseImportExcelReq req) {

        Long versionId = req.getSplitVersionId();

        LongtermPredictOutputSplitVersionDO version = dbQuery.querySplitVersion( versionId, true);
        LongtermPredictTaskDO task =
                cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id=?", version.getTaskId());
        if (task == null) {
            throw new BizException("任务不存在");
        }
        List<String> dateList = getExcelDateHeaders(task);

        ImportFromExcelResp resp = new ImportFromExcelResp();
        Map<String, List<ExcelDownloadDTO>> excelDownloadMap = parseExcelAndCheckHeaders(req, task);
        resp.setExcelDownloadMap(excelDownloadMap);

        String end13weekYearMonth = version.getEnd13weekYearMonth();
        cdLabDbHelper.delete(LongtermPredictOutputPurchaseSplitAdjustDO.class,
                "where split_version_id=? and year_month_str > ?", versionId, end13weekYearMonth);
        // 转换成 adjust 的接口
        List<LongtermPredictOutputPurchaseSplitAdjustDO> inserts = Lang.list();
        for (String sheetName : excelDownloadMap.keySet()) {
            List<ExcelDownloadDTO> excelDownloadDTOS = excelDownloadMap.get(sheetName);
            for (ExcelDownloadDTO excelDownloadDTO : excelDownloadDTOS) {
                String strategyCode = StrategyTypeEnum.getCodeByName(sheetName);
                for (int i = 0; i < dateList.size(); i++) {
                    String date = dateList.get(i);
                    // 忽略13周之前的数据
                    if (!YearMonth.parse(date).isAfter(YearMonth.parse(end13weekYearMonth))) {
                        continue;
                    }
                    Integer purchaseCore = excelDownloadDTO.getNumByIndex(i + 1);
                    LongtermPredictOutputPurchaseSplitAdjustDO adjustInsert =
                            ExcelDownloadDTO.toDO(excelDownloadDTO, strategyCode, versionId, task.getId());
                    inserts.add(adjustInsert);
                    setByDate(date, adjustInsert, purchaseCore);
                }
            }
        }
        cdLabDbHelper.insertBatchWithoutReturnId(inserts);
        resp.setInfo("success");
        return resp;
    }

    private static void setByDate(String date, LongtermPredictOutputPurchaseSplitAdjustDO adjustInsert,
            Integer purchaseCore) {
        YearMonth ym = YearMonth.parse(date);
        adjustInsert.setStatTime(ym.atDay(1));
        adjustInsert.setYearMonthStr(ym.toString());
        adjustInsert.setHalfYear((ym.getMonthValue() + 5) / 6);
        adjustInsert.setQuarter((ym.getMonthValue() + 2) / 3);
        adjustInsert.setYear(ym.getYear());
        if (purchaseCore == null) {
            adjustInsert.setPurchaseCore(BigDecimal.ZERO);
            adjustInsert.setPurchaseCoreDebug(BigDecimal.ZERO);
            adjustInsert.setPurchaseCoreOrigin(BigDecimal.ZERO);
        }else {
            adjustInsert.setPurchaseCore(BigDecimal.valueOf(purchaseCore));
            adjustInsert.setPurchaseCoreDebug(BigDecimal.valueOf(purchaseCore));
            adjustInsert.setPurchaseCoreOrigin(BigDecimal.valueOf(purchaseCore));
        }
    }


    @SneakyThrows
    private static Map<String, List<ExcelDownloadDTO>> parseExcelAndCheckHeaders(
            ParseImportExcelReq req, LongtermPredictTaskDO task) {

        Map<String, List<ExcelDownloadDTO>> excelDataList = new HashMap<>();

        List<String> dateList = getExcelDateHeaders(task);
        List<String> excelHead = getExcelHead(dateList).stream().map((o) -> o.get(0)).collect(Collectors.toList());

        AnalysisEventListener<ExcelDownloadDTO> readListener = new AnalysisEventListener<ExcelDownloadDTO>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                List<String> parseLine = IntStream.range(0, headMap.size())
                        .mapToObj(headMap::get).collect(Collectors.toList());
                for (int i = 0; i < parseLine.size(); i++) {
                    if (!Strings.equals(parseLine.get(i), excelHead.get(i))) {
                        throw BizException.makeThrow("Excel 第%s列导入的表头不是导出的表头(导入表头: %s, 导出表头应该: %s),"
                                        + "请先导出然后用导出的EXCEL填写后导入数据.",
                                convertToExcelColumn(i), parseLine.get(i), excelHead.get(i)
                        );
                    }
                }
            }

            @Override
            public void invoke(ExcelDownloadDTO data, AnalysisContext context) {
                // Check if all fields are null or empty strings
                boolean allFieldsNullOrEmpty = true;
                for (Field field : data.getClass().getDeclaredFields()) {
                    field.setAccessible(true);
                    try {
                        // trim 前后的空白字符串
                        Object value = field.get(data);
                        if (value instanceof Integer) {
                            field.set(data, Math.abs((Integer) value)); // 无论什么数据都转为正数
                        } else if (value instanceof String && Strings.isNotBlank((String) value)) {
                            field.set(data, Strings.trim((String) value));
                        }
                        // 全是空的直接不要数据
                        if (value != null && !(value instanceof String && ((String) value).isEmpty())) {
                            allFieldsNullOrEmpty = false;
                            break;
                        }
                    } catch (IllegalAccessException e) {
                        log.error("获取字段值失败: {}", e.getMessage());
                    }
                }
                if (allFieldsNullOrEmpty) {
                    return;
                }
                String sheetName = context.readSheetHolder().getSheetName();
                excelDataList.computeIfAbsent(sheetName, k -> new ArrayList<>()).add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        };

        List<ReadSheet> allSheets = EasyExcel.read(req.getExcelFile().getInputStream()).build().excelExecutor().sheetList();
        for (String sheetName : SHEET_NAMES) {
            boolean sheetExists = allSheets.stream().anyMatch(sheet -> sheet.getSheetName().equals(sheetName));
            if (!sheetExists) {
                throw BizException.makeThrow("Excel缺少%s的工作表,请检查.", sheetName);
            }
            EasyExcel.read(req.getExcelFile().getInputStream(), ExcelDownloadDTO.class, readListener)
                    .headRowNumber(2).sheet(sheetName).doRead();
        }

        return excelDataList;
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public TransToAnnualResp transToAnnual(TransToAnnualReq req) {
        Long versionId = req.getSplitVersionId();
        LongtermPredictOutputSplitVersionDO version = dbQuery.querySplitVersion(versionId, true);
        version.setIsTransToAnnual(true);
        version.setTransToAnnualTime(LocalDateTime.now());
        version.setTransToAnnualOperator(LoginUtils.getUserName());
        LongtermPredictOutputSplitVersionDO.db().update(version);
        TransToAnnualResp resp = new TransToAnnualResp();
        resp.setInfo("success");
        return resp;
    }



    @SuppressWarnings("Convert2MethodRef")
    @Override
    public DownloadExcelResp downloadExcel(DownLoadExcelReq req) {
        Long versionId = req.getSplitVersionId();

        LongtermPredictOutputSplitVersionDO version = dbQuery.querySplitVersion( versionId, true);
        LongtermPredictTaskDO task =
                cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id=?", version.getTaskId());
        if (task == null) {
            throw new BizException("任务不存在");
        }

        List<LongtermPredictOutputPurchaseSplitAdjustDO> all = cdLabDbHelper.getAll(
                LongtermPredictOutputPurchaseSplitAdjustDO.class, "where split_version_id=?", versionId);
        Function<LongtermPredictOutputPurchaseSplitAdjustDO, String> keyFunc =
                (o) -> Strings.join("@", o.getObsProjectType(), o.getModuleBusinessTypeName(), o.getReason(),
                        o.getIndustryName(), o.getCustomerName(), o.getRegion(),o.getZoneName(),
                        o.getZone(), o.getCampus(), o.getDeviceType());
        List<String> dateList = getExcelDateHeaders(task);

        Map<String, List<ExcelDownloadDTO>> excelData = new HashMap<>();
        ListUtils2.groupThenAccept(all, o -> o.getStrategyType(), (strategy, list) ->
                ListUtils2.groupThenAccept(list, keyFunc, (lineKey, line) -> {
                    Map<String, List<LongtermPredictOutputPurchaseSplitAdjustDO>> lineMap =
                            ListUtils.groupBy(line, o -> o.getYearMonthStr());
                    List<ExcelDownloadDTO> ontStrategyType = excelData.computeIfAbsent(strategy, k -> Lang.list());
                    ExcelDownloadDTO oneExcelLine = ExcelDownloadDTO.getExcelDownloadDTO(line.get(0));
                    ontStrategyType.add(oneExcelLine);
                    int i = 0;
                    for (String date : dateList) {
                        i++;
                        List<LongtermPredictOutputPurchaseSplitAdjustDO> column = lineMap.get(date);
                        BigDecimal sum = NumberUtils.sum(column, o -> o.getPurchaseCore());
                        oneExcelLine.setNumByIndex(i, sum.intValue());
                    }
                })
        );

        List<List<String>> excelHead = getExcelHead(dateList);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out).registerWriteHandler(new DownloadExcelStyleHandler()).build();
        WriteSheet writeSheet0 = EasyExcel.writerSheet(0, SHEET_NAMES.get(0)).head(excelHead).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet(1, SHEET_NAMES.get(1)).head(excelHead).build();
        WriteSheet writeSheet2 = EasyExcel.writerSheet(2, SHEET_NAMES.get(2)).head(excelHead).build();
        excelWriter.write(excelData.get(StrategyTypeEnum.EXTREME.getCode()), writeSheet0)
                .write(excelData.get(StrategyTypeEnum.MIDDLE.getCode()), writeSheet1)
                .write(excelData.get(StrategyTypeEnum.CAUTIOUS.getCode()), writeSheet2).finish();

        DownloadExcelResp downloadExcelResp = new DownloadExcelResp();
        downloadExcelResp.setFileName("全量数据.xlsx");
        downloadExcelResp.setBytes(out.toByteArray());
        return downloadExcelResp;
    }

    private static List<String> getExcelDateHeaders(LongtermPredictTaskDO task) {
        LocalDate predictStart = task.getPredictStart();
        YearMonth startMonth = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue());
        List<String> dateList = new ArrayList<>();
        IntStream.range(0, 24).forEach((i) -> dateList.add(startMonth.plusMonths(i).toString()));
        return dateList;
    }

    private static List<List<String>> getExcelHead(List<String> dateList) {
        List<List<String>> excelHead = new ArrayList<>();
        excelHead.add(Lang.list("项目类型", "项目类型"));
        excelHead.add(Lang.list("业务类型", "业务类型"));
        excelHead.add(Lang.list("需求原因", "需求原因"));
        excelHead.add(Lang.list("行业", "行业"));
        excelHead.add(Lang.list("客户", "客户"));
        excelHead.add(Lang.list("CVM可用区", "CVM可用区"));
        excelHead.add(Lang.list("Region", "Region"));
        excelHead.add(Lang.list("Zone", "Zone"));
        excelHead.add(Lang.list("Campus", "Campus"));
        excelHead.add(Lang.list("设备类型", "设备类型"));
        excelHead.add(Lang.list("备注", "备注"));
        for (String date : dateList) {
            excelHead.add(Lang.list(date, date));
        }
        return excelHead;
    }


    @Override
    public List<LongtermPredictOutputPurchaseSplitAdjustDO> tranFrom(List<LongtermPredictOutputPurchaseSplitDO> data) {
        List<LongtermPredictOutputPurchaseSplitAdjustDO> ret = Lists.newArrayList();
        Map<String, FullCampusBaseDictData> campusToErpRegionInfoMap =
                longtermPredictDictService.getCampusToErpRegionInfoMap();

        List<LongtermPredictConfigExcludeRegionDO> excludeRegion =
                cdLabDbHelper.getAll( LongtermPredictConfigExcludeRegionDO.class);
        for (LongtermPredictOutputPurchaseSplitDO oneSplit : data) {

            Optional<LongtermPredictConfigExcludeRegionDO> hasInConfig = EStream.of(excludeRegion).filter((o) ->
                    Strings.equals(o.getCountryName(), oneSplit.getCountryName())
                            || Strings.equals(o.getRegionName(), oneSplit.getRegionName())).findAny();
            if (hasInConfig.isPresent()) {
                continue;
            }
            LongtermPredictOutputPurchaseSplitAdjustDO one = tranFrom(oneSplit);
            ret.add(one);
            String campus = oneSplit.getCampus();
            if (!campusToErpRegionInfoMap.containsKey(campus)) {
                one.appendNote("未找到 campus 配置");
            } else {
                FullCampusBaseDictData fullCampusBaseDictData = campusToErpRegionInfoMap.get(campus);
                one.setRegion(fullCampusBaseDictData.getRegionName());
                one.setZone(fullCampusBaseDictData.getZoneName());
            }
        }
        return ret;
    }

    @Override
    public CopySplitVersionResp copySplitVersion(CopySplitVersionReq req) {

        Long versionId = req.getSplitVersionId();
        LongtermPredictOutputSplitVersionDO version = dbQuery.querySplitVersion( versionId, true);

        LongtermPredictOutputSplitVersionDO clone = JSON.clone(version);
        clone.cleanBaseDO();
        clone.setSourceVersionId(req.getSplitVersionId());
        if (StringTools.isNotBlank(req.getVersionName())) {
            clone.setName(req.getVersionName());
        }
        cdLabDbHelper.insert(clone);

        List<Class<?>> copyClass = Lang.list(
                LongtermPredictOutputPurchaseSplitDO.class,
                LongtermPredictOutputScaleSplitDO.class,
                LongtermPredictOutputPurchaseSplitIndustryDeptDO.class,
                LongtermPredictOutputPurchaseSplitTreeDO.class,
                LongtermPredictOutputScaleSplitTreeDO.class,
                LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO.class,
                LongtermPredictOutputPurchaseSplitAdjustDO.class
        );
        Long newVersionSplitId = clone.getId();
        for (Class<?> aClass : copyClass) {
            List<String> copyColumn = getTableAndColumnName(aClass);
            String template = "insert into %s (%s) select %s from %s where split_version_id=?";
            String tableName = copyColumn.get(0);
            List<String> columnNames = copyColumn.subList(1, copyColumn.size());
            String columnName = String.join(",", columnNames);
            String sql = String.format(template, tableName, columnName,
                    columnName.replace("split_version_id", newVersionSplitId.toString()), tableName);
            cdLabDbHelper.executeRaw(sql, req.getSplitVersionId());
        }
        CopySplitVersionResp resp = new CopySplitVersionResp();
        resp.setNewVersion(clone);
        return resp;
    }

    // 第一个值是table
    private static List<String> getTableAndColumnName(Class<?> clazz) {
        List<Field> columns = DOInfoReader.getColumns(clazz);
        List<String> columnNames = new ArrayList<>();

        if (!clazz.isAnnotationPresent(Table.class)) {
            throw new RuntimeException("Table annotation not found");
        }
        // 第一个值是table
        Table table = DOInfoReader.getTable(clazz);
        columnNames.add(table.value());

        for (Field column : columns) {
            if (column.isAnnotationPresent(Column.class)) {
                Column annotation = column.getAnnotation(Column.class);
                List<String> ignore = Lang.list("id", "create_time", "update_time");
                if (!ignore.contains(annotation.value())) {
                    columnNames.add(annotation.value());
                }
            }
        }
        return columnNames;
    }

    public LongtermPredictOutputPurchaseSplitAdjustDO tranFrom(LongtermPredictOutputPurchaseSplitDO data) {
        LongtermPredictOutputPurchaseSplitAdjustDO ret = new LongtermPredictOutputPurchaseSplitAdjustDO();
        ret.setTaskId(data.getTaskId());
//        ret.setVersionId();
        ret.setOutputId(data.getOutputId());
        ret.setStrategyType(data.getStrategyType());
        ret.setStatTime(data.getStartDate());
        ret.setYearMonthStr(data.getYearMonthStr());
        ret.setHalfYear(data.getHalfYear());
        ret.setQuarter(data.getQuarter());
        ret.setYear(data.getYear());
        ret.setCustomerName("常规水位");
        ret.setIndustryName("常规");
        ret.setModuleBusinessTypeName("腾讯云-公有云-通用");
        ret.setObsProjectType("常规项目");
        ret.setReason("其他");
//        ret.setRegionType();
        ret.setCampus(data.getCampus());
//        ret.setZone();
        ret.setZoneName(data.getZoneName());
        ret.setCountryName(data.getCountryName());
        ret.setDeviceType(data.getDeviceType());
        ret.setDeviceTypeCoreNum(data.getDeviceTypeCoreNum());
        ret.setPurchaseCore(data.getPurchaseCore());
        ret.setPurchaseCoreOrigin(data.getPurchaseCoreOrigin());
        ret.setPurchaseCoreDebug(data.getPurchaseCoreDebug());
        return ret;
    }


    public static String convertToExcelColumn(int columnNumber) {
        StringBuilder columnName = new StringBuilder();
        while (columnNumber >= 0) {
            // append the current character to columnName string
            columnName.insert(0, (char) ('A' + columnNumber % 26));
            // update the columnNumber
            columnNumber = columnNumber / 26 - 1;
        }
        return columnName.toString();
    }

}
