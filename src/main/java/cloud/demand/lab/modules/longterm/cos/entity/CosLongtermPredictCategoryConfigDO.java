package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.time.LocalDate;

/**
 * cos中长期预测方案配置表
 */
@Data
@Table("cos_longterm_predict_category_config")
public class CosLongtermPredictCategoryConfigDO extends BaseDO {

    /** 方案名称<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 预测时间颗粒度，6=半年<br/>Column: [interval_month] */
    @Column(value = "interval_month")
    private Integer intervalMonth;

    /** 预测粒度<br/>Column: [dims_name] */
    @Column(value = "dims_name")
    private String dimsName;

    /** 客户范围<br/>Column: [scope_customer] */
    @Column(value = "scope_customer")
    private String scopeCustomer;

    /** 资源池<br/>Column: [scope_resource_pool] */
    @Column(value = "scope_resource_pool")
    private String scopeResourcePool;

    /** 预测构成，逗号分隔，例如：快手,非快手<br/>Column: [parts] */
    @Column(value = "parts")
    private String parts;

    /** 用于模型预测的部分的名称，只有一个，例如：非快手<br/>Column: [model_part] */
    @Column(value = "model_part")
    private String modelPart;

    /** 是否区分内外部，0=不分开，1=分开；主要用于后续自研COS这种无法区分内外部的用<br/>Column: [is_separate_in_out] */
    @Column(value = "is_separate_in_out")
    private Boolean isSeparateInOut;

    /** 输入原始规模数据的表，这里仅用于标识，不会实际参与sql<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 查询规模数据条件sql，会参与实际sql<br/>Column: [where_sql] */
    @Column(value = "where_sql")
    private String whereSql;

    /** 采购用到的表名，这里仅用于标识，不会实际参与sql<br/>Column: [purchase_table_name] */
    @Column(value = "purchase_table_name")
    private String purchaseTableName;

    /** 采购量的查询条件，会参与实际sql<br/>Column: [purchase_where_sql] */
    @Column(value = "purchase_where_sql")
    private String purchaseWhereSql;

    /** 预测开始时间，CUR_MONTH表示当月<br/>Column: [predict_start] */
    @Column(value = "predict_start")
    private String predictStart;

    /** 预测结束时间，填yyyy-MM，它一般表示预测到这个月份的最后一天<br/>Column: [predict_end] */
    @Column(value = "predict_end")
    private String predictEnd;

    /** 线性拟合的输入参数的起始时间<br/>Column: [linear_start_date] */
    @Column(value = "linear_start_date")
    private LocalDate linearStartDate;

}