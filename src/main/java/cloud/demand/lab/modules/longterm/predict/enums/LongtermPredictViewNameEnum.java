package cloud.demand.lab.modules.longterm.predict.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum LongtermPredictViewNameEnum {

    CVM("CVM角度"),
    DEVICE("物理机角度"),
    INDUSTRY_DEPT("行业角度"),
    ;
    final private String name;

    LongtermPredictViewNameEnum(String name) {
        this.name = name;
    }
    public static LongtermPredictViewNameEnum getByName(String name) {
        for (LongtermPredictViewNameEnum value : LongtermPredictViewNameEnum.values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    public static LongtermPredictViewNameEnum getByCode(String code) {
        for (LongtermPredictViewNameEnum value : LongtermPredictViewNameEnum.values()) {
            if (Objects.equals(value.name(), code)) {
                return value;
            }
        }
        return null;
    }
}