package cloud.demand.lab.modules.longterm.cos.web.req;

import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import lombok.Data;

import java.util.List;

/**
 * 保存大客户历史变动数据请求
 */
@Data
public class SaveBigCustomerHistoryChangeReq {

    /**
     * 方案id，必须参数
     */
    private Long categoryId;

    /**
     * 任务id，可选参数
     * 当taskId没传时，填0，表示这份数据归属于categoryId
     * 当taskId有值时，表示属于当前的任务
     */
    private Long taskId;

    /**
     * 大客户历史变动数据列表
     */
    private List<BigCustomerHistoryChangeDTO> dataList;
}
