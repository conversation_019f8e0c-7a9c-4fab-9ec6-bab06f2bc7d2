package cloud.demand.lab.modules.longterm.cbs.entity;


import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ToString
@Table("mrpv2_common_instance_type_config")
public class Mrpv2CommonInstanceTypeConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 通用的实例类型<br/>Column: [common_instance_type] */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    /** 需要通用化的实例类型，逗号分隔<br/>Column: [instance_types] */
    @Column(value = "instance_types")
    private String instanceTypes;

    @Column(value = "use_forecast")
    private Integer useForecast;

    /** 采购新机型<br/>Column: [purchase_new_instance_type] */
    @Column(value = "purchase_new_instance_type")
    private String purchaseNewInstanceType;

    @Column(value = "cbs_instance_family")
    private String cbsInstanceFamily;

    public List<String> getInstanceTypeDict() {
        return parse(instanceTypes);
    }

    private static List<String> parse(String str) {
        if (StringTools.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split(","))
                .filter(StringTools::isNotBlank)  // 去除空值
                .collect(Collectors.toList());
    }

}