package cloud.demand.lab.modules.longterm.cbs.entity;
import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cbs_longterm_predict_ratio_detail")
public class CbsRatioDetailDO extends BaseDO {

    /** 配比方案id<br/>Column: [ratio_category_id] */
    @Column(value = "ratio_category_id")
    private Long ratioCategoryId;

    /** 机型族（AMD，Intel，其他,默认）<br/>Column: [cbs_instance_family] */
    @Column(value = "cbs_instance_family")
    private String cbsInstanceFamily;

    /** 境内外（境内境外，可选）<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 内外部（内部外部，可选）<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** 策略（EXTREME、MIDDLE、CAUTIOUS，可选）<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 一核对多少GB<br/>Column: [ratio_num] */
    @Column(value = "ratio_num")
    private BigDecimal ratioNum;

}