package cloud.demand.lab.modules.longterm.demand_delivery_data.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("longterm_demand_and_execute_machine_type_dict")
public class LongtermDemandAndExecuteMachineTypeDictDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 物理机机型族<br/>Column: [machine_type_family] */
    @Column(value = "machine_type_family")
    private String machineTypeFamily;

    /** 机型族大类<br/>Column: [machine_type_family_general] */
    @Column(value = "machine_type_family_general")
    private String machineTypeFamilyGeneral;
}
