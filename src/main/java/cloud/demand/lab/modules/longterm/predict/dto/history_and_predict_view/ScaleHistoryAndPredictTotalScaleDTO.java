package cloud.demand.lab.modules.longterm.predict.dto.history_and_predict_view;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class ScaleHistoryAndPredictTotalScaleDTO {

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("cur_core")
    private BigDecimal curCore;

    @Column("biz_range_type")
    private String bizRangeType;

    @Column("year_month_str")
    private String yearMonthStr;

    @Column("stat_time")
    private String statTime;

}
