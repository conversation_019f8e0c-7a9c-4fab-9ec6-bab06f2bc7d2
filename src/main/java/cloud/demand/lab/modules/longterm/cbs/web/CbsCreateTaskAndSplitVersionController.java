package cloud.demand.lab.modules.longterm.cbs.web;

import cloud.demand.lab.modules.longterm.cbs.service.CbsCreateTaskAndSplitVersionService;
import cloud.demand.lab.modules.longterm.cbs.web.req.task_and_split.CreateTaskAndSplitVersionReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.task_and_split.CreateTaskAndSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryForCreateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 CBS创建预测方案与拆分版本接口
 */
@JsonrpcController("/cbs_longterm_predict")
@Slf4j
public class CbsCreateTaskAndSplitVersionController {

    @Resource
    private CbsCreateTaskAndSplitVersionService cbsCreateTaskAndSplitVersionService;


    @RequestMapping
    public CreateTaskAndSplitVersionResp createTaskAndSplitVersion(@JsonrpcParam CreateTaskAndSplitVersionReq req) {
        return cbsCreateTaskAndSplitVersionService.createTaskAndSplitVersion(req);
    }

    @RequestMapping
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(@JsonrpcParam QueryCategoryAndTaskListReq req){
        return cbsCreateTaskAndSplitVersionService.queryCategoryAndTaskList(req);
    }

    @RequestMapping
    public QueryCategoryForCreateResp queryCategoryForCreate(@JsonrpcParam QueryCategoryForCreateReq req) {
        return cbsCreateTaskAndSplitVersionService.queryCategoryForCreate(req);
    }
}
