package cloud.demand.lab.modules.longterm.cos.algorithm;

import lombok.Data;

import java.util.List;

@Data
public class PredictResult {

    /**预测结果，按时间顺序正序来*/
    private List<DateNumDTO> data;
    /**降级的算法*/
    private String fallbackAlgorithm;

    /**模型原始输入 [可选]*/
    private String modelRawReq;
    /**模型原始输出 [可选]*/
    private String modelRawResp;

    public PredictResult() {}
    public PredictResult(List<DateNumDTO> data) {
        this.data = data;
    }
    public PredictResult(List<DateNumDTO> data, String fallbackAlgorithm) {
        this.data = data;
        this.fallbackAlgorithm = fallbackAlgorithm;
    }

}
