package cloud.demand.lab.modules.longterm.cbs.web;

import cloud.demand.lab.modules.longterm.cbs.service.CbsScalePredictService;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.*;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.*;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QuerySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * CBS中长期存量及其预测
 */
@JsonrpcController("/cbs_longterm_predict")
@Slf4j
public class CbsScalePredictController {
    @Resource
    private CbsScalePredictService cbsScalePredictService;

    @Resource
    private SplitService splitService;
    /**
     * 查询存量净增规模（左侧两个方框）
     */
    @RequestMapping
    public QueryCbsScalePredictTotalResp queryCbsScalePredictTotal(@JsonrpcParam QueryCbsScalePredictTotalReq req) {
        QuerySplitVersionReq querySplitVersionReq = new QuerySplitVersionReq();
        if(req.getTaskId()==null)throw new RuntimeException("未指定拆分版本");
        if(req.getRatioCategoryId()==null)throw new RuntimeException("未指定配比版本");
        querySplitVersionReq.setTaskId(req.getTaskId());
        QuerySplitVersionResp querySplitVersionResp = splitService.querySplitVersionList(querySplitVersionReq);
        Long splitVersionId = querySplitVersionResp.getSplitVersionList().get(0).getSplitVersionId();
        req.setSplitVersionId(splitVersionId);
        return cbsScalePredictService.queryCbsScalePredictTotal(req);
    }


    /**
     * 存量数据框字典
     */
    @RequestMapping
    public QueryCbsScalePredictDictResp queryCbsScalePredictDict(@JsonrpcParam QueryCbsScalePredictDictReq req) {
        return cbsScalePredictService.queryCbsScalePredictDict(req);
    }

    /**
     * 存量历史趋势图
     * @param req
     * @return
     */
    @RequestMapping
    public QueryCbsScaleHistoryResp queryCbsScaleHistory(@JsonrpcParam QueryCbsScaleScaleHistoryReq req) {
        if(req.getStartYearmonth()==null)req.setStartYearmonth("2021-01");
        if(req.getEndYearmonth()==null)req.setEndYearmonth("2050-12");
        return cbsScalePredictService.queryCbsScaleScaleHistory(req);
    }

    /**
     * 存量预测
     * @param req
     * @return
     */
    @RequestMapping
    public QueryScalePredictResp queryCbsScalePredict(@JsonrpcParam QueryCbsScalePredictReq req) {
        QuerySplitVersionReq querySplitVersionReq = new QuerySplitVersionReq();
        querySplitVersionReq.setTaskId(req.getTaskId());
        QuerySplitVersionResp querySplitVersionResp = splitService.querySplitVersionList(querySplitVersionReq);
        Long splitVersionId = querySplitVersionResp.getSplitVersionList().get(0).getSplitVersionId();
        req.setSplitVersionId(splitVersionId);
        return cbsScalePredictService.queryCbsScalePredict(req);
    }


}
