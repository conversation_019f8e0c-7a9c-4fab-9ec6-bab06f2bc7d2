package cloud.demand.lab.modules.longterm.cos.web.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PlanCosScaleDataDTO {

    /**
     * 日期，格式：yyyy-MM-dd
     */
    @Column("date")
    private LocalDate date;

    /**
     * 范围：内部、外部
     */
    @Column("scope")
    private String scope;

    /**
     * 数值
     */
    @Column("value")
    private BigDecimal value;

}
