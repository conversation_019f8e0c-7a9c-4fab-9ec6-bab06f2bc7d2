package cloud.demand.lab.modules.longterm.cbs.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CbsLongtermPredictScaleDTO {

    @Column("year_month_str")
    private String yearMonthStr;
    @Column("cbs_instance_family")
    private String cbsInstanceFamily;
    @Column("customhouse_title")
    private String customhouseTitle;
//    @Column("biz_range_type")
//    private String bizRangeType;
    @Column("strategy_type")
    private String strategyType;
    @Column("region_name")
    private String regionName;
    /**
    总核心数
     */
    @Column("sum_core")
    private BigDecimal sumCore;
    /**
     * 总磁盘数
     */
    @Column("sum_disk")
    private BigDecimal sumDisk;

    @Column("cvm_instance_type")
    private String cvmInstanceType;
    @Column("instance_type")
    private String instanceType;
    @Column("common_instance_type")
    private String commonInstanceType;

    @Column("increase_core")
    private BigDecimal increaseCore;
    @Column("increase_disk")
    private BigDecimal increaseDisk;
    public CbsLongtermPredictScaleDTO() {
    }
    // 复制构造函数：创建一个与传入对象一模一样的全新 DTO
    public CbsLongtermPredictScaleDTO(CbsLongtermPredictScaleDTO dto) {
        this.yearMonthStr = dto.getYearMonthStr();
        this.cbsInstanceFamily = dto.getCbsInstanceFamily();
        this.customhouseTitle = dto.getCustomhouseTitle();
        this.strategyType = dto.getStrategyType();
        this.regionName = dto.getRegionName();
        this.sumCore = dto.getSumCore();
        this.sumDisk = dto.getSumDisk();
        this.cvmInstanceType = dto.getCvmInstanceType();
        this.instanceType = dto.getInstanceType();
        this.commonInstanceType = dto.getCommonInstanceType();
        this.increaseCore = dto.getIncreaseCore();
        this.increaseDisk = dto.getIncreaseDisk();
    }

    public static List<CbsLongtermPredictScaleDTO> createlist(List<CbsScaleDTO> cbsScaleDTOS){
        List<CbsLongtermPredictScaleDTO> resultList=new ArrayList<>();
        for (CbsScaleDTO cbsScaleDTO : cbsScaleDTOS) {
            CbsLongtermPredictScaleDTO cbsLongtermPredictScaleDTO=new CbsLongtermPredictScaleDTO();
            cbsLongtermPredictScaleDTO.setYearMonthStr(cbsScaleDTO.getYearMonthStr());
            cbsLongtermPredictScaleDTO.setCbsInstanceFamily(null);
            cbsLongtermPredictScaleDTO.setCustomhouseTitle(cbsScaleDTO.getCustomhouseTitle());
            cbsLongtermPredictScaleDTO.setStrategyType(cbsScaleDTO.getStrategyType());
            cbsLongtermPredictScaleDTO.setRegionName(cbsScaleDTO.getRegionName());
            cbsLongtermPredictScaleDTO.setSumCore(null);
            cbsLongtermPredictScaleDTO.setSumDisk(cbsScaleDTO.getSumDisk());
            cbsLongtermPredictScaleDTO.setCvmInstanceType(cbsScaleDTO.getCvmInstanceType());
            cbsLongtermPredictScaleDTO.setInstanceType(null);
            cbsLongtermPredictScaleDTO.setCommonInstanceType(null);
            cbsLongtermPredictScaleDTO.setIncreaseCore(null);
            cbsLongtermPredictScaleDTO.setIncreaseDisk(null);
            resultList.add(cbsLongtermPredictScaleDTO);
        }return resultList;
    }
}
