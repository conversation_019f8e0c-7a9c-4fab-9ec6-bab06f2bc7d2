package cloud.demand.lab.common.excel.checker;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.excel.core.ParseContext;
import cloud.demand.lab.common.excel.core.checker.ExcelColumnValueChecker;
import java.util.List;

/**
 *  对列数据校验： 允许 null 值，值必须在{是，否}中
 */
public class WhetherOrNotChecker implements ExcelColumnValueChecker {

    @Override
    public void checkValue(int rowIndex, int columnIndex, String columnName, Object value, List<ErrorMessage> errors,
            ParseContext<?> context) {
        if (value == null) {
            return;
        }
        if ("是".equals(value.toString()) || "否".equals(value.toString())) {
            return;
        }
        ErrorMessage message = new ErrorMessage();
        message.setCol(columnIndex);
        message.setRow(rowIndex);
        message.setColName(columnName);
        message.setMessage("列：【" + columnName + "】值必须在{是，否}中，当前值【" + value + "】");
        errors.add(message);
    }
}
