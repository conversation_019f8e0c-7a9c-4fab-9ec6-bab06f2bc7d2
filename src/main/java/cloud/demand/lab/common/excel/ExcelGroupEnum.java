package cloud.demand.lab.common.excel;

import cloud.demand.lab.common.excel.core.ExcelGroup;

/**
 *   excel 组 枚举
 */
public enum ExcelGroupEnum implements ExcelGroup {

    STOCK_SUPPLY_BM_IMPORT(GroupConstant.STOCK_SUPPLY_BM_IMPORT,
            "裸金属对冲结果导入模版组","excel/stock_supply/stock_supply_bm_import.xlsx"),

    DEFAULT_CVM_PPL_IMPORT(GroupConstant.DEFAULT_CVM_PPL_IMPORT,
            "默认CVM行业PPL导入模版组", "excel/inner_process/default_cvm_import.xlsx"),

    COMD_CVM_PPL_IMPORT(GroupConstant.COMD_CVM_PPL_IMPORT,
            "云运管干预CVM导入PPL模版组", "excel/comd/ppl_comd_intervene_cvm.xlsx"),

    PPL_AND_ORDER_CVM_DEMAND(GroupConstant.PPL_AND_ORDER_CVM_DEMAND,
            "产品供需看板-CVM需求校验模版组", "excel/supply_demand/product_demand_cvm_adjust.xlsx"),

    PPL_AND_ORDER_CBS_DEMAND(GroupConstant.PPL_AND_ORDER_CBS_DEMAND,
            "产品供需看板-CBS需求校验模版组", "excel/supply_demand/product_demand_cbs_adjust.xlsx"),

    PPL_AND_ORDER_DB_DEMAND(GroupConstant.PPL_AND_ORDER_DB_DEMAND,
            "产品供需看板-数据库需求校验模版组", "excel/supply_demand/product_demand_db_adjust.xlsx"),
    ;

    private final String code;

    private final String remark;

    private final String templatePath;

    ExcelGroupEnum(String code, String remark, String templatePath) {
        this.code = code;
        this.remark = remark;
        this.templatePath = templatePath;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public String getTemplatePath() {
        return templatePath;
    }

}
