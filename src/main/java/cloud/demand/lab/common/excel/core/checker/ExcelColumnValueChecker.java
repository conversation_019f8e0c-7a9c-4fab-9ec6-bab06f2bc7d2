package cloud.demand.lab.common.excel.core.checker;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.excel.core.ParseContext;
import java.util.List;

/**
 *   excel 列数据校验器
 */
public interface ExcelColumnValueChecker extends Checker {

    /**
     *  列数据校验
     * @param rowIndex 行下标, 大于 0 ,从1开始
     * @param columnIndex 列下标, 大于 0 ,从1开始
     * @param columnName excel中列名
     * @param value excel中原始值
     * @param errors 校验结果
     * @param context 上下文信息，一般是在业务解析入口或解析过程中自定义添加的信息
     */
    void checkValue(int rowIndex, int columnIndex, String columnName, Object value,
            List<ErrorMessage> errors, ParseContext<?> context);

}
