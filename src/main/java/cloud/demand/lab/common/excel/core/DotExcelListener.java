package cloud.demand.lab.common.excel.core;

import cloud.demand.lab.common.excel.core.checker.ExcelColumnValueChecker;
import cloud.demand.lab.common.excel.core.checker.ExcelResultDataAfterConvertChecker;
import cloud.demand.lab.common.excel.core.checker.ExcelRowDataAfterConvertChecker;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.AutoConverter;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ConverterKeyBuild;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.exception.ExcelCommonException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.read.metadata.property.ExcelReadHeadProperty;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import net.sf.cglib.beans.BeanMap;
import org.nutz.lang.Strings;

/**
 *  数据读取、解析、校验统一入口
 */
public class DotExcelListener<R> implements ReadListener<Map<Integer, CellData>> {

    /** 数据结果集 */
    private List<R> resultData;

    /** 组信息 */
    private ExcelGroup excelGroup;

    /** 数据接收的实体类 */
    private Class<R> clazz;

    /** 列名和列下标的映射关系 */
    private final Map<String, Integer> columnNameAndIndexMap = new HashMap<>();

    /** 数据校验错误信息 */
    private List<ErrorMessage> errorMessages;

    /** 需要进行解析数据的列信息 */
    private List<ExcelFieldInfo> fieldInfoList;

    /** 业务自定义上下文信息，和解析相关的信息 */
    private ParseContext<R> parseContext;

    /** 将解析时遇到异常转为错误消息放到{@link #errorMessages} 中 */
    private boolean catchConvertExceptionToErrorMessage = true;


    /**
     *   new
     * @param excelGroup 组信息
     * @param clazz 数据接收的实体类
     */
    public DotExcelListener(ExcelGroup excelGroup, Class<R> clazz) {
        init(excelGroup, clazz, null, null, null, null,
                null, null, null, null);
    }

    /**
     *  new
     * @param excelGroup 组信息
     * @param clazz 数据接收的实体类
     * @param customContext 自定义上下文信息
     */
    public DotExcelListener(ExcelGroup excelGroup, Class<R> clazz, Map<String, Object> customContext) {
        init(excelGroup, clazz, null, null, customContext, null,
                null, null, null, null);
    }

    /**
     *   new
     * @param excelGroup 组信息
     * @param clazz 数据接收的实体类
     * @param resultData 数据结果集
     * @param errorMessages 数据校验错误信息
     */
    public DotExcelListener(
            ExcelGroup excelGroup, Class<R> clazz, List<R> resultData, List<ErrorMessage> errorMessages) {
        init(excelGroup, clazz, resultData, errorMessages, null, null,
                null, null, null, null);
    }

    /**
     *  new
     * @param excelGroup 组信息
     * @param clazz 数据接收的实体类
     * @param resultData 数据结果集
     * @param errorMessages 数据校验错误信息
     * @param customContext 自定义上下文信息
     */
    public DotExcelListener(
            ExcelGroup excelGroup, Class<R> clazz, List<R> resultData, List<ErrorMessage> errorMessages,
            Map<String, Object> customContext) {
        init(excelGroup, clazz, resultData, errorMessages, customContext, null,
                null, null, null, null);
    }

    /**
     *  new
     * @param excelGroup 组信息
     * @param clazz 数据接收的实体类
     * @param resultData 数据结果集
     * @param errorMessages 数据校验错误信息
     * @param customContext 自定义上下文信息
     */
    public DotExcelListener(
            ExcelGroup excelGroup, Class<R> clazz, List<R> resultData, List<ErrorMessage> errorMessages,
            Map<String, Object> customContext, Map<String, List<ExcelColumnValueChecker>> valueCheckers,
            List<ExcelRowDataAfterConvertChecker<? super R>> rowCheckers,
            List<ExcelResultDataAfterConvertChecker<? super R>> resultCheckers,
            Map<String, Converter<?>> columnConvertMap, List<ExcelFieldInfo> fieldInfos) {
        init(excelGroup, clazz, resultData, errorMessages, customContext, valueCheckers, rowCheckers, resultCheckers,
                columnConvertMap, fieldInfos);
    }

    private void init(ExcelGroup excelGroup, Class<R> clazz, List<R> resultData,
            List<ErrorMessage> errorMessages, Map<String, Object> customContext,
            Map<String, List<ExcelColumnValueChecker>> valueCheckers,
            List<ExcelRowDataAfterConvertChecker<? super R>> rowCheckers,
            List<ExcelResultDataAfterConvertChecker<? super R>> resultCheckers,
            Map<String, Converter<?>> columnConvertMap, List<ExcelFieldInfo> fieldInfos) {
        if (excelGroup == null || excelGroup.getCode() == null) {
            throw new IllegalArgumentException("the params excelGroup must not null");
        }
        if (clazz == null) {
            throw new IllegalArgumentException("the params clazz must not null");
        }
        this.excelGroup = excelGroup;
        this.resultData = resultData == null ? new ArrayList<>() : resultData;
        this.clazz = clazz;
        this.errorMessages = errorMessages == null ? new ArrayList<>() : errorMessages;
        prepareFieldInfoList(fieldInfos);
        prepareContext(customContext, valueCheckers, rowCheckers, resultCheckers, columnConvertMap);
    }

    private void prepareFieldInfoList(List<ExcelFieldInfo> fieldInfos) {
        this.fieldInfoList = ExcelGroupFieldHelper.getByExcelGroup(excelGroup);
        if (fieldInfos != null) {
            if (this.fieldInfoList.size() == 0) {
                this.fieldInfoList.addAll(fieldInfos);
            } else {
                A : for (ExcelFieldInfo custom : fieldInfos) {
                    B : for (int i = 0; i < fieldInfoList.size(); i++) {
                        if (ExcelFieldInfo.isSameName(custom, fieldInfoList.get(i))) {
                            // 相同则替换，使用自定义注入的
                            fieldInfoList.set(i, custom);
                            continue A;
                        }
                    }
                    // 没有相同的直接添加
                    fieldInfoList.add(custom);
                }
            }
        }
        if (this.fieldInfoList.size() == 0) {
            throw new ExcelCommonException("please check register info, can`t find DotExcel info for group:"
                    + excelGroup.getCode());
        }
    }

    private void prepareContext(Map<String, Object> customContext,
            Map<String, List<ExcelColumnValueChecker>> valueCheckers,
            List<ExcelRowDataAfterConvertChecker<? super R>> rowCheckers,
            List<ExcelResultDataAfterConvertChecker<? super R>> resultCheckers,
            Map<String, Converter<?>> columnConvertMap) {
        Map<String, String> keyFiledName = new HashMap<>();
        for (ExcelFieldInfo excelFieldInfo : this.fieldInfoList) {
            if (excelFieldInfo == null) {
                continue;
            }
            keyFiledName.put(excelFieldInfo.getJavaFieldName(), excelFieldInfo.getExcelColumnName());
        }

        parseContext = new ParseContext<R>();
        parseContext.setCustomContext(customContext == null ? new HashMap<>() : customContext);
        parseContext.setFieldNameAndColumnNameMap(Collections.unmodifiableMap(keyFiledName));
        if (valueCheckers != null) {
            Map<String, List<ExcelColumnValueChecker>> copyValueCheckers = new HashMap<>();
            valueCheckers.forEach(
                    (filedName, checkers) -> copyValueCheckers.put(filedName, Collections.unmodifiableList(checkers)));
            parseContext.setCustomValueCheckers(Collections.unmodifiableMap(copyValueCheckers));
        } else {
            parseContext.setCustomValueCheckers(Collections.unmodifiableMap(new HashMap<>()));
        }
        if (rowCheckers != null) {
            parseContext.setCustomRowCheckers(Collections.unmodifiableList(rowCheckers));
        } else {
            parseContext.setCustomRowCheckers(Collections.unmodifiableList(new ArrayList<>()));
        }
        if (resultCheckers != null) {
            parseContext.setCustomResultCheckers(Collections.unmodifiableList(resultCheckers));
        } else {
            parseContext.setCustomResultCheckers(Collections.unmodifiableList(new ArrayList<>()));
        }
        if (columnConvertMap != null) {
            parseContext.setColumnConvertMap(Collections.unmodifiableMap(columnConvertMap));
        } else {
            parseContext.setColumnConvertMap(Collections.unmodifiableMap(new HashMap<>()));
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (catchConvertExceptionToErrorMessage) {
            Integer colIndex = parseContext.currentColumn > 0 ? parseContext.currentColumn : null;
            Integer rowIndex = parseContext.currentRow > 0 ? parseContext.currentRow : null;
            String colName = parseContext.currentFieldInfo == null ? null
                    : parseContext.currentFieldInfo.getExcelColumnName();
            if (colName == null && colIndex != null) {
                List<String> colNames = getColNameByColIndex(colIndex);
                if (ListUtils.isNotEmpty(colNames)) {
                    colName = Strings.join(" ; ", colNames);
                }
            }
            if (exception instanceof ExcelDataConvertException) {
                ExcelDataConvertException convertException = ((ExcelDataConvertException) exception);
                if (colIndex == null) {
                    colIndex = convertException.getColumnIndex();
                }
                String excelData = convertException.getCellData() == null ? ""
                        : convertException.getCellData().toString();
                String msg = "错误提示【" + convertException.getMessage() + "】；excel数据【" + excelData + "】";
                if (rowIndex == null) {
                    rowIndex = convertException.getRowIndex();
                }
                ErrorMessage error = new ErrorMessage(rowIndex, colIndex, colName, msg);
                errorMessages.add(error);
            } else if (exception instanceof ExcelAnalysisStopException) {
                String msg = "解析中止，错误提示【" + exception.getMessage() + "】";
                ErrorMessage error = new ErrorMessage(rowIndex, colIndex, colName, msg);
                errorMessages.add(error);
                // 抛出 ExcelAnalysisStopException 异常，结束解析
                throw exception;
            } else {
                String msg = "错误提示【" + exception.getClass() + "：" + exception.getMessage() + "】";
                ErrorMessage error = new ErrorMessage();
                error.setMessage(msg);
                error.setRow(rowIndex);
                error.setColName(colName);
                error.setCol(colIndex);
                errorMessages.add(error);
            }
            return;
        }
        throw exception;
    }

    private List<String> getColNameByColIndex(int colIndex) {
        List<String> colNames = new ArrayList<>();
        columnNameAndIndexMap.forEach((s, integer) -> {
            if (integer != null && integer == colIndex) {
                colNames.add(s);
            }
        });
        return colNames;
    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        // easyExcel中，row、column都是从 0 开始的，而dot封装的row、column都是从 1 开始的
        parseContext.currentParseInfoSet(null, null, null);
        int rowIndex = context.readRowHolder().getRowIndex();
        int currentHeadRowNumber = context.readSheetHolder().getHeadRowNumber();
        parseContext.setHeadRowNumber(currentHeadRowNumber);
        parseContext.currentParseInfoSet(rowIndex + 1, null, null);
        if (rowIndex + 1 == currentHeadRowNumber) {
            headMap.forEach((columnIndex, cellData) ->
                    columnNameAndIndexMap.put(cellData.getStringValue(), columnIndex + 1));
            // 校验标题行是否与模版标题一致
            titleCheck();
        }
        // 行数据解析完成，重置当前行
        parseContext.currentParseInfoSet(null, null, null);
    }

    private void titleCheck() {
        List<String> lossTitle = new ArrayList<>();
        for (ExcelFieldInfo fieldInfo : fieldInfoList) {
            Integer columnIndex = columnNameAndIndexMap.get(fieldInfo.getExcelColumnName());
            if (columnIndex == null) {
                lossTitle.add(fieldInfo.getExcelColumnName());
            }
        }
        if (ListUtils.isNotEmpty(lossTitle)) {
            throw new ExcelAnalysisStopException(
                    "导入excel的表头列标题数据与模版标题数据不符，请检查是否是使用了错误的excel导入模版，当前缺失标题列："
                            + lossTitle);
        }
    }

    @Override
    public void invoke(Map<Integer, CellData> data, AnalysisContext context) {
        parseContext.currentParseInfoSet(null, null, null);
        if (parseContext.getColumnNameAndIndexMap() == null) {
            parseContext.setColumnNameAndIndexMap(Collections.unmodifiableMap(columnNameAndIndexMap));
        }
        Map<String, Object> map = new HashMap<>();
        ReadHolder currentReadHolder = context.currentReadHolder();
        // easyExcel中，row、column都是从 0 开始的，而dot封装的row、column都是从 1 开始的
        int rowIndex = context.readRowHolder().getRowIndex() + 1;
        parseContext.currentParseInfoSet(rowIndex + 1, null, null);

        ExcelReadHeadProperty excelReadHeadProperty = currentReadHolder.excelReadHeadProperty();
        Map<Integer, ExcelContentProperty> contentPropertyMap = excelReadHeadProperty.getContentPropertyMap();
        List<ErrorMessage> columnErrors = new ArrayList<>();
        for (ExcelFieldInfo fieldInfo : fieldInfoList) {
            Integer columnIndex = columnNameAndIndexMap.get(fieldInfo.getExcelColumnName());
            parseContext.currentParseInfoSet(rowIndex, columnIndex, fieldInfo);
            if (columnIndex == null) {
                ErrorMessage message = new ErrorMessage(rowIndex, null, fieldInfo.getExcelColumnName(),
                        "缺少指定excel 列，列名：" + fieldInfo.getExcelColumnName());
                columnErrors.add(message);
                continue;
            }
            CellData<?> cellData = data.get(columnIndex - 1);
            Object value;
            if (cellData == null) {
                value = null;
                // excel列字段原始值检查
                columnCheck(rowIndex, columnIndex, value, fieldInfo, columnErrors);
            } else {
                ExcelContentProperty excelContentProperty = contentPropertyMap.get(columnIndex);
                switch (cellData.getType()) {
                    case NUMBER:
                        value = cellData.getNumberValue();
                        break;
                    case BOOLEAN:
                        value = cellData.getBooleanValue();
                        break;
                    default:
                        value = cellData.getStringValue();
                        break;
                }
                // excel列字段原始值检查
                columnCheck(rowIndex, columnIndex, value, fieldInfo, columnErrors);
                try {
                    Converter<?> converter = parseContext.getColumnConvertMap().get(fieldInfo.getJavaFieldName());
                    if (converter == null) {
                        converter = fieldInfo.getConverter();
                    }
                    if (converter == null || converter.getClass().equals(AutoConverter.class)) {
                        converter = currentReadHolder.converterMap()
                                .get(ConverterKeyBuild.buildKey(fieldInfo.getFieldClazz(), cellData.getType()));
                        if (converter == null) {
                            converter = new AutoConverter();
                        }
                    }
                    value = converter.convertToJavaData(cellData, excelContentProperty,
                            currentReadHolder.globalConfiguration());
                } catch (Exception e) {
                    throw new ExcelDataConvertException(rowIndex, columnIndex, cellData, excelContentProperty,
                            "Convert data " + cellData + " to " + clazz + " error ", e);
                }
            }

            if (value != null) {
                map.put(fieldInfo.getJavaFieldName(), value);
            }
        }
        // 列数据解析完成，重置当前列
        parseContext.currentParseInfoSet(rowIndex, null, null);
        if (map.isEmpty()) {
            return;
        }
        if (!columnErrors.isEmpty()) {
            errorMessages.addAll(columnErrors);
        }
        R result;
        try {
            result = clazz.newInstance();
        } catch (Exception e) {
            throw new ExcelDataConvertException(rowIndex, 0, null, null,
                    "Can not instance class: " + excelReadHeadProperty.getHeadClazz().getName(), e);
        }
        BeanMap.create(result).putAll(map);
        // 转换后的行数据检查
        rowDataCheck(rowIndex, result);
        resultData.add(result);
        // 行数据解析完成，重置当前行
        parseContext.currentParseInfoSet(null, null, null);
    }

    /** excel列字段原始值检查 */
    private void columnCheck(int rowIndex, int columnIndex, Object value, ExcelFieldInfo fieldInfo,
            List<ErrorMessage> columnErrors) {
        if (fieldInfo == null) {
            return;
        }
        if (fieldInfo.getColumnValueChecker() != null) {
            for (ExcelColumnValueChecker columnValueChecker : fieldInfo.getColumnValueChecker()) {
                // 统一注解注入的检查器
                if (columnValueChecker == null) {
                    continue;
                }
                columnValueChecker.checkValue(rowIndex, columnIndex, fieldInfo.getExcelColumnName(),
                        value, columnErrors, parseContext);
            }
        }

        if (parseContext.getCustomValueCheckers() != null) {
            // 自定义的注入的检查器
            List<ExcelColumnValueChecker> customChecker = parseContext.getCustomValueCheckers()
                    .get(fieldInfo.getJavaFieldName());
            if (customChecker != null) {
                for (ExcelColumnValueChecker checker : customChecker) {
                    if (checker == null) {
                        continue;
                    }
                    checker.checkValue(rowIndex, columnIndex, fieldInfo.getExcelColumnName(),
                            value, columnErrors, parseContext);
                }
            }
        }
    }

    /** 转换后的行数据检查 */
    private void rowDataCheck(int rowIndex, R rowData) {
        if (parseContext.getCustomRowCheckers() != null) {
            for (ExcelRowDataAfterConvertChecker<? super R> checker : parseContext.getCustomRowCheckers()) {
                if (checker == null) {
                    continue;
                }
                checker.checkRowDataAfterConvert(rowIndex, rowData, errorMessages, parseContext);
            }
        }
    }

    /** 结果集数据检查 */
    private void resultDataCheck() {
        if (parseContext.getCustomResultCheckers() != null) {
            for (ExcelResultDataAfterConvertChecker<? super R> checker : parseContext.getCustomResultCheckers()) {
                if (checker == null) {
                    continue;
                }
                checker.checkResultDataAfterConvert(resultData, errorMessages, parseContext);
            }
        }
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {

    }

    @Override
    @SneakyThrows
    public void doAfterAllAnalysed(AnalysisContext context) {
        try {
            // excel解析完成后，结果集数据检查
            resultDataCheck();
            if (ListUtils.isNotEmpty(this.errorMessages)) {
                this.errorMessages.removeIf(Objects::isNull);
            }
        } catch (Exception e) {
            onException(e, context);
        }
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return true;
    }

    /** 返回数据校验错误信息 */
    public List<ErrorMessage> getErrorMessages() {
        return errorMessages;
    }

    /** 返回数据结果集 */
    public List<R> getResultData() {
        return resultData;
    }

}
