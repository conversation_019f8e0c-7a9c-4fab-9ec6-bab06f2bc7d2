package cloud.demand.lab.common.excel.core.checker;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.excel.core.ParseContext;
import java.util.List;

/**
 *  excel 数据结果集校验器
 */
public interface ExcelResultDataAfterConvertChecker<T> extends Checker {

    /**
     *   校验excel数据结果集
     * @param resultData excel数据结果集
     * @param errors 校验信息
     * @param context 上下文信息，一般是在业务解析入口或解析过程中自定义添加的信息
     */
    <R extends T> void checkResultDataAfterConvert(List<R> resultData, List<ErrorMessage> errors,
            ParseContext<R> context);

}
