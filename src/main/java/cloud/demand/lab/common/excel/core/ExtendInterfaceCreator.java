package cloud.demand.lab.common.excel.core;

import cloud.demand.lab.common.excel.core.checker.ExcelColumnValueChecker;
import com.alibaba.excel.converters.Converter;

/**
 *  相关扩展接口的创建器
 */
public interface ExtendInterfaceCreator {

    /**
     *   获取{@link Converter} 接口实现类的实例对象
     * @param clazz 实现类
     */
    Converter<?> getConvert(Class<? extends Converter<?>> clazz);

    /**
     *   获取{@link ExcelColumnValueChecker} 接口实现类的实例对象
     * @param clazz 实现类
     */
    ExcelColumnValueChecker getColumnChecker(Class<? extends ExcelColumnValueChecker> clazz);

}
