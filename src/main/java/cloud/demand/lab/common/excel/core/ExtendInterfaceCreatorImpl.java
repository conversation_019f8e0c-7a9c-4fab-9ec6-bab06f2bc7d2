package cloud.demand.lab.common.excel.core;

import cloud.demand.lab.common.excel.core.checker.ExcelColumnValueChecker;
import com.alibaba.excel.converters.Converter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ExtendInterfaceCreatorImpl implements ExtendInterfaceCreator {

    private static final Logger logger = LoggerFactory.getLogger(ExtendInterfaceCreatorImpl.class);

    @Override
    public Converter<?> getConvert(Class<? extends Converter<?>> clazz) {
        Converter<?> converter = null;
        try {
            converter = clazz.newInstance();
        } catch (Exception e) {
            logger.error("get converter from newInstance failed. convert clazz:{}. exception:{}",
                    clazz.getName(), e.getMessage());
        }
        return converter;
    }

    @Override
    public ExcelColumnValueChecker getColumnChecker(Class<? extends ExcelColumnValueChecker> clazz) {
        ExcelColumnValueChecker checker = null;
        try {
            checker = clazz.newInstance();
        } catch (Exception e) {
            logger.error("get converter from newInstance failed. convert clazz:{}. exception:{}",
                    clazz.getName(), e.getMessage());
        }
        return checker;
    }

}
