package cloud.demand.lab.common.entity;

import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandAuthDO;
import com.google.common.base.Splitter;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UserPermissionDto {

    @NotNull(message = "user不能为空")
    String user;
    @NotNull(message = "role不能为空")
    String role;
    List<String> product;
    List<String> industry;
    List<String> warZone;
    List<String> center;
    List<String> customer;

    /**
     * 是否全行业
     */
    Boolean isAllIndustry;

    /**
     * 是否全战区
     */
    Boolean isAllWarZone;

    /**
     * 是否全客户
     */
    Boolean isAllCustomer;

    /**
     * 是否全产品
     */
    Boolean isAllProduct;

    public static UserPermissionDto form(IndustryDemandAuthDO auth) {
        UserPermissionDto dto = new UserPermissionDto();
        dto.setUser(auth.getUserName());
        dto.setRole(auth.getRole());
        dto.setProduct(Splitter.on(";").omitEmptyStrings().trimResults().splitToList(auth.getProduct()));
        dto.setIndustry(Splitter.on(";").omitEmptyStrings().trimResults().splitToList(auth.getIndustry()));
        dto.setWarZone(Splitter.on(";").omitEmptyStrings().trimResults().splitToList(auth.getWarZoneName()));
        dto.setCenter(Splitter.on(";").omitEmptyStrings().trimResults().splitToList(auth.getCenterName()));
        dto.setCustomer(Splitter.on(";").omitEmptyStrings().trimResults().splitToList(auth.getCommonCustomerName()));
        dto.setIsAllCustomer(auth.getIsAllCustomer());
        dto.setIsAllWarZone(auth.getIsAllWarZone());
        dto.setIsAllIndustry(auth.getIsAllIndustry());
        dto.setIsAllProduct(auth.getIsAllProduct());
        return dto;
    }
}
