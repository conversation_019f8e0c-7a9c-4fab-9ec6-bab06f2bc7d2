package cloud.demand.lab.common.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class BaseUserDO extends BaseDO {

    /**
     * 创建人<br/>Column: [creator]
     */
    @Column(value = "creator", insertValueScript = "cloud.demand.lab.common.utils.LoginUtils.getUserName()")
    private String creator;

    /**
     * 最近改动人<br/>Column: [updater]
     */
    @Column(value = "updater", updateValueScript = "cloud.demand.lab.common.utils.LoginUtils.getUserName()",
            insertValueScript = "cloud.demand.lab.common.utils.LoginUtils.getUserName()")
    private String updater;

}
