package cloud.demand.lab.common.task_log.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("task_log")
public class TaskLogDO {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 任务开始时的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 任务结束时的时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 耗时(毫秒)<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name", maxStringLength = 255)
    private String taskName;

    /** 运行的机器ip<br/>Column: [run_ip] */
    @Column(value = "run_ip", maxStringLength = 128)
    private String runIp;

    /** 超时时长<br/>Column: [timeout] */
    @Column(value = "timeout")
    private Integer timeout;

    /** 任务参数<br/>Column: [args] */
    @Column(value = "args", insertValueScript = "''", maxStringLength = 1024)
    private String args;

    /** 任务执行状态<br/>Column: [status] */
    @Column(value = "status", maxStringLength = 32)
    private String status;

    /** 任务报错信息<br/>Column: [error_msg] */
    @Column(value = "error_msg", insertValueScript = "''", maxStringLength = 10200)
    private String errorMsg;


}
