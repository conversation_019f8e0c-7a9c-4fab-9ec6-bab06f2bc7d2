package cloud.demand.lab.common.task_log.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum TaskRunLogLevelEnum {

    ERROR("ERROR"),
    WARN("WARN");

    private final String code;

    TaskRunLogLevelEnum(String code) {
        this.code = code;
    }

    public static String getCodeByEnum(TaskRunLogLevelEnum logLevelEnum){
        for (TaskRunLogLevelEnum value : TaskRunLogLevelEnum.values()) {
            if (Objects.equals(value, logLevelEnum)){
                return value.getCode();
            }
        }
        return "";
    }
}
