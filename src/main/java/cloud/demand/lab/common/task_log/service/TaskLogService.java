package cloud.demand.lab.common.task_log.service;

import cloud.demand.lab.common.task_log.entity.TaskRunLogDO;
import cloud.demand.lab.common.task_log.enums.TaskRunLogLevelEnum;
import com.pugwoo.dbhelper.DBHelper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class TaskLogService {

    @Resource
    private DBHelper cdCommonDbHelper;

    /**
     * 生成TaskRunLogDO数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void genRunLog(String taskName, String methodName, String errorMsg){
        TaskRunLogDO cdTaskRunLogDO = new TaskRunLogDO(taskName, methodName, errorMsg);
        try {
            cdCommonDbHelper.insert(cdTaskRunLogDO);
        } catch (Exception e) {
            log.info(e.getMessage());
        }    }

    /**
     * 生成TaskRunLogDO数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void genRunWarnLog(String taskName, String methodName, String errorMsg){
        TaskRunLogDO cdTaskRunLogDO = new TaskRunLogDO(taskName, methodName, errorMsg, TaskRunLogLevelEnum.WARN);
        try {
            cdCommonDbHelper.insert(cdTaskRunLogDO);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }


}
