package cloud.demand.lab.common.task_log.service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.core.annotation.AliasFor;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作为TaskLog保存数据的标志注解
 * 加了这个注解的定时任务方法，会自动将定时任务方法的执行情况保存到cd_task_log表中
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskLog {


    /**
     * 可以不填，默认方法名字
     * @return string
     */
    @AliasFor("taskName")
    String value() default "";

    /** 定时任务名称 */
    @AliasFor("value")
    String taskName() default "";


    /** 超时时长(秒)，默认值为-1
     *  若使用者已知任务执行的耗时上限时，最好指定这个属性以更好监控任务执行
     */
    int timeout() default -1;
}
