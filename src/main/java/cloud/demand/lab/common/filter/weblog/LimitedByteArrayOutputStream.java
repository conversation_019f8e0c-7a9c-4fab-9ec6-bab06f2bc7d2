package cloud.demand.lab.common.filter.weblog;

import lombok.Getter;

import java.io.ByteArrayOutputStream;

public class LimitedByteArrayOutputStream extends ByteArrayOutputStream {

    private final int maxSize;

    @Getter
    private int actualCount = 0;

    /**
     * 构造方法，设置最大允许的字节数
     *
     * @param maxSize 最大允许的字节数
     */
    public LimitedByteArrayOutputStream(int maxSize) {
        super();
        this.maxSize = maxSize;
    }

    @Override
    public synchronized void write(int b) {
        actualCount++;
        if (count >= maxSize) {
            return; // ignore
        }
        super.write(b);
    }

    @Override
    public synchronized void write(byte[] b, int off, int len) {
        actualCount += len;
        if (count >= maxSize) {
            return; // ignore
        }
        if (count + len > maxSize) {
            len = maxSize - count;
        }
        super.write(b, off, len);
    }

}