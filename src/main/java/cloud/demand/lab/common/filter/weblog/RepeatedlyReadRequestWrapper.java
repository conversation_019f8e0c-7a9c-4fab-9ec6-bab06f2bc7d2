package cloud.demand.lab.common.filter.weblog;

import com.pugwoo.wooutils.io.IOUtils;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

/**
 * 自定义HttpServletRequest包装类，规避只能读取一次输入流的问题
 * <p>
 * 说明：这个方法可能导致恶意攻击使内存爆满，同时这个方法复制了一份内存，也降低了效率
 * 所以线上不建议使用这个wrapper。
 * 还有一种方案是超过10M则存磁盘也有问题，没有好的时间点去清理文件，
 * 另外1T的磁盘也存不了多少次请求（10万次）何况一般也没有1T的磁盘。
 * <p>
 * <a href="https://aiwwb.com/index.php/archives/20/">...</a>
 * <p>
 * 说明：原来的WebRequestWrapper版本是网上最常见的版本，<br>
 * 但它不支持spring boot 2.2.x，原因是2.2.x以后的版本中，不像2.1.x会调一下request的parseParameter方法
 * <p>
 * 这个版本能支持：
 * 1. GET queryString Spring这边支持对象和参数方式接收
 * 2. POST queryString Spring这边支持对象和参数方式接收
 * 3. POST json request body
 * 4. POST 上传文件
 * 5. GET 下载文件
 */
public class RepeatedlyReadRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body; // not null

    private final HttpServletRequest rawRequest;

    public RepeatedlyReadRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.rawRequest = request;

        // 上传文件和解析POST queryString是特殊的，交给tomcat自己去解析，后面再直接从Parts中拿来打log就好
        if (!isMultipart() && !isPostQueryString()) {
            body = IOUtils.readAll(request.getInputStream());
        } else {
            body = new byte[0];
        }
    }

    /**
     * 是否是上传文件
     */
    public boolean isMultipart() {
        String contentType = rawRequest.getContentType();
        return contentType != null && contentType.startsWith("multipart/form-data");
    }

    /**
     * 是否是POST queryString
     */
    public boolean isPostQueryString() {
        String method = rawRequest.getMethod();
        String contentType = rawRequest.getContentType();
        return "POST".equals(method) && contentType != null
                && contentType.startsWith("application/x-www-form-urlencoded");
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream byteArrayIns = new ByteArrayInputStream(body);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() {
                return byteArrayIns.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
    }

}