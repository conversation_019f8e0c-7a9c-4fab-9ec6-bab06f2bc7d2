package cloud.demand.lab.common.column_map;

import cloud.demand.lab.common.column_map.setter.SetDateInfoByDate.SetDateInfoEnum;
import cloud.demand.lab.common.column_map.setter.SetRegionInfoByRegionName.SetRegionInfoByNameEnum;
import cloud.demand.lab.common.column_map.setter.SetZoneInfoByZoneId.SetZoneInfoEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface MapValue {

    /**
     * 设置腾讯云CVM 可用区的信息
     */
    SetZoneInfoEnum setZoneInfoByZoneId() default SetZoneInfoEnum.NONE;

    /**
     * 关联日期信息
     */
    SetDateInfoEnum setDateInfoByDate() default SetDateInfoEnum.NONE;


    /**
     * 关联region_name 能关联到的信息, 需要DO实现 SetRegionInfoByRegionName 接口
     * 1、CVM 的国家名字 SetRegionInfoByNameEnum.COUNTRY_NAME， e.g. 美国，新加坡
     * 2、CVM 的境内外信息 SetRegionInfoByNameEnum.CUSTOMHOUSE_TITLE, e.g. 境内
     * 3、CVM 的REGION，SetRegionInfoByNameEnum.REGION, e.g. bj
     */
    SetRegionInfoByNameEnum setRegionInfoByRegionName() default SetRegionInfoByNameEnum.NONE;



}
