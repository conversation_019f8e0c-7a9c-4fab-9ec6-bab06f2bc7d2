package cloud.demand.lab.common.column_map;

import cloud.demand.lab.common.column_map.setter.SetDateInfoByDate;
import cloud.demand.lab.common.column_map.setter.SetZoneInfoByZoneId;
import java.time.LocalDate;
import lombok.Data;

@Data
public class DemoEntity implements SetZoneInfoByZoneId, SetDateInfoByDate {

    private Long zoneId;

    @Override
    public Long getZoneId() {
        return zoneId;
    }

    @MapValue(setZoneInfoByZoneId = SetZoneInfoEnum.ZONE_NAME)
    private String zoneName;

    private LocalDate date;

    @Override
    public LocalDate getDate() {
        return date;
    }

    @MapValue(setDateInfoByDate = SetDateInfoEnum.YEAR)
    private Integer year;

    @MapValue(setDateInfoByDate = SetDateInfoEnum.MONTH)
    private Integer month;

    public static void main(String[] args) {
        DemoEntity dataEntity = new DemoEntity();
        dataEntity.setDate(LocalDate.now());
        dataEntity.setZoneId(1343L);

        //MapValue, 使用了这个注解，不需要再写set方法了
        dataEntity.applyMapValue();

        System.out.println(dataEntity);

    }
}
