package cloud.demand.lab.common.config;

import cloud.demand.lab.common.excel.core.ExcelGroupFieldHelper;
import cloud.demand.lab.common.excel.core.ExtendInterfaceCreator;
import cloud.demand.lab.common.excel.core.ExtendInterfaceCreatorImpl;
import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Component;

/**
 *  dotExcel 的 spring自动配置 <br/>
 *  <ul>
 *      <li>生成扫描标注类 {@link DotExcelEntity} 的类，来注册到 {@link ExcelGroupFieldHelper} 中</li>
 *      <li>指定具体的 {@link ExtendInterfaceCreator} </li>
 *  </ul>
 */
@Component
@ComponentScan(value = "cloud.demand.lab.*", excludeFilters = {
        @ComponentScan.Filter(type = FilterType.CUSTOM,classes = {DotExcelAutoConfig.class})})
public class DotExcelAutoConfig implements ApplicationRunner, TypeFilter {

    /** 所有 dotExcel 的实体类信息 */
    private static final List<Class> DOT_EXCEL_ENTITY_CLASSES = new ArrayList<>();

    /** 注册 */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        ExcelGroupFieldHelper.registerExtendInterfaceCreator(new ExtendInterfaceCreatorImpl());
        for (Class dotExcelEntityClass : DOT_EXCEL_ENTITY_CLASSES) {
            ExcelGroupFieldHelper.registerExcelFieldClass(dotExcelEntityClass);
        }
    }

    /** 扫描 */
    @Override
    public boolean match(MetadataReader metadataReader, MetadataReaderFactory metadataReaderFactory)
            throws IOException {
        AnnotationMetadata c = metadataReader.getAnnotationMetadata();
        if (c.hasAnnotation(DotExcelEntity.class.getName())) {
            try {
                Class clazz = Class.forName(c.getClassName());
                DOT_EXCEL_ENTITY_CLASSES.add(clazz);
            } catch (ClassNotFoundException e) {
                // non
            }
        }
        return false;
    }
}
