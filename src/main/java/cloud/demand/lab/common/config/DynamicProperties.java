package cloud.demand.lab.common.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import yunti.boot.config.DynamicProperty;

import java.util.Map;
import java.util.function.Supplier;

public class DynamicProperties {

    private static final Supplier<String> crpUrl =
            DynamicProperty.create("crpUrl", "");

    public static String getCrpUrl() {
        return crpUrl.get();
    }

    private static final Supplier<Integer> restTemplateConnectTimeoutSeconds =
            DynamicProperty.create("app.config.rest-template-connect-timeout-seconds", "2",
                    Integer::parseInt);

    private static final Supplier<Integer> restTemplateReadTimeoutSeconds =
            DynamicProperty.create("app.config.rest-template-read-timeout-seconds", "10",
                    Integer::parseInt);

    private static final Supplier<String> erpDemandForecastUrl =
            DynamicProperty.create("url.erp.demand.forecast.url", "");

    private static final Supplier<Map<String, String>> rrpERPCityConverterMap =
            DynamicProperty.create("map.rrp.city.converter", "{\"劳登\":\"圣克拉拉\", \"默费尔登\":\"法兰克福\"}",
                    e -> {
                        ObjectMapper objectMapper = new ObjectMapper();
                        return objectMapper.readValue(e, new TypeReference<Map<String, String>>() {
                        });
                    });

    private static final Supplier<Map<String, String>> rrpERPCountryConverterMap =
            DynamicProperty.create("map.rrp.country.converter", "{\"美西\":\"美国\", \"美东\":\"美国\"}",
                    e -> {
                        ObjectMapper objectMapper = new ObjectMapper();
                        return objectMapper.readValue(e, new TypeReference<Map<String, String>>() {
                        });
                    });

    /**
     * 是否使用老的ch，133
     */
    private static final Supplier<String> useOldCh =
            DynamicProperty.create("app.useOldCh", "true");

    /**
     * 是否读老库
     */
    private static final Supplier<String> readOldCh =
            DynamicProperty.create("app.readOldCh", "true");

    /**
     * 进销存中CVM、裸金属、GPU腾讯云上新数据起始日期 yyyy-MM-dd格式
     */
    private static final Supplier<String> JXC_CLOUD_NEW_DATE =
            DynamicProperty.create("jxc.cloud.newDataDate", "");

    private static final Supplier<String> gpuPrefix =
            DynamicProperty.create("gpuPrefix","G;PN;BMG;H");

    private static final Supplier<String> idcDataAuthorization =
            DynamicProperty.create("idcDataAuthorization","");

    public static String jxcCloudNewDataDate() {
        return JXC_CLOUD_NEW_DATE.get();
    }

    public static String getGpuPrefix() {
        return gpuPrefix.get();
    }

    public static String getIdcDataAuthorization() {
        return idcDataAuthorization.get();
    }

    public static Integer restTemplateConnectTimeoutSeconds() {
        return restTemplateConnectTimeoutSeconds.get();
    }

    public static Integer restTemplateReadTimeoutSeconds() {
        return restTemplateReadTimeoutSeconds.get();
    }

    public static String erpDemandForecastUrl() {
        return erpDemandForecastUrl.get();
    }

    public static Map<String, String> rrpERPCityConverterMap() {
        return rrpERPCityConverterMap.get();
    }

    public static Map<String, String> rrpERPCountryConverterMap() {
        return rrpERPCountryConverterMap.get();
    }

    public static Boolean useOldCh() {
        return "true".equals(useOldCh.get());
    }

    public static Boolean readOldCh() {
        return "true".equals(readOldCh.get());
    }
}
