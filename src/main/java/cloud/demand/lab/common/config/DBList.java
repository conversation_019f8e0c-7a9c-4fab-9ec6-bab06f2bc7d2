package cloud.demand.lab.common.config;

import cloud.demand.lab.common.utils.SpringUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.exception.NullKeyValueException;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 */
@Slf4j
public class DBList {

    /**
     * 解决insert 不好找地方的问题
     */
    @Data
    @AllArgsConstructor
    public static class DBHelperWithClz<T> {

        Class<T> clz;
        DBHelper dbHelper;

        @SneakyThrows(value = ClassNotFoundException.class)
        public static <T> DBHelperWithClz<T> create(DBHelper dbHelper) {
            String className = Thread.currentThread().getStackTrace()[2].getClassName(); // 获取调用者的类名
            @SuppressWarnings("unchecked") Class<T> callerClass = (Class<T>) Class.forName(className);
            return new DBHelperWithClz<>(callerClass, dbHelper);
        }

        public List<T> getAll() {
            return dbHelper.getAll(clz);
        }

        public List<T> getAll(String postSql, Object... args) {
            return dbHelper.getAll(clz, postSql, args);
        }

        public T getByKey(Object keyValue) throws NullKeyValueException {
            return dbHelper.getByKey(clz, keyValue);
        }

        public int insert(Collection<?> list) {
            return dbHelper.insert(list);
        }

        public <DO> int insert(DO t) {
            return dbHelper.insert(t);
        }

        public boolean isExist(String postSql, Object... args) {
            return dbHelper.isExist(clz, postSql, args);
        }

        public T getOne(String postSql, Object... args) {
            return dbHelper.getOne(clz, postSql, args);
        }

        public int update(T t) {
            return dbHelper.update(t);
        }

        public int insertBatchWithoutReturnId(Collection<T> list) {
            return dbHelper.insertBatchWithoutReturnId(list);
        }

        public int executeRaw(String sql, Object... args) {
            return dbHelper.executeRaw(sql, args);
        }

        public int executeRaw(String sql, Map<String, ?> paramMap) {
            return dbHelper.executeRaw(sql, paramMap);
        }


        public long getCount(String postSql, Object... args) {
            return dbHelper.getCount(clz, postSql, args);
        }

        public long getCount() {
            return dbHelper.getCount(clz);
        }

    }

//***************************************** cloud_demand *************************************/

    /**
     * 获取原生的  JdbcTemplate
     *
     * @param dbHelper DBHelper
     * @return JdbcTemplate
     */
    public static JdbcTemplate getJdbcTemplate(DBHelper dbHelper) {
        return ((SpringJdbcDBHelper) dbHelper).getJdbcTemplate();
    }


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#cdLabDbHelper
     */
    public static DBHelper cdLabDbHelper = SpringUtil.getBean("cdLabDbHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#demandDBHelper
     */
    public static DBHelper demandDBHelper = SpringUtil.getBean("demandDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckForecastStdCrpDBHelper
     */
    public static DBHelper ckForecastStdCrpDBHelper = SpringUtil.getBean("ckForecastStdCrpDBHelper", DBHelper.class);


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckForecastStdCrpSwapDBHelper
     */
    public static DBHelper ckForecastStdCrpSwapDBHelper = SpringUtil.getBean("ckForecastStdCrpSwapDBHelper",
            DBHelper.class);


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#resplanDBHelper(JdbcTemplate)
     */
    public static DBHelper resplanDBHelper = SpringUtil.getBean("resplanDBHelper", DBHelper.class);


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckcubesDBHelper
     */
    public static DBHelper ckCubesDBHelper = SpringUtil.getBean("ckcubesDBHelper", DBHelper.class);


    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#ckstdcrpDBHelper
     */
    public static DBHelper ckstdcrpDBHelper = SpringUtil.getBean("ckstdcrpDBHelper", DBHelper.class);
    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#ckcldStdCrpSwapDBHelper(JdbcTemplate)
     */
    public static DBHelper ckcldStdCrpSwapDBHelper = SpringUtil.getBean("ckcldStdCrpSwapDBHelper", DBHelper.class);

    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#ckcldDBHelper(JdbcTemplate)
     */
    public static DBHelper ckcldDBHelper = SpringUtil.getBean("ckcldDBHelper", DBHelper.class);

    /**
     * 这里做一层引用
     *
     * @see DatabaseConfiguration#rrpDBHelper
     */
    public static DBHelper rrpDBHelper = SpringUtil.getBean("rrpDBHelper", DBHelper.class);

    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#ckcldStdCrpDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper ckcldStdCrpDBHelper = SpringUtil.getBean("ckcldStdCrpDBHelper", DBHelper.class);

    /**
     * 新的数据源引用
     *
     * @see DatabaseConfiguration#prodReadOnlyCkStdCrpDBHelper(org.springframework.jdbc.core.JdbcTemplate)
     */
    public static DBHelper prodReadOnlyCkStdCrpDBHelper = SpringUtil.getBean("prodReadOnlyCkStdCrpDBHelper",
            DBHelper.class);


}
