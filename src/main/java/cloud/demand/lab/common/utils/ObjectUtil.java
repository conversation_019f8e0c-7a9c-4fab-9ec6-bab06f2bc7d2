package cloud.demand.lab.common.utils;

import java.lang.reflect.Field;
import java.util.List;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 */
public class ObjectUtil {

    /**
     * 把空的字符串设置为 null， 使用数据库的默认值
     * @param object obj
     */
    public static void setEmptyStringFieldsToNull(Object object) {

        if (object == null) {
            return;
        }

        if (object instanceof List) {
            List<?> list = (List<?>) object;
            for (Object listItem : list) {
                setEmptyStringFieldsToNull(listItem);
            }
        } else {
            Class<?> clazz = object.getClass();
            setEmptyStringFieldsToNullForClass(object, clazz);
        }
    }

    private static void setEmptyStringFieldsToNullForClass(Object object, Class<?> clazz) {
        if (clazz == null || clazz.equals(Object.class)) {
            return;
        }

        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            if (field.getType().equals(String.class)) {
                try {
                    String fieldValue = (String) field.get(object);
                    if (StringUtils.isEmpty(fieldValue)) {
                        field.set(object, null);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }

        // 递归处理父类的字段
        setEmptyStringFieldsToNullForClass(object, clazz.getSuperclass());
    }

}
