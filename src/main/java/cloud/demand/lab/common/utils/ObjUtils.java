package cloud.demand.lab.common.utils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ObjUtils {

    /**
     * null    -> 1
     * 0      -> 1
     */
    public static boolean isEmpty(Collection<?> a) {
        return a == null || a.isEmpty();
    }

    public static boolean isNotEmpty(Collection<?> a) {
        return !isEmpty(a);
    }


    public static boolean allFieldIsNull(Object o) {
        try {
            for (Field field : o.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object object = field.get(o);
                if (!Objects.isNull(object)) {
                    return false;
                }
            }
        } catch (Exception e) {
            return true;
        }
        return true;
    }

}
