package cloud.demand.lab.common.utils;

import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.*;
import java.util.stream.*;

/**
 * EnhancedStream
 * <p>
 * 增加如下几个方法：
 * <p>
 * 1、acceptList 返回
 * 2、of 支持空
 * 3、groupThenApply
 *
 * @param <T>
 */
public class EStream<T> implements Stream<T> {

    private final Stream<T> stream;

    private EStream(Stream<T> stream) {
        this.stream = stream;
    }

    public void consumeAsList(Consumer<List<T>> consumer) {
        consumer.accept(stream.collect(Collectors.toList()));
    }

    public static <T> EStream<T> of(List<T> list) {
        return new EStream<>(list == null ? Stream.empty() : list.stream());
    }

    public static <T> EStream<T> of(Collection<T> list) {
        return new EStream<>(list == null ? Stream.empty() : list.stream());
    }

    public List<T> toList() {
        return stream.collect(Collectors.toList());
    }

    public Set<T> toSet() {
        return stream.collect(Collectors.toSet());
    }

    public BigDecimal sum(Function<T, BigDecimal> mapper) {
        return stream.map(mapper)
                .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b));
    }

    public <K, V> Map<K, V> toMap(Function<T, K> keyMapper, Function<T, V> valueMapper) {
        return stream.collect(Collectors.toMap(keyMapper, valueMapper));
    }

    public <K, V> Map<K, V> toMap(Function<T, K> keyMapper, Function<T, V> valueMapper,
            BinaryOperator<V> mergeFunction) {
        return stream.collect(Collectors.toMap(keyMapper, valueMapper, mergeFunction));

    }

    public <K> EStream<Map.Entry<K, List<T>>> groupBy(Function<T, K> keyFunction) {
        return new EStream<>(stream.collect(Collectors.groupingBy(keyFunction)).entrySet().stream());
    }

    public <K> void groupAndConsume(Function<T, K> keyFunction, BiConsumer<K, List<T>> valueFunction) {
        stream.collect(Collectors.groupingBy(keyFunction)).forEach(valueFunction);
    }


    @Override
    public EStream<T> filter(Predicate<? super T> predicate) {
        return new EStream<>(stream.filter(predicate));
    }

    @Override
    public <R> EStream<R> map(Function<? super T, ? extends R> mapper) {
        return new EStream<>(stream.map(mapper));
    }

    @Override
    public IntStream mapToInt(ToIntFunction<? super T> mapper) {
        return stream.mapToInt(mapper);
    }

    @Override
    public LongStream mapToLong(ToLongFunction<? super T> mapper) {
        return stream.mapToLong(mapper);
    }

    @Override
    public DoubleStream mapToDouble(ToDoubleFunction<? super T> mapper) {
        return stream.mapToDouble(mapper);
    }

    @Override
    public <R> EStream<R> flatMap(Function<? super T, ? extends Stream<? extends R>> mapper) {
        return new EStream<>(stream.flatMap(mapper));
    }

    @Override
    public IntStream flatMapToInt(Function<? super T, ? extends IntStream> mapper) {
        return stream.flatMapToInt(mapper);
    }

    @Override
    public LongStream flatMapToLong(Function<? super T, ? extends LongStream> mapper) {
        return stream.flatMapToLong(mapper);
    }

    @Override
    public DoubleStream flatMapToDouble(Function<? super T, ? extends DoubleStream> mapper) {
        return stream.flatMapToDouble(mapper);
    }

    @Override
    public EStream<T> distinct() {
        return new EStream<>(stream.distinct());
    }

    @Override
    public EStream<T> sorted() {
        return new EStream<>(stream.sorted());
    }

    @Override
    public EStream<T> sorted(Comparator<? super T> comparator) {
        return new EStream<>(stream.sorted(comparator));
    }

    @Override
    public EStream<T> peek(Consumer<? super T> action) {
        return new EStream<>(stream.peek(action));
    }

    @Override
    public EStream<T> limit(long maxSize) {
        return new EStream<>(stream.limit(maxSize));
    }

    @Override
    public EStream<T> skip(long n) {
        return new EStream<>(stream.skip(n));
    }

    @Override
    public void forEach(Consumer<? super T> action) {
        stream.forEach(action);
    }

    @Override
    public void forEachOrdered(Consumer<? super T> action) {
        stream.forEachOrdered(action);
    }

    @Override
    public Object[] toArray() {
        return stream.toArray();
    }

    @Override
    public <A> A[] toArray(IntFunction<A[]> generator) {
        return stream.toArray(generator);
    }

    @Override
    public T reduce(T identity, BinaryOperator<T> accumulator) {
        return stream.reduce(identity, accumulator);
    }

    @Override
    public Optional<T> reduce(BinaryOperator<T> accumulator) {
        return stream.reduce(accumulator);
    }

    @Override
    public <U> U reduce(U identity, BiFunction<U, ? super T, U> accumulator, BinaryOperator<U> combiner) {
        return stream.reduce(identity, accumulator, combiner);
    }

    @Override
    public <R> R collect(Supplier<R> supplier, BiConsumer<R, ? super T> accumulator, BiConsumer<R, R> combiner) {
        return stream.collect(supplier, accumulator, combiner);
    }

    @Override
    public <R, A> R collect(Collector<? super T, A, R> collector) {
        return stream.collect(collector);
    }

    @Override
    public Optional<T> min(Comparator<? super T> comparator) {
        return stream.min(comparator);
    }

    @Override
    public Optional<T> max(Comparator<? super T> comparator) {
        return stream.max(comparator);
    }

    @Override
    public long count() {
        return stream.count();
    }

    @Override
    public boolean anyMatch(Predicate<? super T> predicate) {
        return stream.anyMatch(predicate);
    }

    @Override
    public boolean allMatch(Predicate<? super T> predicate) {
        return stream.allMatch(predicate);
    }

    @Override
    public boolean noneMatch(Predicate<? super T> predicate) {
        return stream.noneMatch(predicate);
    }

    @Override
    public Optional<T> findFirst() {
        return stream.findFirst();
    }

    @Override
    public Optional<T> findAny() {
        return stream.findAny();
    }

    @Override
    public Iterator<T> iterator() {
        return stream.iterator();
    }

    @Override
    public Spliterator<T> spliterator() {
        return stream.spliterator();
    }

    @Override
    public boolean isParallel() {
        return stream.isParallel();
    }

    @Override
    public EStream<T> sequential() {
        return new EStream<>(stream.sequential());
    }

    @Override
    public EStream<T> parallel() {
        return new EStream<>(stream.parallel());
    }

    @Override
    public EStream<T> unordered() {
        return new EStream<>(stream.unordered());
    }

    @Override
    public EStream<T> onClose(Runnable closeHandler) {
        return new EStream<>(stream.onClose(closeHandler));
    }

    @Override
    public void close() {
        stream.close();
    }
}