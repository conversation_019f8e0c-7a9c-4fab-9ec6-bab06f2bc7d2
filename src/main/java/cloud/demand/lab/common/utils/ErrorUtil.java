package cloud.demand.lab.common.utils;

import yunti.boot.exception.BizException;

public class ErrorUtil {

    public static void throwIfNull(Object prime, String format, Object... args) {
        throwIfNull(prime, String.format(format, args));
    }

    public static void throwIfNull(Object prime, String info) {
        if (prime == null) {
            throw BizException.makeThrow(info);
        }
    }

    public static void throwIfFalse(boolean prime, String format, Object... args) {
        throwIfNull(prime, String.format(format, args));
    }

    public static void throwIfFalse(boolean prime, String info) {
        if (!prime) {
            throw BizException.makeThrow(info);
        }
    }

}
