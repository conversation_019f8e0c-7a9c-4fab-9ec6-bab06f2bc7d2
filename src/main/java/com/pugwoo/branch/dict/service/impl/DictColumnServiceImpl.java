package com.pugwoo.branch.dict.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.dict.entity.DictColumnDO;
import com.pugwoo.branch.dict.service.DictColumnService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DictColumnServiceImpl implements DictColumnService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public DictColumnDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DictColumnDO.class, id);
    }

    @Override
    public List<DictColumnDO> getAll(Long dictId, String name) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.andIf(StringTools.isNotBlank(name), "name like ?", "%" + name + "%");
        whereSQL.and("dict_id=?", dictId);
        return dbHelper.getAll(DictColumnDO.class, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DictColumnDO dictColumnDO) {
        if(dictColumnDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(dictColumnDO);
        return rows > 0 ? ResultBean.ok(dictColumnDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DictColumnDO dictColumnDO = new DictColumnDO();
        dictColumnDO.setId(id);
        return dbHelper.delete(dictColumnDO) > 0;
    }

}