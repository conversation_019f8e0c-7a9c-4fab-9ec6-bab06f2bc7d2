package com.pugwoo.branch.dict.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 表的列关系
 */
@Data
@ToString
@Table("dict_column_relation")
public class DictColumnRelationDO extends AdminCoreDO {

    /** 关联的名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 关联的描述<br/>Column: [description] */
    @Column(value = "description")
    private String description;

    /** 左关联的列，注意如果是多列关联的，请创建虚拟列，将多列先合成一列<br/>Column: [left_column_id] */
    @Column(value = "left_column_id")
    private Long leftColumnId;

    /** 右关联的列，注意如果是多列关联的，请创建虚拟列，将多列先合成一列<br/>Column: [right_column_id] */
    @Column(value = "right_column_id")
    private Long rightColumnId;

    /** 关联mvel脚本，等值关联就不用写了，这个适用于更复杂的情况，左值用left，右值用right，返回true表示匹配，false不匹配<br/>Column: [relation_script] */
    @Column(value = "relation_script")
    private String relationScript;

}