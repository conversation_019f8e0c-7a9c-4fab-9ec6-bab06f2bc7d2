package com.pugwoo.branch.dict.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 字典表
 */
@Data
@ToString
@Table("dict")
public class DictDO extends AdminCoreDO {

    /** 字典名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 字典表的命名空间，支持中文，这个是为了区分具有相同名称字段的字典，它和name还是有区别，同一个namespace下不应该有相同的字段<br/>Column: [namespace] */
    @Column(value = "namespace")
    private String namespace;

    /** 字典描述信息<br/>Column: [description] */
    @Column(value = "description")
    private String description;

    /** 关联的数据库id，可选<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 数据库名，可选<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 字典对应的表名，可选<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 字典表额外的where条件<br/>Column: [extra_where] */
    @Column(value = "extra_where")
    private String extraWhere;

    /** 参考的链接<br/>Column: [ref_url] */
    @Column(value = "ref_url")
    private String refUrl;

}