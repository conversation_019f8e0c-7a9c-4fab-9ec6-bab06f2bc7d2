package com.pugwoo.branch.code_scan.model;

import com.pugwoo.branch.code_scan.entity.GitCodeCheckRuleDO;
import com.pugwoo.branch.code_scan.entity.GitCodeCheckRuleDetailDO;
import com.pugwoo.branch.code_scan.entity.GitCodeCheckRuleFileDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class GitCodeCheckRuleBO extends GitCodeCheckRuleDO {

    @RelatedColumn(localColumn = "id",remoteColumn = "rule_id")
    private List<GitCodeCheckRuleDetailDO> gitCodeCheckRuleDetailDOList;

    @RelatedColumn(localColumn = "id", remoteColumn = "rule_id")
    private List<GitCodeCheckRuleFileDO> checkFiles;

}