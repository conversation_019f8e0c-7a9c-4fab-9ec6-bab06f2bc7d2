package com.pugwoo.branch.common.utils;

import org.springframework.scheduling.support.CronExpression;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class CronUtils {

    public static long getNextTimestampNearestNow(String cronExpression, LocalDateTime lastTime) {
        CronExpression cron = CronExpression.parse(cronExpression);
        LocalDateTime next = cron.next(lastTime);
        LocalDateTime lastOne = next;
        while(next != null && next.isBefore(LocalDateTime.now())) {
            lastOne = next;
            next = cron.next(next);
        }
        if (lastOne == null) {
            return 0;
        }
        Instant instant = lastOne.atZone(ZoneId.systemDefault()).toInstant();
        return instant.toEpochMilli();
    }

}
