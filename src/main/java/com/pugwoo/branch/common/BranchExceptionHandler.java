package com.pugwoo.branch.common;

import com.pugwoo.admin.bean.WebJsonBean;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class BranchExceptionHandler {

    @ExceptionHandler(GitAPIException.class)
    public WebJsonBean gitApiException(GitAPIException ex) {
        String msg = ex.getMessage();
        if(msg.contains("authentication not supported")) {
            return WebJsonBean.fail("Git账号密码错误");
        } else {
            log.error("GitAPIException", ex);
            return WebJsonBean.fail(msg);
        }
    }

}
