package com.pugwoo.branch.common;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;
import java.util.Map;

public class VelocityTools {

    /**
     * 渲染vm模版。说明：模版不一定要以vm结尾，只要在classpath中存在即可。
     */
    public static String renderVM(String templatePath, Map<String, Object> params) {
        VelocityEngine ve = new VelocityEngine();
        ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        ve.init();
        Template t = ve.getTemplate(templatePath, "utf-8");

        VelocityContext context = new VelocityContext();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                context.put(entry.getKey(), entry.getValue());
            }
        }

        StringWriter writer = new StringWriter();
        t.merge(context, writer);

        return writer.toString();
    }

}
