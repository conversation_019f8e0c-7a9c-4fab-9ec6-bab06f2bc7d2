package com.pugwoo.branch.common;

import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

/**
 * 负责将编辑相关页面转发到后端的服务
 */
@RestController
public class EditormdController {

    @Value("${branch.editorHost}")
    private String editorHost;

    @RequestMapping(value = "/editormd/**", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<?> proxyEditorMD(HttpServletRequest request) throws IOException {
        String queryString = request.getQueryString();
        String urlWithParams = request.getRequestURI() + (queryString == null ? "" : "?" + queryString);
        String proxyUrl = editorHost + urlWithParams;

        Browser browser = new Browser();

        // 复制request的http的头部
        Enumeration<String> headerNames = request.getHeaderNames();
        while(headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String value = request.getHeader(headerName);
            if (headerName.equals("content-type")) { // 特别处理下，等woo-utils的browser不区分大小写后再放开
                headerName = "Content-Type";
            }
            browser.addRequestHeader(headerName, value);
        }

        HttpResponse httpResponse = null;
        if ("GET".equals(request.getMethod())) {
            httpResponse = browser.get(proxyUrl);
        } else if ("POST".equals(request.getMethod())) {
            httpResponse = browser.post(proxyUrl, request.getInputStream()); // post流不放在内存中，节省内存资源
        } else {
            throw new RuntimeException("not supported http method:" + request.getMethod());
        }

        MultiValueMap<String, String> headers = new HttpHeaders();
        for (Map.Entry<String, List<String>> e : httpResponse.getHeaders().entrySet()) {
            if (e.getKey() != null) {
                headers.addAll(e.getKey(), e.getValue());
            }
        }
        return new ResponseEntity<>(httpResponse.getContentBytes(),
                headers, httpResponse.getResponseCode());
    }

}
