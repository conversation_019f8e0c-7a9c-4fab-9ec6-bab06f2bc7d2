package com.pugwoo.branch.net_check.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.service.NetCheckResultService;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = "/net_check_result")
public class NetCheckResultController {

    @Autowired
    private NetCheckResultService netCheckResultService;
    
    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize, Long configId) {
        PageData<NetCheckResultDO> pageData = netCheckResultService.getPage(page, pageSize, configId);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(NetCheckResultDO netCheckResultDO) {
        WebCheckUtils.assertNotNull(netCheckResultDO, "缺少修改的对象参数");
        // TODO check parameters

        ResultBean<Long> result = netCheckResultService.insertOrUpdate(netCheckResultDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(netCheckResultService.deleteById(id));
    }

}
