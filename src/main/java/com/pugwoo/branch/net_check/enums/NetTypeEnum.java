package com.pugwoo.branch.net_check.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 检查网络类型
 */
@Getter
public enum NetTypeEnum {

    HTTP("HTTP", "HTTP");

    final private String code;
    final private String name;

    NetTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static NetTypeEnum getByCode(String code) {
        for (NetTypeEnum e : NetTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        NetTypeEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}