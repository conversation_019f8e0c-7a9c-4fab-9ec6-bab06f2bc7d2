package com.pugwoo.branch.net_check.task;

import com.pugwoo.branch.common.utils.CronUtils;
import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.service.NetCheckConfigService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class NetCheckTask {

    @Autowired
    private NetCheckConfigService netCheckConfigService;

    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(fixedDelay = 1000 * 3)
    public void check() {
        // 执行的任务列表
        List<NetCheckConfigDO> taskList = getCheckConfigTaskList();

        // 执行
        for (NetCheckConfigDO netCheckConfigDO : taskList) {
            try {
                NetCheckResultDO resultDO = netCheckConfigService.doCheck(netCheckConfigDO);

                // 发送告警消息 TODO

                netCheckConfigService.updateCheckResult(resultDO, netCheckConfigDO);
            } catch (Throwable e) { // 加异常是为了让后面的config可以正常执行，不受前面的影响
                log.error("net检查configId:{} 失败", netCheckConfigDO.getId(), e);
            }
        }
    }

    /**
     * 获取执行的任务列表
     *    启用的 满足执行频率的
     * @return 检查sql配置
     */
    private List<NetCheckConfigDO> getCheckConfigTaskList() {
        long taskStartMs = System.currentTimeMillis();

        // 获取任务
        List<NetCheckConfigDO> allEnabledConfigList = netCheckConfigService.getAllEnabledList();
        if (allEnabledConfigList == null) {
            return new ArrayList<>();
        }

        // 过滤出可以执行的任务
        return allEnabledConfigList.stream()
                .filter(config -> {
                    Date lastTime = config.getLastTime();
                    Date lastTime2 = config.getUpdateTime() != null ? config.getUpdateTime() : config.getCreateTime();
                    lastTime = max(lastTime, lastTime2);
                    if (lastTime == null) {
                        return true; // 兜底，先run起来
                    }
                    long lastTimeMs = lastTime.getTime();
                    String cronExpression = config.getCronExpression();
                    if (StringTools.isNotBlank(cronExpression)) {
                        long timestamp = CronUtils.getNextTimestampNearestNow(cronExpression, DateUtils.toLocalDateTime(lastTime));
                        return taskStartMs >= timestamp;
                    }

                    Integer rateSeconds = config.getRateSeconds();
                    if (rateSeconds != null && rateSeconds > 0) {
                        return taskStartMs - lastTimeMs > rateSeconds * 1000;
                    }

                    return false; // 没配置不执行
                })
                .collect(Collectors.toList());
    }

    private static Date max(Date d1, Date d2) {
        if (d1 == null) {
            return d2;
        }
        if (d2 == null) {
            return d1;
        }
        return d1.getTime() > d2.getTime() ? d1 : d2;
    }
}
