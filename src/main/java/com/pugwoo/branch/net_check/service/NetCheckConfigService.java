package com.pugwoo.branch.net_check.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.vo.NetCheckConfigVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface NetCheckConfigService {

    /**
     * 通过主键获得数据
     */
    NetCheckConfigDO getById(Long id);

    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<NetCheckConfigVO> getPage(int page, int pageSize);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(NetCheckConfigDO netCheckConfigDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 获取所有启用的任务
     */
    List<NetCheckConfigDO> getAllEnabledList();

    /**
     * 执行检查并返回结果
     * 不包括：1）发送消息告警 2）将resultDO数据插入数据库
     */
    NetCheckResultDO doCheck(NetCheckConfigDO netCheckConfigDO);

    /**
     * 更新检查结果
     */
    void updateCheckResult(NetCheckResultDO netCheckResultDO, NetCheckConfigDO netCheckConfigDO);

}
