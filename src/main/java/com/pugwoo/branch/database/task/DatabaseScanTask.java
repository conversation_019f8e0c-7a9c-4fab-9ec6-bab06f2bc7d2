package com.pugwoo.branch.database.task;

import com.pugwoo.branch.database.service.DatabaseScanService;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DatabaseScanTask {

    @Autowired
    private DatabaseScanService databaseScanService;

    /**
     * 执行sql扫描检查操作
     */
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(fixedDelay = 1000 * 3)
    public void databaseScan() {
        databaseScanService.scan();
    }

}
