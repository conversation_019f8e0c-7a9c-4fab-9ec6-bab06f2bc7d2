package com.pugwoo.branch.database.task;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.branch.common.utils.CronUtils;
import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseCheckResultDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.service.DatabaseCheckConfigService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * 2022/09/04
 */
@Component
@Slf4j
public class DatabaseCheckTask {
    
    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private DatabaseCheckConfigService databaseCheckConfigService;
    @Autowired
    private AdminNotifyService notifyService;

    /**
     * 执行sql检查操作。
     * <br>
     * 数据库检查自动开启告警恢复机制，第一次发现时，告警；第二次仍错误，进入告警恢复机制：
     * 即再发送一条告警，之后等待直到成功才发送一条告警恢复通知。
     */
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(fixedDelay = 1000 * 3)
    public void databaseCheck() {
        // 执行的任务列表
        List<DatabaseCheckConfigDO> taskList = getCheckConfigTaskList();
        
        // 获取连接信息
        Map<Long, DatabaseDO> databaseId2DO = getId2Database(taskList);
        
        // 执行sql检查
        for (DatabaseCheckConfigDO databaseCheckConfigDO : taskList) {
            try {
                Long databaseId = databaseCheckConfigDO.getDatabaseId();
                DatabaseDO databaseDO = databaseId2DO.get(databaseId);

                DatabaseCheckResultDO databaseCheckResultDO = databaseCheckConfigService.executeAndAssertSql(databaseDO,
                        databaseCheckConfigDO.getDatabaseName(), databaseCheckConfigDO.getSql(),
                        databaseCheckConfigDO.getAssertion(), databaseCheckConfigDO.getDetailSql());
                databaseCheckResultDO.setDatabaseCheckConfigId(databaseCheckConfigDO.getId());

                Integer alarmErrorValve = databaseCheckConfigDO.getAlarmErrorValve();
                if (alarmErrorValve == null) {
                    alarmErrorValve = 1;
                }

                // 配置了邮件则进行通知（这里有告警恢复通知机制）
                boolean isRecover = false; // 用于标识该次成功是否已经用于发送恢复消息，如果是，那么该次成功一定要写入数据，不受成功间隔的控制
                if (StringTools.isNotBlank(databaseCheckConfigDO.getSendEmail())) {
                    List<DatabaseCheckResultDO> items = databaseCheckConfigService.getLatestCheckItems(databaseCheckConfigDO.getId(), alarmErrorValve * 2 + 1);
                    if (databaseCheckResultDO.getIsSuccess()) { // 本次检测成功
                        // 当items最后一个是失败且有发送过告警消息时，发送恢复通知
                        if (!items.isEmpty() && items.getFirst().getIsSuccess() != null && !items.getFirst().getIsSuccess()
                                && items.getFirst().getIsSendNotify() != null && items.getFirst().getIsSendNotify()) {
                            isRecover = notifyService.sendEmail(databaseCheckConfigDO.getSendEmail(),
                                    DateUtils.format(new Date()) + "检查SQL【已恢复】,检查名称:" + databaseCheckConfigDO.getName(),
                                    "数据库实例:" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort() + "," +
                                            "断言:" + databaseCheckResultDO.getAssertion() + ",实际:" + databaseCheckResultDO.getSqlRowsJson());
                            // 发送恢复通知后，设置已发送告警标识
                            if (isRecover) {
                                databaseCheckResultDO.setIsSendNotify(true);
                            }
                        }
                    } else { // 本次检测失败
                        items.addFirst(databaseCheckResultDO); // 需要把本次失败也加进去参与判断
                        // 当items连续alarmErrorValve次失败时
                        if (isContinueFailExactly(items, alarmErrorValve)) {
                            boolean sendSuccess = notifyService.sendEmail(databaseCheckConfigDO.getSendEmail(),
                                    DateUtils.format(new Date()) + "检查SQL【失败" + alarmErrorValve +"次】,检查名称:" + databaseCheckConfigDO.getName(),
                                    "数据库实例:" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort() + "," +
                                            "断言:" + databaseCheckResultDO.getAssertion() + ",实际:" + databaseCheckResultDO.getSqlRowsJson());
                            // 发送第一次告警后，设置已发送告警标识
                            if (sendSuccess) {
                                databaseCheckResultDO.setIsSendNotify(true);
                            }

                        } else if (isContinueFailExactly(items, alarmErrorValve * 2)) { // 告警2次
                            boolean sendSuccess = notifyService.sendEmail(databaseCheckConfigDO.getSendEmail(),
                                    DateUtils.format(new Date()) + "检查SQL【第二次告警，失败共" + (alarmErrorValve * 2) + "次】,检查名称:" + databaseCheckConfigDO.getName(),
                                    "数据库实例:" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort() + "," +
                                            "断言:" + databaseCheckResultDO.getAssertion() + ",实际:" + databaseCheckResultDO.getSqlRowsJson()
                                            + " 【后续在检测成功之前，将不会再发送失败告警，实际上失败仍在继续，请留意检测恢复消息】");
                            // 发送第二次告警后，设置已发送告警标识
                            if (sendSuccess) {
                                databaseCheckResultDO.setIsSendNotify(true);
                            }

                        } else { // 等待恢复，不再告警
                            log.info("{}检查SQL【连续失败】,检查名称:{} 不再告警，等待恢复", DateUtils.format(new Date()), databaseCheckConfigDO.getName());
                        }
                    }
                }

                if (isRecover) {
                    databaseCheckConfigDO.setSuccLogRateSecs(null); // 故意不受成功间隔控制
                }
                databaseCheckConfigService.updateCheckResult(databaseCheckResultDO, databaseCheckConfigDO);

            } catch (Throwable e) { // 加异常是为了让后面的config可以正常执行，不受前面的影响
                log.error("检查configId:{} 失败", databaseCheckConfigDO.getId(), e);
            }
        }
    }

    private boolean isContinueFailExactly(List<DatabaseCheckResultDO> items, int alarmErrorValve) {
        if (items.size() < alarmErrorValve) {
            return false;
        }

        for (int i = 0; i < alarmErrorValve; i++) {
            if (items.get(i).getIsSuccess() != null && items.get(i).getIsSuccess()) {
                return false;
            }
        }
        // 如果items个数刚好是alarmErrorValve，那么认为是满足条件
        if (items.size() == alarmErrorValve) {
            return true;
        } else { // 否则要求其下一个是成功
            return items.get(alarmErrorValve) != null && items.get(alarmErrorValve).getIsSuccess();
        }
    }
    
    /**
     * 获取执行的任务列表
     *    启用的 满足执行频率的
     * @return 检查sql配置
     */
    private List<DatabaseCheckConfigDO> getCheckConfigTaskList() {
        long taskStartMs = System.currentTimeMillis();
        
        // 获取任务
        List<DatabaseCheckConfigDO> allEnabledConfigList = databaseCheckConfigService.getAllEnabledList();
        if (allEnabledConfigList == null) {
            return new ArrayList<>();
        }
    
        // 过滤出可以执行的任务
        return allEnabledConfigList.stream()
                .filter(config -> {
                    Date lastTime = config.getLastTime();
                    Date lastTime2 = config.getUpdateTime() != null ? config.getUpdateTime() : config.getCreateTime();
                    lastTime = max(lastTime, lastTime2);
                    if (lastTime == null) {
                        return true; // 兜底，先run起来
                    }
                    long lastTimeMs = lastTime.getTime();
                    String cronExpression = config.getCronExpression();
                    if (StringTools.isNotBlank(cronExpression)) {
                        long timestamp = CronUtils.getNextTimestampNearestNow(cronExpression, DateUtils.toLocalDateTime(lastTime));
                        return taskStartMs >= timestamp;
                    }

                    Integer rateSeconds = config.getRateSeconds();
                    if (rateSeconds != null && rateSeconds > 0) {
                        return taskStartMs - lastTimeMs > rateSeconds * 1000;
                    }

                    return false; // 没配置不执行
                })
                .collect(Collectors.toList());
    }

    private static Date max(Date d1, Date d2) {
        if (d1 == null) {
            return d2;
        }
        if (d2 == null) {
            return d1;
        }
        return d1.getTime() > d2.getTime() ? d1 : d2;
    }
    
    /**
     * 获取数据库连接信息
     * @param databaseCheckConfigDOList 执行的任务列表
     * @return databaseId -> databaseDO
     */
    private Map<Long, DatabaseDO> getId2Database(List<DatabaseCheckConfigDO> databaseCheckConfigDOList) {
        // 获取连接信息
        Set<Long> databaseIdSet = databaseCheckConfigDOList.stream()
                .map(DatabaseCheckConfigDO::getDatabaseId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<DatabaseDO> databaseDOList = databaseService.getByIds(databaseIdSet);
        return databaseDOList.stream()
                .collect(toMap(AdminCoreDO::getId, item -> item));
    }
}
