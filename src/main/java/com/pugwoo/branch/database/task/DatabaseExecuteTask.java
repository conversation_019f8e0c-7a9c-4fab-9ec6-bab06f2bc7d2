package com.pugwoo.branch.database.task;

import com.pugwoo.branch.database.enums.Constants;
import com.pugwoo.branch.database.service.DatabaseExecuteConfigService;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisMsg;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
public class DatabaseExecuteTask {

    @Autowired
    RedisHelper redisHelper;
    @Resource
    private DatabaseExecuteConfigService databaseExecuteConfigService;

    private static final ThreadPoolExecutor threadPool = ThreadPoolUtils.createThreadPool(
            5, 10000, 10, "database-execute");

    @PostConstruct
    public void init() {
        threadPool.submit((Runnable) () -> {
            while(true) {
                RedisMsg message = null;
                try {
                    message = redisHelper.receive(Constants.DATABASE_EXECUTE_CONFIG_TASK_MQ);
                    if (message != null) {
                        Long taskId = JSON.parse(message.getMsg(), Long.class);
                        threadPool.submit(() -> databaseExecuteConfigService.execute(taskId));
                        redisHelper.ack(Constants.DATABASE_EXECUTE_CONFIG_TASK_MQ, message.getUuid());
                    }
                } catch (Throwable e) {
                    log.error("database execute task error", e);
                    if (message != null) {
                        redisHelper.nack(Constants.DATABASE_EXECUTE_CONFIG_TASK_MQ, message.getUuid());
                    }
                }
            }
        });
    }

}
