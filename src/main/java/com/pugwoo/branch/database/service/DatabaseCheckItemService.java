package com.pugwoo.branch.database.service;

import com.pugwoo.branch.database.entity.DatabaseCheckResultDO;
import com.pugwoo.dbhelper.model.PageData;

public interface DatabaseCheckItemService {

    /**
     * 获得分页数据 倒序
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<DatabaseCheckResultDO> getPage(int page, int pageSize, Boolean isSuccess, Long databaseCheckConfigId);

    /**
     * 删除失败记录
     */
    void deleteFailRecord(Long databaseCheckConfigId);

}
