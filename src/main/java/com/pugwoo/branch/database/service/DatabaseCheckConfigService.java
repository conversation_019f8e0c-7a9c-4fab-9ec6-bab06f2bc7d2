package com.pugwoo.branch.database.service;// package a.b.c;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseCheckResultDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.vo.DatabaseCheckConfigVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface DatabaseCheckConfigService {
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<DatabaseCheckConfigVO> getPage(int page, int pageSize);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(DatabaseCheckConfigDO databaseCheckConfigDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);
    
    /**
     * 获取所有启用的任务
     */
    List<DatabaseCheckConfigDO> getAllEnabledList();

    /**
     * 执行sql并断言
     */
    DatabaseCheckResultDO executeAndAssertSql(DatabaseDO databaseDO, String databaseName, String sql, String assertion,
                                              String detailSql);

    /**
     * 更新sql检查结果
     * @param databaseCheckResultDO 执行明细
     */
    void updateCheckResult(DatabaseCheckResultDO databaseCheckResultDO, DatabaseCheckConfigDO databaseCheckConfigDO);

    /**
     * 获取最近N次检查结果，时间逆序
     * @param checkConfigId 检查id
     * @param limit 获取的记录数量
     */
    List<DatabaseCheckResultDO> getLatestCheckItems(Long checkConfigId, int limit);

    /**
     * 重置错误计数
     */
    void resetFailCount(Long id);

    /**
     * 发送邮件，多个分号隔开
     */
    void sendEmail(String sendEmails);

}
