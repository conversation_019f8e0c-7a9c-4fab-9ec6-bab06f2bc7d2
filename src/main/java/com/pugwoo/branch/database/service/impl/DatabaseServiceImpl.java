package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.DatabaseStatusDTO;
import com.pugwoo.branch.database.service.DatabaseOperateService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.vo.DatabaseVO;
import com.pugwoo.branch.database.model.MonitorsYamlDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import com.pugwoo.wooutils.yaml.YAML;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 */
@Service
public class DatabaseServiceImpl implements DatabaseService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    private final ThreadPoolExecutor pool = ThreadPoolUtils.createThreadPool(10, 100, 10,
            "database-fill-info");

    private final Map<DatabaseTypeEnum, DatabaseOperateService> type2OperateService = new HashMap<>();

    @Autowired(required = false)
    public void setType2OperateService(List<DatabaseOperateService> databaseOperateServiceList) {
        if (databaseOperateServiceList == null) { return; }
        for (DatabaseOperateService operateService : databaseOperateServiceList) {
            DatabaseTypeEnum type = operateService.getSupportedDatabaseType();
            if (type != null) {
                type2OperateService.put(type, operateService);
            }
        }
    }

    @Override
    public DatabaseDO getById(Long databaseId) {
        DatabaseDO databaseDO = dbHelper.getByKey(DatabaseDO.class, databaseId);
        if (databaseDO == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "数据库实例不存在");
        }
        return databaseDO;
    }

    @Override
    public PageData<DatabaseVO> getPage(int page, int pageSize, String env, String type) {
        WhereSQL where = new WhereSQL();
        if (StringTools.isNotBlank(env)) {
            where.and("env=?", env);
        }
        if (StringTools.isNotBlank(type)) {
            where.and("type=?", type);
        }

        return dbHelper.getPage(DatabaseVO.class, page, pageSize, where.getSQL(), where.getParams());
    }

    @Override
    public DatabaseStatusDTO getDatabaseStatus(Long databaseId) {
        DatabaseDO databaseDO = getById(databaseId);

        DatabaseStatusDTO databaseStatusDTO = new DatabaseStatusDTO();

        String type = databaseDO.getType();
        DatabaseTypeEnum databaseType = DatabaseTypeEnum.getByCode(type);
        if (databaseType == null) {
            databaseStatusDTO.connectFail(-1, "数据库类型不支持操作");
            return databaseStatusDTO;
        }
        DatabaseOperateService operateService = type2OperateService.get(databaseType);
        if (operateService == null) {
            databaseStatusDTO.connectFail(-3, "数据库类型操作未实现");
            return databaseStatusDTO;
        }

        return operateService.ping(databaseDO, false);
    }

    @Override
    public void insertOrUpdate(DatabaseDO databaseDO) {
        dbHelper.insertOrUpdate(databaseDO);
        clearDataSource(databaseDO.getId());

        // 对于内置监控邮箱，如果改动了，需要更新监控细项的邮箱
        dbHelper.updateAll(DatabaseCheckConfigDO.class, "set send_email=?",
                "where database_id=? and is_build_in=1", databaseDO.getMonitorSendMail(),
                databaseDO.getId());
    }

    @Override
    public boolean deleteById(Long id) {
        if (id == null) { return false; }
        DatabaseDO databaseDO = new DatabaseDO();
        databaseDO.setId(id);
        dbHelper.delete(databaseDO);
        clearDataSource(id);
        return true;
    }

    private void clearDataSource(Long databaseId) {
        for (DatabaseOperateService d : type2OperateService.values()) {
            d.clearDatabaseConnection(databaseId);
            // 为了彻底，还是都清理，当前因为BaseJdbcOperateService中存放数据库连接并不是static，导致实际上没有清理到
        }
    }

    @Override
    public List<DatabaseDO> getByIds(Collection<Long> databaseIds) {
        if (databaseIds == null || databaseIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> databaseIdList = databaseIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (databaseIdList.isEmpty()) {
            return new ArrayList<>();
        }
        return dbHelper.getAll(DatabaseDO.class, "and id in (?)", databaseIdList);
    }

    @Override
    public List<DatabaseDO> getAll() {
        return dbHelper.getAll(DatabaseDO.class);
    }

    @Override
    public List<DatabaseDO> getCapacityMonitorDatabase() {
        return dbHelper.getAll(DatabaseDO.class, "where enable_capacity_monitor=1");
    }

    @Override
    public List<Map<String, Object>> executeQuery(DatabaseDO databaseDO, String databaseName, String sql, Object... args) {
        return getDatabaseOperateService(databaseDO).executeQuery(databaseDO, databaseName, sql, args);
    }

    @Override
    public int executeModify(DatabaseDO databaseDO, String databaseName, String sql, Object... args) {
        return getDatabaseOperateService(databaseDO).executeModify(databaseDO, databaseName, sql, args);
    }

    @Override
    public DatabaseOperateService getDatabaseOperateService(DatabaseDO databaseDO) {
        String type = databaseDO.getType();
        DatabaseTypeEnum databaseType = DatabaseTypeEnum.getByCode(type);
        if (databaseType == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "不支持的数据库");
        }
        DatabaseOperateService operateService = type2OperateService.get(databaseType);
        if (operateService == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "当前数据库类型不支持");
        }

        return operateService;
    }

    @SneakyThrows
    @Override
    @Transactional
    public boolean enableMonitor(Long databaseId, Boolean enabled) {
        DatabaseDO databaseDO = getById(databaseId);

        if (enabled) {
            MonitorsYamlDTO monitorsYamlDTO = null;
            DatabaseTypeEnum databaseType = DatabaseTypeEnum.getByCode(databaseDO.getType());
            if (databaseType == DatabaseTypeEnum.MYSQL) {
                String mysql = IOUtils.readClasspathResourceAsString("/database_buildin_monitor/mysql.yaml");
                monitorsYamlDTO = YAML.parse(mysql, MonitorsYamlDTO.class);
            } else if (databaseType == DatabaseTypeEnum.CLICKHOUSE) {
                String clickhouse = IOUtils.readClasspathResourceAsString("/database_buildin_monitor/clickhouse.yaml");
                monitorsYamlDTO = YAML.parse(clickhouse, MonitorsYamlDTO.class);
            }

            // 其他数据库类型待实现

            if (monitorsYamlDTO != null && monitorsYamlDTO.getMonitors() != null) {
                Map<String, MonitorsYamlDTO.MonitorDTO> monitorMap = ListUtils.toMap(monitorsYamlDTO.getMonitors(),
                        o -> o.getCode(), o -> o);

                // 1. 先删除不存在的build-in code
                dbHelper.delete(DatabaseCheckConfigDO.class,
                        "where database_id=? and is_build_in=1 and (build_in_code is null or build_in_code not in (?))",
                        databaseId, monitorMap.keySet());

                // 2. 再更新已经存在的build-in code
                List<DatabaseCheckConfigDO> existConfigs = dbHelper.getAll(DatabaseCheckConfigDO.class,
                        "where database_id=? and is_build_in=1", databaseId);
                for (DatabaseCheckConfigDO config : existConfigs) {
                    MonitorsYamlDTO.MonitorDTO monitorDTO = monitorMap.get(config.getBuildInCode());
                    if (monitorDTO != null) { // 正常都是非null的，因为前面已经删除了不存在的了
                        config.setName(monitorDTO.getName());
                        config.setSql(monitorDTO.getMonitorSql());
                        config.setRateSeconds(monitorDTO.getIntervalSecond());
                        config.setDetailSql(monitorDTO.getDetailSql());
                        config.setAssertion(monitorDTO.getAssertExpression());
                        config.setSuccLogRateSecs(monitorDTO.getSuccLogRateSecs());
                        config.setIsEnabled(true); // 自动打开
                        dbHelper.update(config);
                    }
                }

                // 3. 最后插入新增的build-in code
                for (Map.Entry<String, MonitorsYamlDTO.MonitorDTO> entry : monitorMap.entrySet()) {
                    String code = entry.getKey();
                    MonitorsYamlDTO.MonitorDTO monitorDTO = entry.getValue();
                    if (existConfigs.stream().noneMatch(o -> code.equals(o.getBuildInCode()))) {
                        DatabaseCheckConfigDO config = getDatabaseCheckConfigDO(databaseDO, monitorDTO);
                        dbHelper.insert(config);
                    }
                }
            }
        } else {
            // 禁用对应的内置监控
            dbHelper.updateAll(DatabaseCheckConfigDO.class, "set is_enabled=0",
                    "where database_id=? and is_build_in=1", databaseId);
        }

        databaseDO.setEnableMonitor(enabled);
        dbHelper.update(databaseDO);
        return true;
    }

    @Override
    public boolean enableCapacityMonitor(Long databaseId, Boolean enabled) {
        DatabaseDO databaseDO = getById(databaseId);
        databaseDO.setEnableCapacityMonitor(enabled);
        dbHelper.update(databaseDO);
        return true;
    }

    @Override
    public int getBuildInMonitorCount(Long databaseId) {
        return (int) dbHelper.getCount(DatabaseCheckConfigDO.class,
                "where is_build_in=1 and database_id=?", databaseId);
    }

    @Override
    public void resetConnectionPool() {
        for (DatabaseOperateService d : type2OperateService.values()) {
            d.resetAllDatabaseConnection();
            // 为了彻底，还是都清理，当前因为BaseJdbcOperateService中存放数据库连接并不是static，导致实际上没有清理到
        }
    }

    private static DatabaseCheckConfigDO getDatabaseCheckConfigDO(DatabaseDO databaseDO, MonitorsYamlDTO.MonitorDTO monitorDTO) {
        DatabaseCheckConfigDO config = new DatabaseCheckConfigDO();
        config.setName(monitorDTO.getName());
        config.setDatabaseId(databaseDO.getId());
        config.setDatabaseName("");
        config.setIsBuildIn(true);
        config.setBuildInCode(monitorDTO.getCode());
        config.setCategory("内置监控");
        config.setSql(monitorDTO.getMonitorSql());
        config.setRateSeconds(monitorDTO.getIntervalSecond());
        config.setDetailSql(monitorDTO.getDetailSql());
        config.setAssertion(monitorDTO.getAssertExpression());
        config.setSuccLogRateSecs(monitorDTO.getSuccLogRateSecs());
        config.setIsEnabled(true); // 自动打开
        config.setSendEmail(databaseDO.getMonitorSendMail());
        return config;
    }
}
