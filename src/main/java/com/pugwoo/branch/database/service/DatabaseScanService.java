package com.pugwoo.branch.database.service;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.entity.DatabaseScanResultDO;
import com.pugwoo.branch.database.vo.DatabaseScanConfigVO;

import java.util.List;

/**
 * 负责数据库扫描服务
 */
public interface DatabaseScanService {

    /**
     * 扫描数据库
     */
    void scan();

    /**
     * 尝试扫描分组，不会写入到数据库
     */
    List<DatabaseScanResultDO> tryScanGroup(DatabaseScanConfigVO configVO, List<DatabaseScanConfigDetailDO> details);

}
