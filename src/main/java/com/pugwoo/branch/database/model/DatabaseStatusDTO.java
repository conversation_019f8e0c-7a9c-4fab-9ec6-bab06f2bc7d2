package com.pugwoo.branch.database.model;

import lombok.Data;

/**
 * 数据库状态信息
 */
@Data
public class DatabaseStatusDTO {

    /** 是否连接成功 */
    private boolean connectSuccess;
    /** 数据库连接状态 */
    private int statusCode;
    /** 状态信息 */
    private String statusMsg;

    public void connectSuccess(int ms) {
        this.connectSuccess = true;
        this.statusCode = 0;
        this.statusMsg = "成功(" + ms + "ms)";
    }

    public void connectFail(int statusCode, String statusMsg) {
        this.connectSuccess = false;
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }

}
