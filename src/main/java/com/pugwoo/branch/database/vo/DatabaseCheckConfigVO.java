package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

/**
 * 检查sql配置表
 */
@Data
public class DatabaseCheckConfigVO extends DatabaseCheckConfigDO {

    @RelatedColumn(localColumn = "database_id", remoteColumn = "id")
    private DatabaseDO databaseDO;

    /**vm用到*/
    public String getLastTimeStr() {
        return DateUtils.getIntervalToNow(super.getLastTime());
    }

    /**vm用到*/
    public String getLastErrorTimeStr() {
        return DateUtils.getIntervalToNow(super.getLastErrorTime());
    }

    /**vm用到*/
    public String getDatabase() {
        return databaseDO == null ? "" : databaseDO.getName();
    }

}
