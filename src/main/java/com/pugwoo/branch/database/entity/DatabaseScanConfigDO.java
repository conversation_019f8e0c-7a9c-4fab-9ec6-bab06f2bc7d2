package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("database_scan_config")
public class DatabaseScanConfigDO extends AdminCoreDO {

    /** 扫描任务的名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 是否启用<br/>Column: [enabled] */
    @Column(value = "enabled", insertValueScript = "true")
    private Boolean enabled;

    /** 扫描间隔秒数<br/>Column: [scan_interval_second] */
    @Column(value = "scan_interval_second")
    private Integer scanIntervalSecond;

    /** 数据库连接id<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 扫描的数据库名称<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 主键字段名称<br/>Column: [id_column] */
    @Column(value = "id_column")
    private String idColumn;

    /** 最后一次扫描的主键的名称<br/>Column: [last_key] */
    @Column(value = "last_key")
    private String lastKey;

    /** 最后一次扫描的时间<br/>Column: [last_scan_time] */
    @Column(value = "last_scan_time")
    private Date lastScanTime;

    /** 如果匹配中，主要记录的字段信息，只限一个<br/>Column: [result_main_columns] */
    @Column(value = "result_main_columns", readIfNullScript = "''")
    private String resultMainColumns;

    /** 错误时发送邮件；复用后也支持钉钉<br/>Column: [send_email] */
    @Column(value = "send_email", readIfNullScript = "''")
    private String sendEmail;

}