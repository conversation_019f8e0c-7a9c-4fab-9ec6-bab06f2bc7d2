package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 标准宽表列枚举值
 */
@Data
@ToString
@Table("database_std_wide_table_column_enum")
public class DatabaseStdWideTableColumnEnumDO extends AdminCoreDO {

    /** 列id<br/>Column: [column_id] */
    @Column(value = "column_id")
    private Long columnId;

    /** 枚举值<br/>Column: [enum_value] */
    @Column(value = "enum_value")
    private String enumValue;

    /** 枚举值说明<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 是否已确认该枚举值的出现影响已处理<br/>Column: [is_confirm] */
    @Column(value = "is_confirm")
    private Boolean isConfirm;

    /** 首次发现时间<br/>Column: [first_found_time] */
    @Column(value = "first_found_time")
    private LocalDateTime firstFoundTime;

}