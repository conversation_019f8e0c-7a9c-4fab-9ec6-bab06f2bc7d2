package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("database_scan_config_detail")
public class DatabaseScanConfigDetailDO extends AdminCoreDO {

    /** database_scan_config的id<br/>Column: [scan_config_id] */
    @Column(value = "scan_config_id")
    private Long scanConfigId;

    /** 模式：MATCH/FILTER<br/>Column: [text_type] */
    @Column(value = "match_type")
    private String matchType;

    /** sql匹配方式<br/>Column: [sql_condition] */
    @Column(value = "sql_condition")
    private String sqlCondition;

    /** 脚本模式使用，如果返回true则记录<br/>Column: [mvel_script] */
    @Column(value = "mvel_script")
    private String mvelScript;

}
