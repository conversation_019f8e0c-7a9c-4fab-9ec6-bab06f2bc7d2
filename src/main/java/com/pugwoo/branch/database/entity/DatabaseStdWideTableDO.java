package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalTime;

/**
 * 标准宽表
 */
@Data
@ToString
@Table("database_std_wide_table")
public class DatabaseStdWideTableDO extends AdminBaseDO {

    /** 标准宽表名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 数据库连接id<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 数据库名<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 标准宽表表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 表的数据类型，SNAPSHOT全量快照，FULL全量无快照<br/>Column: [table_data_type] */
    @Column(value = "table_data_type")
    private String tableDataType;

    /** 更新频率，用秒来表达，例如按天就是86400<br/>Column: [refresh_freq] */
    @Column(value = "refresh_freq")
    private Integer refreshFreq;

    /** 数据刷新延迟类型，T、T-1、T-2<br/>Column: [refresh_delay_type] */
    @Column(value = "refresh_delay_type")
    private String refreshDelayType;

    /** 期望的刷新时间，一般来说，只有按天刷新的才有个期望的刷新时间<br/>Column: [refresh_time] */
    @Column(value = "refresh_time")
    private LocalTime refreshTime;

    /** 来源业务系统<br/>Column: [source_biz_system] */
    @Column(value = "source_biz_system")
    private String sourceBizSystem;

    /** 表的维护人，多个人用逗号隔开<br/>Column: [maintainer] */
    @Column(value = "maintainer")
    private String maintainer;

    /** 关注人，多个用逗号隔开<br/>Column: [follower] */
    @Column(value = "follower")
    private String follower;

    /** 简介，一句话介绍这张表，该表的每一行代表什么<br/>Column: [summary] */
    @Column(value = "summary")
    private String summary;

    /** 一个简单的例子来概要说明这张表<br/>Column: [summary_demo] */
    @Column(value = "summary_demo")
    private String summaryDemo;

    /** 宽表的文档，富文本，markdown格式<br/>Column: [doc_markdown] */
    @Column(value = "doc_markdown")
    private String docMarkdown;

    /** 宽表的文档，html格式，它和markdown对应<br/>Column: [doc_html] */
    @Column(value = "doc_html")
    private String docHtml;

    /** 分区字段<br/>Column: [partition_column] */
    @Column(value = "partition_column")
    private String partitionColumn;

    /** 最小的分区值<br/>Column: [partition_min] */
    @Column(value = "partition_min")
    private String partitionMin;

    /** 目前的最大分区值<br/>Column: [partition_max] */
    @Column(value = "partition_max")
    private String partitionMax;

    /** 分区条数，一般取最近3天的平均值，是个近似值<br/>Column: [partition_count] */
    @Column(value = "partition_count")
    private Integer partitionCount;

    /** 条数波动范围<br/>Column: [partition_count_range] */
    @Column(value = "partition_count_range")
    private Integer partitionCountRange;

}