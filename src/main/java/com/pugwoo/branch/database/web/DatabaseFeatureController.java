package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.model.*;
import com.pugwoo.branch.database.service.DatabaseOperateService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.web.dto.QueryProcessListReq;
import com.pugwoo.branch.database.web.dto.QueryProcessListResp;
import com.pugwoo.branch.database.web.dto.QuerySystemVariablesReq;
import com.pugwoo.branch.database.web.dto.QuerySystemVariablesResp;
import com.pugwoo.branch.database.web.dto.QueryTableSizeInfoResp;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 数据库特性管理，可以查看各种附加在数据库之上的功能
 */
@RestController
@Permission(value = "DatabaseManage", name = "数据库管理列表")
@RequestMapping("/database_feature")
public class DatabaseFeatureController {

    @Autowired
    private DatabaseService databaseService;

    /**数据库空间管理*/
    @GetMapping("get_table_size_info")
    public QueryTableSizeInfoResp getTableSizeInfo(Long databaseId) {
        DatabaseDO databaseDO = databaseService.getById(databaseId);
        DatabaseOperateService databaseOperateService = databaseService.getDatabaseOperateService(databaseDO);

        List<TableSizeInfoDTO> list = databaseOperateService.getAllTableFileSize(databaseDO);
        Integer clusterNodeCount = databaseOperateService.getClusterNodeCount(databaseDO);
        Map<String, Integer> shardNum = databaseOperateService.getShardNum(databaseDO);

        ListUtils.sortDescNullLast(list, o -> o.getDiskSizeByte());

        QueryTableSizeInfoResp resp = new QueryTableSizeInfoResp();
        resp.setDatabaseType(databaseDO.getType());
        resp.setTableSizeInfo(list);
        resp.setClusterNodeCount(clusterNodeCount);
        if (shardNum != null) {
            resp.setShardNum(StringTools.join(",", ListUtils.transform(shardNum.entrySet(), o -> "集群" + o.getKey() + ":" + o.getValue())));
        }

        return resp;
    }

    @GetMapping("/query_process_list")
    public QueryProcessListResp queryProcessList(QueryProcessListReq req) {
        DatabaseDO databaseDO = databaseService.getById(req.getDatabaseId());
        DatabaseOperateService databaseOperateService = databaseService.getDatabaseOperateService(databaseDO);

        ProcessListReqDTO config = new ProcessListReqDTO();
        config.setIsQueryRowLock(req.getIsQueryRowLock());
        ProcessListRespDTO processResp = databaseOperateService.queryProcessList(databaseDO, config);

        List<ProcessListDTO> list = processResp.getProcessList();

        List<ProcessListDTO> sleepAndNoLocks = ListUtils.filter(list, ProcessListDTO::isSleepAndNoLocksNoUndo);
        List<ProcessListDTO> notSleeps = ListUtils.filter(list, o -> !o.isSleepAndNoLocksNoUndo());

        ListUtils.sortDescNullLast(notSleeps, ProcessListDTO::getTime);

        QueryProcessListResp resp = new QueryProcessListResp();
        resp.setProcessList(notSleeps);
        resp.setQueryLockCostMs(processResp.getQueryLockCostMs());
        resp.setSleepAndNoLockConnections(sleepAndNoLocks.size());

        Map<String, String> variables = databaseOperateService.queryDBVariables(databaseDO);
        resp.setIsPerformanceSchemaOn("ON".equals(variables.get("performance_schema")));

        return resp;
    }

    @PostMapping("/kill_thread")
    public WebJsonBean<?> killThread(Long databaseId, Long threadId) {
        DatabaseDO databaseDO = databaseService.getById(databaseId);
        boolean result = databaseService.getDatabaseOperateService(databaseDO).killProcess(databaseDO, threadId);
        return result ? WebJsonBean.ok() : WebJsonBean.fail("kill fail, see server log");
    }

    /**
     * 根据状态信息批量kill thread
     * @param state 匹配批量删除的sql的状态，包含匹配，不区分大小写，必须提供
     * @param executeSecond 要删除的sql至少执行了多少秒，默认值3，即3秒
     */
    @PostMapping("/kill_thread_batch")
    public WebJsonBean<?> killThreadBatch(Long databaseId, String state, Integer executeSecond) {
        if (StringTools.isBlank(state)) {
            return WebJsonBean.fail("state参数必须提供");
        }
        if (executeSecond == null) {
            executeSecond = 3;
        }

        DatabaseDO databaseDO = databaseService.getById(databaseId);
        ProcessListReqDTO config = new ProcessListReqDTO();
        config.setIsQueryRowLock(false);
        List<ProcessListDTO> list = databaseService.getDatabaseOperateService(databaseDO)
                .queryProcessList(databaseDO, config).getProcessList();

        Integer finalExecuteSecond = executeSecond;
        List<ProcessListDTO> filteredProcess = ListUtils.filter(list, o ->
                o.getState() != null && o.getState().toLowerCase().contains(state.toLowerCase())
                        && o.getTime() != null && o.getTime() > finalExecuteSecond);

        int successCount = 0;
        for (ProcessListDTO process :filteredProcess) {
            boolean result = databaseService.getDatabaseOperateService(databaseDO).killProcess(databaseDO, process.getId());
            if (result) {
                successCount++;
            }
        }

        return WebJsonBean.ok(successCount);
    }

    @GetMapping("/query_system_variables")
    public QuerySystemVariablesResp querySystemVariables(QuerySystemVariablesReq req) {
        DatabaseDO databaseDO = databaseService.getById(req.getDatabaseId());
        DatabaseOperateService databaseOperateService = databaseService.getDatabaseOperateService(databaseDO);
        List<SystemVariablesDTO> systemVariablesDTOS = databaseOperateService.querySystemVariables(databaseDO);

        QuerySystemVariablesResp resp = new QuerySystemVariablesResp();
        resp.setItems(ListUtils.transform(systemVariablesDTOS, QuerySystemVariablesResp.Item::from));
        return resp;
    }

}
