package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class SaveScanConfigDetailReq {

    @NotBlank(message = "缺少resultMainColumns")
    private String resultMainColumns;

    @NotNull(message = "缺少scanConfigId")
    private Long scanConfigId;

    @Valid
    @NotNull(message = "缺少scanConfigDetail")
    private ScanConfigDetailDTO scanConfigDetail;

    public List<DatabaseScanConfigDetailDO> toDetailDOList() {
        return scanConfigDetail.toDetailDOList(scanConfigId);
    }
}
