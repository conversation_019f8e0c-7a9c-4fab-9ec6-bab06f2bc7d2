package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.enums.DatabaseScanTextTypeEnum;
import com.pugwoo.wooutils.json.JSON;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScanConfigDetailDTO {

    /**匹配字符串*/
    @Valid
    private List<MatchFilterDetailDTO> matchTextContent;
    /**过滤字符串*/
    @Valid
    private List<MatchFilterDetailDTO> filterTextContent;

    @Data
    public static class MatchFilterDetailDTO {
        private String sqlCondition;
        private String mvelScript;
    }

    public List<DatabaseScanConfigDetailDO> toDetailDOList(Long scanConfigId) {
        List<DatabaseScanConfigDetailDO> list = new ArrayList<>();

        DatabaseScanConfigDetailDO detailDO = new DatabaseScanConfigDetailDO();
        detailDO.setScanConfigId(scanConfigId);
        // 添加匹配的
        if (this.getMatchTextContent() != null) {
            for (ScanConfigDetailDTO.MatchFilterDetailDTO match : this.getMatchTextContent()) {
                DatabaseScanConfigDetailDO matchDetailDO = JSON.clone(detailDO);
                matchDetailDO.setMvelScript(match.getMvelScript());
                matchDetailDO.setMatchType(DatabaseScanTextTypeEnum.MATCH.getCode());
                matchDetailDO.setSqlCondition(match.getSqlCondition());
                list.add(matchDetailDO);
            }
        }

        // 添加过滤的
        if (this.getFilterTextContent() != null) {
            for (ScanConfigDetailDTO.MatchFilterDetailDTO filter : this.getFilterTextContent()) {
                DatabaseScanConfigDetailDO filterDetailDO = JSON.clone(detailDO);
                filterDetailDO.setMvelScript(filter.getMvelScript());
                filterDetailDO.setMatchType(DatabaseScanTextTypeEnum.FILTER.getCode());
                filterDetailDO.setSqlCondition(filter.getSqlCondition());
                list.add(filterDetailDO);
            }
        }

        return list;
    }


}
