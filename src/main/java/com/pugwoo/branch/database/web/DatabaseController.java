package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.enums.EnvEnum;
import com.pugwoo.branch.database.model.DatabaseStatusDTO;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.vo.DatabaseVO;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 */
@Permission(value = "DatabaseManage", name = "数据库管理列表")
@RestController
@RequestMapping("/database")
public class DatabaseController {
    
    @Autowired
    private DatabaseService databaseService;
    
    @GetMapping("list")
    public ModelAndView list() {
        ModelAndView modelAndView = new ModelAndView("database/database_list");
        modelAndView.addObject("databaseTypes",
                ListUtils.transform(DatabaseTypeEnum.values(), o -> o.getCode()));
        modelAndView.addObject("envs",
                ListUtils.transform(EnvEnum.values(), o -> MapUtils.of("code", o.getCode(),
                                                                      "name", o.getName())));
        return modelAndView;
    }
    
    @GetMapping("page")
    public WebJsonBean<?> getPage(int page, int pageSize, String env, String type) {
        PageData<DatabaseVO> pageData = databaseService.getPage(page, pageSize, env, type);
        return WebJsonBean.ok(pageData);
    }

    @GetMapping("/get_database_status")
    public DatabaseStatusDTO getDatabaseStatus(Long databaseId) {
        return databaseService.getDatabaseStatus(databaseId);
    }

    /**提供数据库实例给前端下拉框选择*/
    @GetMapping("get_database_for_select")
    public WebJsonBean<?> getDatabaseForSelect() {
        List<DatabaseDO> all = databaseService.getAll();
        return WebJsonBean.ok(ListUtils.transform(all, o -> MapUtils.of(
                "databaseId", o.getId(), "name", o.getName())));
    }

    /**提供数据库名称给前端下拉选择*/
    @GetMapping("get_database_name_for_select")
    public WebJsonBean<?> getDatabaseNameForSelect(Long databaseId) {
        DatabaseDO databaseDO = databaseService.getById(databaseId);
        return WebJsonBean.ok(databaseService.getDatabaseOperateService(databaseDO).listDatabaseNames(databaseDO));
    }

    @PostMapping("add_or_update")
    public WebJsonBean<?> addOrUpdate(DatabaseDO databaseDO) {
        WebCheckUtils.assertNotNull(databaseDO, "缺少修改的对象参数");
        WebCheckUtils.clearBaseInfo(databaseDO);
        WebCheckUtils.assertNotBlank(databaseDO.getName(), "连接名称不能为空");
        WebCheckUtils.assertNotBlank(databaseDO.getEnv(), "环境不能为空");
        WebCheckUtils.assertNotBlank(databaseDO.getType(), "类型不能为空");
        WebCheckUtils.assertNotBlank(databaseDO.getHost(), "主机不能为空");
        WebCheckUtils.assertNotNull(databaseDO.getPort(), "端口不能为空");
        WebCheckUtils.assertNotBlank(databaseDO.getUsername(), "用户名不能为空");
        WebCheckUtils.assertNotNull(databaseDO.getPassword(), "密码不能为空");
        Integer port = databaseDO.getPort();
        if (port < 0 || port > 65535) {
            throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, "端口取值范围必须为 0~65535");
        }
        
        // 密码为空白表示不更新
        if(StringTools.isBlank(databaseDO.getPassword())) {
            databaseDO.setPassword(null);
        }
    
        databaseService.insertOrUpdate(databaseDO);
        return WebJsonBean.ok();
    }

    /**说明：这里之所以传DO，是因为此时可能还没有id*/
    @PostMapping("/test_connect")
    public WebJsonBean<?> testConnect(DatabaseDO databaseDO) {
        WebCheckUtils.assertNotNull(databaseDO, "缺少修改的对象参数");
        DatabaseStatusDTO status = databaseService.getDatabaseOperateService(databaseDO).ping(databaseDO, true);
        return status.isConnectSuccess() ? WebJsonBean.ok()
                : WebJsonBean.fail(status.getStatusCode() + ": " + status.getStatusMsg());
    }

    @RequestMapping("delete")
    public WebJsonBean<?> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(databaseService.deleteById(id));
    }

    @PostMapping("enable_monitor")
    public WebJsonBean<?> enableMonitor(Long databaseId, Boolean enabled) {
        WebCheckUtils.assertNotNull(databaseId, "缺少参数databaseId");
        WebCheckUtils.assertNotNull(enabled, "缺少参数enabled");
        return WebJsonBean.ok(databaseService.enableMonitor(databaseId, enabled));
    }

    @PostMapping("enable_capacity_monitor")
    public WebJsonBean<?> enableCapacityMonitor(Long databaseId, Boolean enabled) {
        WebCheckUtils.assertNotNull(databaseId, "缺少参数databaseId");
        WebCheckUtils.assertNotNull(enabled, "缺少参数enabled");
        return WebJsonBean.ok(databaseService.enableCapacityMonitor(databaseId, enabled));
    }

    @GetMapping("get_build_in_monitor_count")
    public WebJsonBean<?> getBuildInMonitorCount(Long databaseId) {
        WebCheckUtils.assertNotNull(databaseId, "缺少参数databaseId");
        return WebJsonBean.ok(databaseService.getBuildInMonitorCount(databaseId));
    }

    @PostMapping("reset_connection_pool")
    public WebJsonBean<?> resetConnectionPool() {
        databaseService.resetConnectionPool();
        return WebJsonBean.ok();
    }
}
