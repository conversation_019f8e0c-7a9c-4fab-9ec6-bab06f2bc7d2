package com.pugwoo.branch.database.web.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ViewGroupDataReq {

    /**数据库实例id*/
    @NotNull(message = "数据库实例id不能为空")
    private Long databaseId;

    /**数据库名称*/
    private String databaseName;

    /**查询SQL*/
    @NotBlank(message = "查询SQL不能为空")
    private String querySql;

    /**聚合函数*/
    private String aggFunc;

    /**指定查询哪一列的，当指定时，它最大查询前1000条*/
    private String specificColumn;

}
