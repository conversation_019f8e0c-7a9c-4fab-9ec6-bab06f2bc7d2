package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.enums.DatabaseScanTextTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

@Data
public class QueryScanConfigDetailResp {

    private ScanConfigDetailDTO scanConfigDetail;

    public static QueryScanConfigDetailResp transFrom(List<DatabaseScanConfigDetailDO> configDetails) {

        ScanConfigDetailDTO scanConfigDetailDTO = new ScanConfigDetailDTO();

        List<DatabaseScanConfigDetailDO> match = ListUtils.filter(configDetails, o -> DatabaseScanTextTypeEnum.MATCH.getCode().equals(o.getMatchType()));
        scanConfigDetailDTO.setMatchTextContent(ListUtils.transform(match, o -> {
            ScanConfigDetailDTO.MatchFilterDetailDTO matchFilterDetailDTO = new ScanConfigDetailDTO.MatchFilterDetailDTO();
            matchFilterDetailDTO.setMvelScript(o.getMvelScript());
            matchFilterDetailDTO.setSqlCondition(o.getSqlCondition());
            return matchFilterDetailDTO;
        }));

        List<DatabaseScanConfigDetailDO> filter = ListUtils.filter(configDetails, o -> DatabaseScanTextTypeEnum.FILTER.getCode().equals(o.getMatchType()));
        scanConfigDetailDTO.setFilterTextContent(ListUtils.transform(filter, o -> {
            ScanConfigDetailDTO.MatchFilterDetailDTO matchFilterDetailDTO = new ScanConfigDetailDTO.MatchFilterDetailDTO();
            matchFilterDetailDTO.setMvelScript(o.getMvelScript());
            matchFilterDetailDTO.setSqlCondition(o.getSqlCondition());
            return matchFilterDetailDTO;
        }));

        QueryScanConfigDetailResp resp = new QueryScanConfigDetailResp();
        resp.setScanConfigDetail(scanConfigDetailDTO);
        return resp;
    }

}
