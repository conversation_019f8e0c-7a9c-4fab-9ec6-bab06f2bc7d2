package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.TableSizeInfoDTO;
import lombok.Data;

import java.util.List;

@Data
public class QueryTableSizeInfoResp {

    /**
     * 数据库类型
     * @see DatabaseTypeEnum
     */
    private String databaseType;

    /**
     * 表大小信息
     */
    private List<TableSizeInfoDTO> tableSizeInfo;

    /**
     * 集群节点数
     */
    private Integer clusterNodeCount;

    /**
     * 分片数，支持按集群名称显示
     */
    private String shardNum;

}
