package com.pugwoo.branch.database.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum EnvEnum {

    DEVELOP("DEVELOP", "开发"),

    TEST("TEST", "测试"),

    PROD("PROD", "生产"),

    ;

    private String code;
    private String name;

    EnvEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static EnvEnum getByCode(String code) {
        for (EnvEnum e : EnvEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        EnvEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
