package com.pugwoo.branch.nimble_orm.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("nimble_orm_gen_log")
public class NimbleOrmGenLogDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** create_time<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 是否成功<br/>Column: [is_success] */
    @Column(value = "is_success")
    private Boolean isSuccess;

    /** 创建SQL<br/>Column: [create_sql] */
    @Column(value = "create_sql", maxStringLength = 65535)
    private String createSql;

    /** 生成的do类<br/>Column: [gen_do] */
    @Column(value = "gen_do", maxStringLength = 65535)
    private String genDo;

}