package com.pugwoo.branch.nimble_orm.model;

import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.List;

@Data
public class GenerateColumnVO {
	
	// 列名
	private String name;

	// mysql类型
	private String dataType;

	/**
	 * 数据类型的额外参数，目前用途：
	 * 1）给tinyint类型判断长度
	 * 2）decimal的精度和小数点位数
	 */
	private List<String> argumentsStringList;
	
	// java类型
	private String javaType;
	
	// java成员变量名
	private String javaVarName;
	
	// java成员变量的第一个字符小写
	private String downJavaVarName;
	
	// java成员变量的第一个字符大写
	private String upJavaVarName;

	/**转换成clickhouse的数据类型*/
	private String toClickhouseType;
	
	// 是否主键
	private boolean isKey = false;
	
	// 是否自增
	private boolean isAutoIncrement = false;
	
	// 是否无符号
	private boolean isUnsigned = false;

	/**是否标识了not null*/
	private boolean isNotNull = false;
	
	// 列备注
	private String comment;

	/**标识是否是最后一列，方便vm渲染*/
	private boolean isLastColumn = false;

	/**是否是BaseDO基类的列，不要删除，vm中用到*/
	public boolean getIsBaseColumn() {
		return StringTools.isInIgnoreCase(name,
				"id", "deleted", "create_time", "update_time");
	}

}
