package com.pugwoo.branch.nimble_orm.utils;

import com.pugwoo.branch.nimble_orm.model.GenerateColumnVO;
import com.pugwoo.branch.nimble_orm.model.GenerateTableVO;
import com.pugwoo.wooutils.collect.ListUtils;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Function;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.create.table.ColumnDefinition;
import net.sf.jsqlparser.statement.create.table.CreateTable;
import net.sf.jsqlparser.statement.create.table.Index;
import net.sf.jsqlparser.statement.select.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class SQLParserUtils {

	private static List<String> trimChars = ListUtils.of("`", "'", "\"");

	public static GenerateTableVO parse(String sql, boolean enableBaseDO, String baseDOClass)
			throws JSQLParserException {
		Statement statement = CCJSqlParserUtil.parse(sql);
		if (statement instanceof Select) {
			return parse((Select) statement);
		} else if (statement instanceof CreateTable){
			return parse((CreateTable) statement, enableBaseDO, baseDOClass);
		} else {
			throw new RuntimeException("只支持CREATE TABLE和SELECT");
		}
	}

	private static GenerateTableVO parse(Select select) {

		PlainSelect selectBody = null;
		if (select.getSelectBody() instanceof SetOperationList) {
			selectBody = (PlainSelect) ((SetOperationList)select.getSelectBody()).getSelects().get(0);
		} else {
			selectBody = (PlainSelect) select.getSelectBody();
		}

		String tableName;
		if (selectBody.getFromItem() instanceof Table) {
			tableName = ((Table) selectBody.getFromItem()).getName();
		} else {
			tableName = "your_table_name"; // 这种情况一般是子select查询，拿不到表名
		}

		GenerateTableVO generateTableVO = new GenerateTableVO();
		generateTableVO.setSqlType("SELECT");
		generateTableVO.setName(escape(tableName, trimChars));
		fillOtherClassName(generateTableVO);

		initColumns(generateTableVO);

		List<GenerateColumnVO> columns = new ArrayList<>();
		generateTableVO.setColumns(columns);

		List<SelectItem<?>> selectItems = selectBody.getSelectItems();
		for (SelectItem<?> selectItem : selectItems) {
			GenerateColumnVO columnVO = new GenerateColumnVO();
			columns.add(columnVO);
			Alias alias = selectItem.getAlias();
			if (alias != null) {
				columnVO.setName(alias.getName());
			} else {
				Expression expression = selectItem.getExpression();
				if (expression instanceof Column) {
					columnVO.setName(((Column)expression).getColumnName());
				} else if (expression instanceof Function) {
					columnVO.setName(expression.toString());
				} else {
					columnVO.setName(expression.toString());
				}
			}
			columnVO.setName(escape(columnVO.getName(), trimChars));

			columnVO.setJavaType("String");
			columnVO.setJavaVarName(transTFName(columnVO.getName(), "", 0));
			columnVO.setUpJavaVarName(transTFName(columnVO.getName(), "", 1));
			columnVO.setDownJavaVarName(transTFName(columnVO.getName(), "", 2));

			if (Objects.equals(selectItem, selectItems.get(selectItems.size() - 1))) {
				columnVO.setLastColumn(true);
			}
		}

		return generateTableVO;
	}
	
	private static GenerateTableVO parse(CreateTable statement, boolean enableBaseDO, String baseDOClass) {

		GenerateTableVO generateTableVO = new GenerateTableVO();
		generateTableVO.setSqlType("CREATE");
		generateTableVO.setName(escape(statement.getTable().getName(), trimChars));
		fillOtherClassName(generateTableVO);
		
		// 设置表格备注
		List<String> tableOptions = statement.getTableOptionsStrings();
		if(tableOptions != null) {
			for(int i = 0; i < tableOptions.size(); i++) {
				if("COMMENT".equalsIgnoreCase(tableOptions.get(i))) {
					if(i < tableOptions.size() - 2) {
						if("=".equals(tableOptions.get(i + 1).trim())) {
							generateTableVO.setComment(escape(tableOptions.get(i + 2), trimChars));
						}
					}
				}
			}
		}

		Set<String> javaImports = initColumns(generateTableVO);
		
		List<GenerateColumnVO> columns = new ArrayList<>();
		generateTableVO.setColumns(columns);
		List<ColumnDefinition> columnDefs = statement.getColumnDefinitions();
		
		// 处理列
		if(columnDefs != null) {

			for(int c = 0; c < columnDefs.size(); c++) {
				ColumnDefinition colDef = columnDefs.get(c);

				GenerateColumnVO column = new GenerateColumnVO();
				column.setName(escape(colDef.getColumnName(), trimChars));
				String dataType = colDef.getColDataType().getDataType();

				if ("nullable".equalsIgnoreCase(dataType)) { // 处理clickhouse的nullable
					if (colDef.getColDataType().getArgumentsStringList() != null &&
							!colDef.getColDataType().getArgumentsStringList().isEmpty()) {
						dataType = colDef.getColDataType().getArgumentsStringList().get(0);
					} else if (ListUtils.isNotEmpty(colDef.getColumnSpecs())) { // 处理clickhouse的 nullable(date)
						dataType = colDef.getColumnSpecs().get(0);
						if (dataType != null && dataType.startsWith("(")) {
							dataType = dataType.substring(1);
						}
						if (dataType != null && dataType.endsWith(")")) {
							dataType = dataType.substring(0, dataType.length() - 1);
						}
					}
				}
				column.setDataType(dataType);
				column.setArgumentsStringList(colDef.getColDataType().getArgumentsStringList());
				
				List<String> specs = colDef.getColumnSpecs();
				if(specs != null) {
					for(int i = 0; i < specs.size(); i++) {
						if("AUTO_INCREMENT".equalsIgnoreCase(specs.get(i))) {
							column.setAutoIncrement(true);
						}
						if("UNSIGNED".equalsIgnoreCase(specs.get(i))) {
							column.setUnsigned(true);
						}
						if("COMMENT".equalsIgnoreCase(specs.get(i)) && i < specs.size() - 1) {
							column.setComment(escape(specs.get(i + 1), trimChars));
						}

						if ("PRIMARY".equalsIgnoreCase(specs.get(i)) && i < specs.size() - 1 &&
								"KEY".equalsIgnoreCase(specs.get(i + 1))) {
							column.setKey(true);
						}
						if ("NOT".equalsIgnoreCase(specs.get(i)) && i < specs.size() - 1 &&
								"NULL".equalsIgnoreCase(specs.get(i + 1))) {
							column.setNotNull(true);
						}
					}
				}
				
				String javaType = DataTypeUtils.dbToJavaType(dataType, javaImports, column.isUnsigned(),
						column.getArgumentsStringList());
				column.setJavaType(javaType);
				column.setJavaVarName(transTFName(column.getName(), "", 0));
				column.setUpJavaVarName(transTFName(column.getName(), "", 1));
				column.setDownJavaVarName(transTFName(column.getName(), "", 2));

				String clickhouseDataType = DataTypeUtils.mysqlToClickhouseType(
						dataType, column.isUnsigned(), column.getArgumentsStringList());
				column.setToClickhouseType(clickhouseDataType);

				if (c == columnDefs.size() - 1) {
					column.setLastColumn(true);
				}

				columns.add(column);
			}
		}
		
		// 处理主键索引
		List<Index> indexes = statement.getIndexes();
		if(indexes != null) {
			for(Index index : indexes) {
				if("PRIMARY KEY".equalsIgnoreCase(index.getType())) {
					List<String> colNames = index.getColumnsNames();
					for(String colName : colNames) {
						colName = escape(colName, trimChars);
						for(GenerateColumnVO generateColumnVO : columns) {
							if(generateColumnVO.getName().equalsIgnoreCase(colName)) {
								generateColumnVO.setKey(true);
							}
						}
					}
				}
			}
		}
		
		// 判断下是否使用baseDO
		boolean useBaseDO = false;
		if(enableBaseDO) {
			boolean hasId = false, hasDeleted = false, hasCreateTime = false, hasUpdateTime = false;
			for(GenerateColumnVO colVO : generateTableVO.getColumns()) {
				if("id".equalsIgnoreCase(colVO.getName()) && colVO.isAutoIncrement() && colVO.isKey()) {
					hasId = true;
				}
				if("deleted".equalsIgnoreCase(colVO.getName())) {
					hasDeleted = true;
				}
				if("create_time".equalsIgnoreCase(colVO.getName())) {
					hasCreateTime = true;
				}
				if("update_time".equalsIgnoreCase(colVO.getName())) {
					hasUpdateTime = true;
				}
			}
			if(hasId && hasDeleted && hasCreateTime && hasUpdateTime) {
				useBaseDO = true;
			}
		}
		if(useBaseDO && StringUtils.isNotBlank(baseDOClass)) {
			generateTableVO.setUseBaseDO(true);
			javaImports.add(baseDOClass.trim());
			String[] split = baseDOClass.split("\\.");
			if (split.length > 0) {
				generateTableVO.setBaseClassSimpleName(split[split.length - 1]);
			}
		}
		
		// 计算主键数
		int keyColumnNum = 0;
		for(GenerateColumnVO colVO : generateTableVO.getColumns()) {
			if(colVO.isKey()) {
				keyColumnNum++;
			}
		}
		generateTableVO.setKeyColumnNum(keyColumnNum);
		
		return generateTableVO;
	}

	/**
	 * 去掉name中左右两侧的指定字符（只去一对），先trim name，处理完再trim
	 */
	private static String escape(String name, List<String> ch) {
		if(name == null) {
			return "";
		}
		name = name.trim();

		for (String c : ch) {
			if (name.startsWith(c) && name.endsWith(c)) {
				name = name.substring(1, name.length() - 1);
				return name.trim(); // 需要再trim一次，一般列名表名左右两侧不允许有空格，一般也不会在这些地方留空格
			}
		}
		return name.trim(); // 需要再trim一次，一般列名表名左右两侧不允许有空格，一般也不会在这些地方留空格
	}
	
	/**
	 * 下划线方式转成驼峰形式
	 * @param excludePrefix 需要去掉的前缀，如果有的话
	 * @param changeFirstLetter 0 不转换，1转换成大写，2转换成小写
	 */
	private static String transTFName(String str, String excludePrefix, int changeFirstLetter) {
		if(str == null) {
			return "";
		}
		str = str.trim();
		if(excludePrefix != null && !excludePrefix.isEmpty()) {
			if(str.startsWith(excludePrefix)) {
				str = str.substring(excludePrefix.length());
			}
		}
		
		String[] strs = str.split("_");
		if(strs.length == 1) {
			if(changeFirstLetter == 0) {
				return strs[0];
			} else if (changeFirstLetter == 1) {
				return strs[0].substring(0, 1).toUpperCase() + strs[0].substring(1);
			} else if (changeFirstLetter == 2) {
				return strs[0].substring(0, 1).toLowerCase() + strs[0].substring(1);
			}
		}
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < strs.length; i++) {
			if(strs[i].isEmpty()) {
				continue;
			}
			if(i == 0) {
				if(changeFirstLetter == 0) {
					sb.append(strs[i]);
				} else if (changeFirstLetter == 1) {
					sb.append(strs[i].substring(0, 1).toUpperCase()).append(strs[i].substring(1));
				} else if (changeFirstLetter == 2) {
					sb.append(strs[i].substring(0, 1).toLowerCase()).append(strs[i].substring(1));
				}
			} else {
				sb.append(strs[i].substring(0, 1).toUpperCase()).append(strs[i].substring(1));
			}
		}
		return sb.toString();
	}

	private static String toAllLowerClassName(String lowClassName) {
		if(StringUtils.isBlank(lowClassName)) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < lowClassName.length(); i++) {
			char c = lowClassName.charAt(i);
			if(c >= 'A' && c <= 'Z') {
				sb.append("_").append(String.valueOf(c).toLowerCase());
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	private static void fillOtherClassName(GenerateTableVO generateTableVO) {
		generateTableVO.setClassName(transTFName(generateTableVO.getName(), "t_", 1));
		String lowClassName = transTFName(generateTableVO.getName(), "t_", 2);
		String allLowClassName = toAllLowerClassName(lowClassName);
		generateTableVO.setLowClassName(lowClassName);
		generateTableVO.setAllLowClassName(allLowClassName);

		String allLowDashClassName = allLowClassName.replace("_", "-");
		generateTableVO.setAllLowDashClassName(allLowDashClassName);
	}

	private static Set<String> initColumns(GenerateTableVO generateTableVO) {
		Set<String> javaImports = new HashSet<>();
		generateTableVO.setJavaImportClasses(javaImports);
		javaImports.add("com.pugwoo.dbhelper.annotation.Table");
		javaImports.add("com.pugwoo.dbhelper.annotation.Column");
		return javaImports;
	}
}
