package com.pugwoo.branch.nimble_orm.utils;

/**
 * 提前预处理SQL，解决解析失败的问题，属于hack的方式
 */
public class PreHandleSQLUtils {

    public static String handle(String sql) {
        sql = handleClickhouse(sql);
        return sql;
    }

    private static String handleClickhouse(String sql) {
        // clickhouse的Distributed engine，目前不支持解析，需要替换掉
        sql = sql.replaceAll("(?i)rand\\(\\)", "rand");
        // 去掉Decimal()的括号
        sql = sql.replaceAll("Decimal\\(.*?\\)", "Decimal");
        // 去掉ON CLUSTER xxxx
        sql = sql.replaceAll("(?i)on cluster [\\w\\d_]+", "");
        // 去掉创建语句中的ORDER BY（正常的建表语句都没有order by）
        sql = sql.replaceAll("(?i)ORDER\\s+BY", "");
        return sql;
    }

}
