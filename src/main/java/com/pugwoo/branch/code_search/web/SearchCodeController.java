package com.pugwoo.branch.code_search.web;

import com.pugwoo.branch.code_search.model.SearchCodeDO;
import com.pugwoo.branch.code_search.service.ISearchCodeService;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Permission(value = "SearchCode", name = "SearchCode查看列表")
@Controller
@RequestMapping(value = "/search_code")
public class SearchCodeController {
    @Autowired
    private ISearchCodeService searchCodeService;

    @RequestMapping("list")
    public String list() {
        return "search/search_code";
    }

    @ResponseBody
    @RequestMapping("start_search")
    public WebJsonBean startSearch(String searchReg) throws IOException {
        WebCheckUtils.assertNotBlank(searchReg, "请输入内容");
        String uuid = searchCodeService.startSearch(searchReg);
        return WebJsonBean.ok(uuid);
    }

    @ResponseBody
    @RequestMapping("get_page")
    public WebJsonBean getPage(String uuid) {
        WebCheckUtils.assertNotBlank(uuid, "参数不正确");
        List<SearchCodeDO> page = searchCodeService.getList(uuid);

        Map<String, Object> result = new HashMap<>();
        result.put("data", ListUtils.transform(page, t -> {
            Map<String, Object> map = new HashMap<>();
            map.put("serialNumber", t.getSerialNumber());
            map.put("repository", t.getRepository());
            map.put("branch", t.getBranch());
            map.put("codeLine", t.getCodeLine());
            map.put("content", t.getContent());
            return map;
        }));
        result.put("progress", searchCodeService.getProgress(uuid));
        return WebJsonBean.ok(result);
    }
}
