package com.pugwoo.branch.code_search.service;

import com.pugwoo.branch.code_search.model.SearchCodeDO;

import java.io.IOException;
import java.util.List;

public interface ISearchCodeService {


    /**
     *
     * @Description: 开始关键字查询
     * @date:  2019-10-16
     * @param searchReg  查询关键字
     *
     */
    String startSearch(String searchReg) throws IOException;

    /**
     * 获得分页数据
     * @param uuid
     */
    List<SearchCodeDO> getList(String uuid);

    /**
     *
     * @Description: 获取进度
     * @date:  2019-10-17
     *
     */
    Integer getProgress(String uuid);
}
