package com.pugwoo.branch.chart.service;

import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.echarts.Create3DBarDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateBubbleDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateLineDTO;
import com.pugwoo.branch.chart.dto.echarts.CreatePieDTO;

/**
 * 用于创建echarts图表的服务
 */
public interface EchartsCreateService {

    /**
     * 创建多折线图表 <a href="https://echarts.apache.org/examples/en/editor.html?c=line-simple">ref</a>
     */
    HtmlCssJsDTO createLine(CreateLineDTO dto);

    /**
     * 创建3d bar图表 <a href="https://echarts.apache.org/examples/en/editor.html?c=transparent-bar3d&gl=1">ref</a>
     */
    HtmlCssJsDTO create3DBar(Create3DBarDTO dto);

    /**
     * 创建饼图 <a href="https://echarts.apache.org/examples/zh/editor.html?c=pie-simple">ref</a>
     */
    HtmlCssJsDTO createPie(CreatePieDTO dto);

    /**
     * 创建气泡图 <a href="https://echarts.apache.org/examples/zh/editor.html?c=bubble-gradient">ref</a>
     */
    HtmlCssJsDTO createBubble(CreateBubbleDTO dto);

}
