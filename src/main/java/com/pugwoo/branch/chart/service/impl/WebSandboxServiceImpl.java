package com.pugwoo.branch.chart.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.echarts.Create3DBarDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateBubbleDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateLineDTO;
import com.pugwoo.branch.chart.dto.echarts.CreatePieDTO;
import com.pugwoo.branch.chart.dto.highchart.CreatePercentageAreaDTO;
import com.pugwoo.branch.chart.dto.tradingvue.CreateTradeDTO;
import com.pugwoo.branch.chart.dto.plantuml.CreatePlantUmlDTO;
import com.pugwoo.branch.chart.entity.WebSandboxDO;
import com.pugwoo.branch.chart.enums.ChartEnum;
import com.pugwoo.branch.chart.service.EchartsCreateService;
import com.pugwoo.branch.chart.service.HighchartCreateService;
import com.pugwoo.branch.chart.service.TradingVueService;
import com.pugwoo.branch.chart.service.PlantUmlService;
import com.pugwoo.branch.chart.service.WebSandboxService;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class WebSandboxServiceImpl implements WebSandboxService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private EchartsCreateService echartsCreateService;
    @Resource
    private HighchartCreateService highchartCreateService;
    @Resource
    private TradingVueService tradingVueService;
    @Resource
    private PlantUmlService plantUmlService;

    @Override
    public WebSandboxDO getByLinkCode(String linkCode) {
        if (StringTools.isBlank(linkCode)) {
            return null;
        }
        return dbHelper.getOne(WebSandboxDO.class, "where link_code=?", linkCode);
    }

    @Override
    public void recordVisit(WebSandboxDO webSandboxDO) {
        webSandboxDO.setLastVisitTime(LocalDateTime.now());
        webSandboxDO.setVisitCount(webSandboxDO.getVisitCount() == null ? 1 : webSandboxDO.getVisitCount() + 1);
        dbHelper.update(webSandboxDO);
    }

    @Override
    public String createWebSandbox(CreateSandboxReq createSandboxReq) {
        ChartEnum chartEnum = ChartEnum.getByLibraryChart(createSandboxReq.getLibrary(), createSandboxReq.getCharts());
        if (chartEnum == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR,
                    "未知的库和图表:" + createSandboxReq.getLibrary() + "-" + createSandboxReq.getCharts());
        }

        if (StringTools.isBlank(createSandboxReq.getLibrary())) {
            createSandboxReq.setLibrary(chartEnum.getLibrary());
        }

        HtmlCssJsDTO htmlCssJsDTO = switch (chartEnum) {
            case BAR_3D -> echartsCreateService.create3DBar(Create3DBarDTO.from(createSandboxReq));
            case LINES -> echartsCreateService.createLine(CreateLineDTO.from(createSandboxReq));
            case PERCENTAGE_AREA -> highchartCreateService.createPercentageAreaChart(CreatePercentageAreaDTO.from(createSandboxReq));
            case PIE -> echartsCreateService.createPie(CreatePieDTO.from(createSandboxReq));
            case BUBBLE -> echartsCreateService.createBubble(CreateBubbleDTO.from(createSandboxReq));
            case K_LINE -> tradingVueService.createChart(CreateTradeDTO.from(createSandboxReq));
            case PLANTUML -> plantUmlService.createChart(CreatePlantUmlDTO.from(createSandboxReq));
        };

        String randomLinkCode = getRandomLinkCode();

        if (createSandboxReq.getTmp() != null && createSandboxReq.getTmp()) {
            randomLinkCode = "tmp-" + randomLinkCode;
            redisHelper.setObject("chart:" + randomLinkCode, 600, htmlCssJsDTO);
            return randomLinkCode;
        } else {
            WebSandboxDO webSandboxDO = new WebSandboxDO();
            webSandboxDO.setName(createSandboxReq.getName());
            webSandboxDO.setHtml(htmlCssJsDTO.getHtml());
            webSandboxDO.setCss(htmlCssJsDTO.getCss());
            webSandboxDO.setJs(htmlCssJsDTO.getJs());
            webSandboxDO.setLinkCode(randomLinkCode);
            webSandboxDO.setChartType(createSandboxReq.getLibrary() + "-" + createSandboxReq.getCharts());

            dbHelper.insert(webSandboxDO);
            return webSandboxDO.getLinkCode();
        }
    }

    private static String getRandomLinkCode() {
        return StringTools.randomString("abcdefghijklmnopqrstuvwxyz1234567890", 8);
    }

}
