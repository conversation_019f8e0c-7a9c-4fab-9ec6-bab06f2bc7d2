package com.pugwoo.branch.chart.service.impl;

import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.plantuml.CreatePlantUmlDTO;
import com.pugwoo.branch.chart.service.PlantUmlService;
import com.pugwoo.branch.common.VelocityTools;
import net.sourceforge.plantuml.FileFormat;
import net.sourceforge.plantuml.FileFormatOption;
import net.sourceforge.plantuml.SourceStringReader;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

@Service
public class PlantUmlServiceImpl implements PlantUmlService {

    @Override
    public HtmlCssJsDTO createChart(CreatePlantUmlDTO dto) {
        if (dto == null || dto.getUmlCode() == null) {
            return null;
        }

        HtmlCssJsDTO htmlCssJsDTO = new HtmlCssJsDTO();

        try {
            // 创建 SourceStringReader 对象
            SourceStringReader reader = new SourceStringReader(dto.getUmlCode());

            // 生成SVG到内存
            ByteArrayOutputStream svgStream = new ByteArrayOutputStream();
            reader.generateImage(svgStream, new FileFormatOption(FileFormat.SVG));
            
            // 将SVG转换为字符串
            String svgContent = svgStream.toString("UTF-8");

            // 准备模板参数
            Map<String, Object> params = new HashMap<>();
            params.put("TITLE", dto.getTitle() != null ? dto.getTitle() : "");
            params.put("SVG_CONTENT", svgContent);

            // 渲染模板
            String html = VelocityTools.renderVM("/chart/plantuml/common.vm.html", params);
            htmlCssJsDTO.setHtml(html);
            htmlCssJsDTO.setCss("");
            htmlCssJsDTO.setJs("");

        } catch (Exception e) {
            // 如果生成失败，返回错误信息
            Map<String, Object> params = new HashMap<>();
            params.put("TITLE", dto.getTitle() != null ? dto.getTitle() : "PlantUML图");
            params.put("ERROR_MESSAGE", "PlantUML图生成失败: " + e.getMessage());
            
            String html = VelocityTools.renderVM("/chart/plantuml/error.vm.html", params);
            htmlCssJsDTO.setHtml(html);
            htmlCssJsDTO.setCss("");
            htmlCssJsDTO.setJs("");
        }

        return htmlCssJsDTO;
    }

}
