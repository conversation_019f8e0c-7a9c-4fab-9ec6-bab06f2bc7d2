package com.pugwoo.branch.chart.web;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.entity.WebSandboxDO;
import com.pugwoo.branch.chart.service.WebSandboxService;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.branch.chart.web.resp.CreateSandboxResp;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@NotRequireLogin
@RestController
public class WebSandboxController {

    @Resource
    private WebSandboxService webSandboxService;
    @Resource
    private RedisHelper redisHelper;

    /**
     * 沙盒模式，提供linkCode找到对应的html/css/js，然后渲染出页面。
     */
    @GetMapping("/chart/{linkCode}")
    public void chartLinkCode(@PathVariable("linkCode") String linkCode,
                              HttpServletResponse response) throws IOException {

        String html = "";
        String css = "";
        String js = "";

        if (linkCode.startsWith("tmp-")) {
            HtmlCssJsDTO dto = redisHelper.getObject("chart:" + linkCode, HtmlCssJsDTO.class);
            if (dto == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            } else {
                html = dto.getHtml();
                css = dto.getCss();
                js = dto.getJs();
            }
        } else {
            WebSandboxDO webSandbox = webSandboxService.getByLinkCode(linkCode);
            if (webSandbox == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            } else {
                html = webSandbox.getHtml();
                css = webSandbox.getCss();
                js = webSandbox.getJs();
                webSandboxService.recordVisit(webSandbox);
            }
        }

        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("text/html;charset=utf-8");
        if (StringTools.isNotBlank(html)) {
            response.getWriter().println(html);
        }
        if (StringTools.isNotBlank(css)) {
            response.getWriter().println("<style>" + css + "</style>");
        }
        if (StringTools.isNotBlank(js)) {
            response.getWriter().println("<script>" + js + "</script>");
        }
    }

    @PostMapping("/create_chart")
    public CreateSandboxResp createSandbox(@RequestBody CreateSandboxReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");
        WebCheckUtils.assertNotBlank(req.getCharts(), "缺少图表类型charts");

        String linkCode = webSandboxService.createWebSandbox(req);
        if (StringTools.isBlank(linkCode)) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "创建失败");
        }

        CreateSandboxResp resp = new CreateSandboxResp();
        resp.setLinkCode(linkCode);
        return resp;
    }

}
