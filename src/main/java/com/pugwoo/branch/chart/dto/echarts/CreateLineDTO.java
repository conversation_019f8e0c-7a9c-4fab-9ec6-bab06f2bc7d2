package com.pugwoo.branch.chart.dto.echarts;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创建多折线图
 */
@Slf4j
@Data
public class CreateLineDTO {

    @Data
    public static class YAxis {
        /**Y轴名称*/
        private String name;
        /**该y轴对应的线条*/
        private List<String> lines;
    }

    @NotNull
    private List<DateSingleDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";
    @NotNull
    private Boolean smooth = false;

    /**key支持*?模糊匹配*/
    @NotNull
    private Map<String, String> colors = new HashMap<>();
    /**key支持*?模糊匹配*/
    @NotNull
    private Map<String, String> styles = new HashMap<>();

    /**是否显示缩略图*/
    @NotNull
    private Boolean showZoom = true;
    /**是否把null值连起来*/
    @NotNull
    private Boolean connectNulls = false;

    /**x轴名称*/
    private String xAxisName = "";
    /**y轴名称，当yAxis为空时生效*/
    private String yAxisName = "";

    /**适合于多个y轴的场景*/
    private List<YAxis> yAxis = new ArrayList<>();

    public static CreateLineDTO from(CreateSandboxReq req) {
        ChartCommonUtils.handleDataAsFirstColumnXAxis(req);

        CreateLineDTO createLineDTO = new CreateLineDTO();

        // 1. 处理数据
        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null) {
            List<DateSingleDimValueDTO> data = new ArrayList<>();
            if (columns == 2) { // 1. 处理只有日期+值的表头的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d = new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim("默认");
                    d.setValue(NumberUtils.parseBigDecimal(row.get(1)));
                    data.add(d);
                }
            } else if (columns == 3) { // 2. 处理有日期+dim+值的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d =new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim(row.get(1));
                    d.setValue(NumberUtils.parseBigDecimal(row.get(2)));
                    data.add(d);
                }
            } else {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "表头列数只支持2或3列，实际列数：" + req.getHeaders().size());
            }
            createLineDTO.setData(data);
        }

        // 2. 处理x轴和y轴名称
        List<String> titles = req.getHeaders();
        if (titles != null && titles.size() == 1) {
            createLineDTO.setXAxisName(titles.getFirst());
        } else if (titles != null && titles.size() >= 2) {
            createLineDTO.setXAxisName(titles.getFirst());
            createLineDTO.setYAxisName(titles.getLast());
        }

        // 3. 处理颜色和线条、其它配置
        Map<String, String> colors = ChartCommonUtils.getConfig(req.getConfigs(), "colors", createLineDTO.getColors(),
                o -> ChartCommonUtils.toMap("*", o),
                o -> MapUtils.transform(o, Object::toString));
        if (MapUtils.isEmpty(colors)) { // 增加别称color
            colors = ChartCommonUtils.getConfig(req.getConfigs(), "color", createLineDTO.getColors(),
                    o -> ChartCommonUtils.toMap("*", o),
                    o -> MapUtils.transform(o, Object::toString));
        }
        createLineDTO.setColors(colors);

        Map<String, String> styles = ChartCommonUtils.getConfig(req.getConfigs(), "styles", createLineDTO.getStyles(),
                o -> ChartCommonUtils.toMap("*", o),
                o -> MapUtils.transform(o, Object::toString));
        if (MapUtils.isEmpty(styles)) { // 增加别称style
            styles = ChartCommonUtils.getConfig(req.getConfigs(), "style", createLineDTO.getStyles(),
                    o -> ChartCommonUtils.toMap("*", o),
                    o -> MapUtils.transform(o, Object::toString));
        }
        createLineDTO.setStyles(styles);

        List<YAxis> yAxis = ChartCommonUtils.getConfig(req.getConfigs(), "yAxis", createLineDTO.getYAxis(),
                null, null, o -> {
                    List<YAxis> yAxisList = new ArrayList<>();
                    for (Object obj : o) {
                        if (obj instanceof String objStr) { // 支持yAxis: ["name1", "name2"]的写法，此时等价于
                                                            // yAxis: [{name: "name1", lines: ["name1"]}, {name: "name2", lines: ["name2"]}]
                            YAxis y = new YAxis();
                            y.setName(objStr);
                            y.setLines(ListUtils.of(objStr));
                            yAxisList.add(y);
                            continue;
                        }

                        if (!(obj instanceof Map)) {
                            log.error("yAxis config must be map");
                            continue;
                        }
                        YAxis y = new YAxis();
                        Map<String, Object> map = (Map<String, Object>) obj;
                        y.setName(toString(map.get("name")));
                        Object lines = map.get("lines");
                        if (lines instanceof List) {
                            y.setLines(ListUtils.transform((List<?>) lines, Object::toString));
                        } else if (lines instanceof String linesStr) {
                            y.setLines(ListUtils.of(linesStr));
                        } else if (lines == null) {
                            y.setLines(ListUtils.of(y.getName())); // lines为null时，lines默认为name
                        } else {
                            log.error("yAxis lines config must be list or string");
                            continue;
                        }
                        yAxisList.add(y);
                    }
                    return yAxisList;
        });
        createLineDTO.setYAxis(yAxis);

        createLineDTO.setSmooth(ChartCommonUtils.getConfig(req.getConfigs(), "smooth", createLineDTO.getSmooth(),
                ChartCommonUtils::parseBoolean));
        createLineDTO.setShowZoom(ChartCommonUtils.getConfig(req.getConfigs(), "showZoom", createLineDTO.getShowZoom(),
                ChartCommonUtils::parseBoolean));

        // connectNulls支持2种名称
        Boolean connectNulls = ChartCommonUtils.getConfig(req.getConfigs(), "connectNulls", createLineDTO.getConnectNulls(),
                ChartCommonUtils::parseBoolean);
        Boolean connectNull = ChartCommonUtils.getConfig(req.getConfigs(), "connectNull", createLineDTO.getConnectNulls(),
                ChartCommonUtils::parseBoolean);
        createLineDTO.setConnectNulls(connectNulls != null && connectNulls || connectNull != null && connectNull);

        createLineDTO.setTitle(req.getName() == null ? "" : req.getName());
        return createLineDTO;
    }

    private static String toString(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

}
