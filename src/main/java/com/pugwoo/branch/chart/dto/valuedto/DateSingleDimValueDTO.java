package com.pugwoo.branch.chart.dto.valuedto;

import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 这是一个非常通用的画图数据结构，适用于折线图、堆叠图、柱状图等。
 * 它只有一个维度（多维度数据也可以聚合成单维度）。
 */
@Data
public class DateSingleDimValueDTO {

    /**表示时间，可以是日期、时间等，会智能排序，必须，扩展：数值也是支持的*/
    private String date;

    /**维度值，必须*/
    private String dim;

    /**数值，必须*/
    private BigDecimal value;

    /**
     * 找出所有的dim，保持按提供的数据的顺序
     */
    public static List<String> getDims(List<DateSingleDimValueDTO> data) {
        if (data == null) {
            return new ArrayList<>();
        }
        List<String> dimList = new ArrayList<>();
        Set<String> dimSet = new HashSet<>();
        for (DateSingleDimValueDTO d : data) {
            if (!dimSet.contains(d.getDim())) {
                dimList.add(d.getDim());
                dimSet.add(d.getDim());
            }
        }
        return dimList;
    }

    public static List<String> getDatesSortAsc(List<DateSingleDimValueDTO> data) {
        Set<String> datesSet = new HashSet<>();
        List<String> datesList = new ArrayList<>();
        for (DateSingleDimValueDTO d : data) {
            if (!datesSet.add(d.getDate())) {
                continue;
            }
            datesList.add(d.getDate());
        }
        ChartCommonUtils.smartSortByDate(datesList);
        return datesList;
    }

    /**
     * 判断时间是否为数值，如果是数值，则返回true
     * 这里有一种特殊情况要处理，就是yyyyMMdd这种日期格式，虽然全是数据，但应该按日期处理
     */
    public static boolean isDatesAsNumber(List<DateSingleDimValueDTO> data) {
        String regex = "^[+-]?(\\d+(\\.\\d+)?|\\.\\d+)$";
        for (DateSingleDimValueDTO d : data) {
            if (d.getDate() != null && !d.getDate().matches(regex)) {
                return false;
            }
        }

        // 测试下所有的数字是否都可以转成日期，如果可以，则认为是日期
        boolean isAllDate = true;
        for (DateSingleDimValueDTO d : data) {
            if (StringTools.isBlank(d.getDate())) {
                continue;
            }
            if (DateUtils.parseLocalDateTime(d.getDate()) == null) {
                isAllDate = false;
                break;
            }
        }
        if (isAllDate) {
            return false;
        }

        return true;
    }

}
