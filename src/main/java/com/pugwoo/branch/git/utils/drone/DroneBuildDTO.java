package com.pugwoo.branch.git.utils.drone;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DroneBuildDTO {

    /**
     * 状态
     */
    private String status;

    /**
     * 错误消息
     */
    private String error;

    /**
     * 事件
     */
    private String event;

    /**
     * 分支
     */
    private String branch;

    /**
     * commit
     */
    private String commit;

    /**创建时间戳*/
    @JsonProperty("created_at")
    private Long createdAt;

}
