package com.pugwoo.branch.git.utils.drone;

import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DroneUtils {

    // 简单的缓存，写死超时时间为1小时，一般这个token很稳定，长达几个月都是不变的
    private static Map<String, CacheToken> tokenCache = new HashMap<>();

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    private static class CacheToken {
        private long timestamp;
        private String token;
    }

    /**
     * 登录drone并获得token
     * @param droneBaseUrl drone的地址，例如http://drone.abc.com，不要加/
     * @param username
     * @param password
     * @return
     */
    public static String getToken(String droneBaseUrl, String username, String password) {
        if(StringTools.isBlank(droneBaseUrl) || StringTools.isBlank(username) || StringTools.isBlank(password)) {
            return "";
        }
        if(droneBaseUrl.endsWith("/")) {
            droneBaseUrl = droneBaseUrl.substring(0, droneBaseUrl.length() - 1);
        }

        // 缓存
        String key = droneBaseUrl + "@" + username + "@" + password;
        CacheToken cacheToken = tokenCache.get(key);
        if(cacheToken != null && cacheToken.getTimestamp() < (System.currentTimeMillis() + 3600 * 1000)) { // 1小时有效
            return cacheToken.getToken();
        }

        Browser browser = new Browser();
        browser.disableRedirect();

        try {
            String loginUrl = droneBaseUrl + "/authorize";
            HttpResponse resp = browser.post(loginUrl,
                    MapUtils.of("username", username,
                    "password", password));

            HttpResponse resp2 = browser.get(droneBaseUrl + "/account/token");
            String csrf = RegexUtils.getFirstMatchStr(resp2.getContentString(), "window.DRONE_CSRF = \"(.*)\"");
            browser.addRequestHeader("X-CSRF-TOKEN", csrf);

            HttpResponse resp3 = browser.post(droneBaseUrl + "/api/user/token", new HashMap<>());
            String token = resp3.getContentString();
            if (StringTools.isBlank(token)) {
                throw new Exception("get token fail");
            }

            tokenCache.put(key, new CacheToken(System.currentTimeMillis(), token));
            return token;
        } catch (Exception e) {
            log.error("drone getToken fail, drone baseUrl:{}, username:{}, password:{}, msg:{}",
                    droneBaseUrl, username, password, e.getMessage());
            // 如果缓存中有，那还是返回缓存的
            if (cacheToken != null) {
                return cacheToken.getToken();
            }
            return "";
        }
    }

    public static List<DroneBuildDTO> getDroneBuildList(String repos, String droneBaseUrl, String droneToken) {
        if(droneBaseUrl.endsWith("/")) {
            droneBaseUrl = droneBaseUrl.substring(0, droneBaseUrl.length() - 1);
        }

        List<DroneBuildDTO> result = new ArrayList<>();
        Browser browser = new Browser();
        browser.addRequestHeader("Authorization", "Bearer " + droneToken);
        try {
            HttpResponse httpResponse = browser.get(droneBaseUrl + "/api/repos/" + repos + "/builds");
            result = JSON.parse(httpResponse.getContentString(), List.class, DroneBuildDTO.class);
        } catch (IOException e) {
            log.error("get repo:{} build list fail, msg:{}", repos, e.getMessage()); // 忽略异常堆栈，减少log数量
        }
        return result;
    }

    /**
     * 获得当前用户可以看到的所有仓库地址，仓库地址一般以.git结尾
     * @param droneBaseUrl
     * @param droneToken
     * @return
     */
    public static List<DroneRepoDTO> getAllRepoCloneUrlList(String droneBaseUrl, String droneToken) {
        if(droneBaseUrl.endsWith("/")) {
            droneBaseUrl = droneBaseUrl.substring(0, droneBaseUrl.length() - 1);
        }

        List<DroneRepoDTO> result = new ArrayList<>();
        Browser browser = new Browser();
        browser.addRequestHeader("Authorization", "Bearer " + droneToken);
        try {
            HttpResponse resp = browser.get(droneBaseUrl + "/api/user/repos?all=true&flush=true");
            result = JSON.parse(resp.getContentString(), List.class, DroneRepoDTO.class);
        } catch (IOException e) {
            log.error("get drone:{} repo list fail, msg:{}", droneBaseUrl, e.getMessage()); // 忽略异常堆栈，减少log数量
        }

        return result;
    }

    /**
     * 获得git的url的路径，
     * 例如输入 http://git.oa.com/abc/hello.git
     * 输出 abc/hello
     *
     * @param url
     * @return
     */
    public static String getGitUrlPath(String url) {
        if(url == null) {return "";}

        if(url.toLowerCase().startsWith("http://")) {
            url = url.substring(7); // 去掉http://
        } else if(url.toLowerCase().startsWith("https://")) {
            url = url.substring(8); // 去掉https://
        }

        // 去掉域名端口部分
        int index = url.indexOf("/");
        if(index >= 0) {
            url = url.substring(index + 1);
        }

        // 去掉尾部 .git
        if(url.toLowerCase().endsWith(".git")) {
            url = url.substring(0, url.length() - 4);
        }

        return url;
    }

}
