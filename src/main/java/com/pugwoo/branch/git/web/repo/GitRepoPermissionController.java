package com.pugwoo.branch.git.web.repo;

import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.branch.git.service.IGitPermissionService;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NoPermission;
import com.pugwoo.admin.utils.WebCheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限相关
 */
@RequestMapping("/git_repo_permission")
@RestController
public class GitRepoPermissionController {

    @Autowired
    private IGitPermissionService gitPermissionService;

    /**刷新个人的权限*/
    @NoPermission
    @PostMapping("/do_refresh")
    public WebJsonBean doRefresh(String droneBaseUrl, String username, String password) {
        WebCheckUtils.assertNotBlank(droneBaseUrl, "必须选择drone地址");
        WebCheckUtils.assertNotBlank(username, "必须填写用户名");
        WebCheckUtils.assertNotBlank(password, "必须填写密码");

        Long userId = AdminUserLoginInterceptor.getAdminUserLoginContext().getUserId();
        gitPermissionService.addRepo(droneBaseUrl, username, password, userId);

        return WebJsonBean.ok();
    }

}
