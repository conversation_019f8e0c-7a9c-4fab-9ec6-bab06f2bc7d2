package com.pugwoo.branch.git.web.repo;

import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.branch.git.service.IJGitService;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 和api部署相关的接口
 */
@Slf4j
@Permission(value = "ReadGit", name = "Git查看列表")
@RestController
@RequestMapping("/git_repository")
public class GitReleaseApiController {

    @Autowired
    private IGitRepositoryService repositoryService;
    @Autowired
    private IJGitService jGitService;

    /**查询分支对应的api版本*/
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @GetMapping("get_branch_api_ver")
    public WebJsonBean queryReleaseBranchSnapshotApiVersion(Long id, String name) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(name, "缺少分支名称");

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        Map<String, Object> map = new HashMap<>();
        map.put("apiPomLocation", gitRepositoryDO.getApiPomLocation());
        boolean isConf = StringTools.isNotBlank(gitRepositoryDO.getApiPomLocation());
        map.put("isConfApiPomLocation", isConf);
        if (isConf) {
            String mavenVersion = jGitService.getBranchSnapshotApiVersion(gitRepositoryDO, name);
            map.put("mavenVersion", mavenVersion);
            map.put("isSnapshot", mavenVersion != null && mavenVersion.endsWith("-SNAPSHOT"));
        }

        return WebJsonBean.ok(map);
    }

    /**发布snapshot版本的api*/
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "ReleaseSnapshotApi", name = "Git发布分支SnapshotAPI")
    @RequestMapping("release_branch_snapshot_api")
    public WebJsonBean releaseBranchSnapshotApi(Long id, String name) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(name, "缺少分支名称");

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        WebCheckUtils.assertNotBlank(gitRepositoryDO.getApiPomLocation(), "此仓库尚未配置api pom.xml文件位置，不允许发布API");

        return WebJsonBean.ok(jGitService.releaseBranchSnapshotApi(gitRepositoryDO, name));
    }

    /**查询master分支的api版本*/
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @GetMapping("/query_release_api")
    public WebJsonBean queryReleaseApi(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        Map<String, Object> map = new HashMap<>();
        map.put("apiPomLocation", gitRepositoryDO.getApiPomLocation());
        boolean isConf = StringTools.isNotBlank(gitRepositoryDO.getApiPomLocation());
        map.put("isConfApiPomLocation", isConf);
        if (isConf) {
            String mavenVersion = jGitService.getReleaseApiVersion(gitRepositoryDO);
            map.put("mavenVersion", mavenVersion);
            map.put("isRelease", mavenVersion != null && !mavenVersion.endsWith("-SNAPSHOT"));
        }

        return WebJsonBean.ok(map);
    }

    /**发布正式版本的api*/
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "ReleaseApi", name = "Git发布API")
    @RequestMapping("release_api")
    public WebJsonBean releaseApi(Long id) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        String mavenVersion = jGitService.getReleaseApiVersion(gitRepositoryDO);
        return WebJsonBean.ok(jGitService.releaseApi(gitRepositoryDO, mavenVersion));
    }
}
