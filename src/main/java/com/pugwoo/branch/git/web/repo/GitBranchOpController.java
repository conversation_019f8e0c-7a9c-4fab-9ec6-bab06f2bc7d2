package com.pugwoo.branch.git.web.repo;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.service.GitBranchService;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.branch.git.service.IJGitService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分支新增、复制、删除操作
 */
@Slf4j
@Permission(value = "ReadGit", name = "Git查看列表")
@RestController
@RequestMapping("/git_repository")
public class GitBranchOpController {

    @Autowired
    private IGitRepositoryService repositoryService;
    @Autowired
    private IJGitService jGitService;
    @Resource
    private GitBranchService gitBranchService;

    /**当srcBranchName不为空时，为复制分支*/
    @Permission(value = "AddGitBranch", name = "Git新增分支")
    @RequestMapping("add_branch")
    public WebJsonBean addMerge(Long id, String name, String srcBranchName) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(name, "缺少分支名称");
        if (!name.startsWith("develop-")) {
            name = "develop-" + name;
        }

        if (StringTools.isNotBlank(srcBranchName) && !srcBranchName.startsWith("develop-")) {
            srcBranchName = "develop-" + srcBranchName;
        }

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        if(StringTools.isBlank(srcBranchName)) {
            return WebJsonBean.ok(jGitService.addBranch(gitRepositoryDO, name));
        } else {
            return WebJsonBean.ok(jGitService.copyBranch(gitRepositoryDO, srcBranchName, name));
        }
    }

    /**删除分支以异步的方式进行，所以这里最长等待10分钟*/
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 600000)
    @Permission(value = "DeleteBranch", name = "Git删除分支")
    @RequestMapping("delete_branch")
    public WebJsonBean deleteBranch(Long id, String name) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(name, "缺少分支名称");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        return WebJsonBean.ok(jGitService.deleteBranch(gitRepositoryDO, name));
    }

    // @Synchronized(namespace = "GitWebOp", keyScript = "args[0]") // 说明：这个下沉到jGitService.developBranchMergeMaster
    @Permission(value = "AddGitBranch", name = "开发分支合入master")
    @RequestMapping("develop_branch_merge_master")
    public WebJsonBean developBranchMergeMaster(Long id, String name) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(name, "缺少分支名称");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        return WebJsonBean.ok(jGitService.developBranchMergeMaster(gitRepositoryDO, name));
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "AddGitBranch", name = "开发分支合入其它分支")
    @RequestMapping("develop_branch_merge_other_branch")
    public WebJsonBean developBranchMergeOtherBranch(Long id, String srcBranchName,
                                                     @JsonParam("otherBranches") List<String> otherBranch) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(srcBranchName, "缺少分支名称");
        if (ListUtils.isEmpty(otherBranch)) {
            throw new IllegalArgumentException("缺少合入分支名称");
        }

        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        return WebJsonBean.ok(jGitService.developBranchMergeOtherBranch(gitRepositoryDO, srcBranchName, otherBranch));
    }
    
    /**
     * 回退一个提交版本
     * @param id             仓库id
     * @param branchName  分支名
     * @return true成功
     */
    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @Permission(value = "ResetBranch", name = "回退分支版本")
    @PostMapping("reset_branch_undo_one_commit")
    public WebJsonBean resetBranchUndoCommit1(Long id, String branchName) throws Exception {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(branchName, "缺少分支名称");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        return WebJsonBean.ok(jGitService.resetCommit(gitRepositoryDO, branchName, 1));
    }

    @PostMapping("switch_auto_merge_master")
    @Permission(value = "SwitchAutoMergeMaster", name = "开启关闭分支自动合并master")
    public WebJsonBean<?> switchAutoMergeMaster(Long id, String branchName, Boolean enable) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        WebCheckUtils.assertNotBlank(branchName, "缺少分支名称");
        WebCheckUtils.assertNotNull(enable, "缺少参数enable");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(id);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");
        gitBranchService.switchAutoMergeMaster(gitRepositoryDO, branchName, enable);
        return WebJsonBean.ok();
    }
    
}
