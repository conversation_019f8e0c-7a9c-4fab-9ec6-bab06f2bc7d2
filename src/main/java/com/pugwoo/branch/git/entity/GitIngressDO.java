package com.pugwoo.branch.git.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("git_ingress")
public class GitIngressDO extends AdminBaseDO {

    /** 仓库id<br>Column: [repository_id] */
    @Column(value = "repository_id")
    private Long repositoryId;

    /** 类型：DEVELOP，TEST<br>Column: [type] */
    @Column(value = "type")
    private String type;

    /** 版本名称<br>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 提交commit id<br>Column: [commit_id] */
    @Column(value = "commit_id")
    private String commitId;

    /** 当时的master commit id<br>Column: [master_commit_id] */
    @Column(value = "master_commit_id")
    private String masterCommitId;

    /** 编译状态：NEW，RUNNING,SUCCESS */
    @Column(value = "build_status")
    private String buildStatus;

}