package com.pugwoo.branch.git.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("git_branch_config")
public class GitBranchConfigDO extends AdminCoreDO {

    /** git_repository的id<br/>Column: [repo_id] */
    @Column(value = "repo_id")
    private Long repoId;

    /** 分支名称<br/>Column: [branch_name] */
    @Column(value = "branch_name")
    private String branchName;

    /** 是否自动合并master<br/>Column: [is_auto_merge_master] */
    @Column(value = "is_auto_merge_master")
    private Boolean isAutoMergeMaster;

    /** 最后一次合并是否失败<br/>Column: [is_merge_master_fail] */
    @Column(value = "is_merge_master_fail")
    private Boolean isMergeMasterFail;

    /** 最后一次合并失败信息<br/>Column: [merge_master_fail_msg] */
    @Column(value = "merge_master_fail_msg")
    private String mergeMasterFailMsg;

    /** 是否自动合并远程分支<br/>Column: [is_auto_merge_remote] */
    @Column(value = "is_auto_merge_remote")
    private Boolean isAutoMergeRemote;

    /** 给remote命名<br/>Column: [remote_name] */
    @Column(value = "remote_name")
    private String remoteName;

    /** 自动合并的远程url<br/>Column: [remote_url] */
    @Column(value = "remote_url")
    private String remoteUrl;

    @Column(value = "remote_url_username")
    private String remoteUrlUsername;

    @Column(value = "remote_url_password")
    private String remoteUrlPassword;

    /** 要合并的远程url的分支<br/>Column: [remote_url_branch] */
    @Column(value = "remote_url_branch")
    private String remoteUrlBranch;

    /** 合并remote是否失败<br/>Column: [is_merge_remote_fail] */
    @Column(value = "is_merge_remote_fail")
    private Boolean isMergeRemoteFail;

    /** 合并remote失败信息<br/>Column: [merge_remote_fail_msg] */
    @Column(value = "merge_remote_fail_msg")
    private String mergeRemoteFailMsg;

}