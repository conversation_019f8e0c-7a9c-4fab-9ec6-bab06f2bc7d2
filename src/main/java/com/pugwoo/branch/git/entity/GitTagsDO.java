package com.pugwoo.branch.git.entity;


import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Table("git_tags")
@Data
public class GitTagsDO extends AdminBaseDO {

    /** 仓库id<br>Column: [repository_id] */
    @Column(value = "repository_id")
    private Long repositoryId;

    /** tag名称<br>Column: [tag_name] */
    @Column(value = "tag_name")
    private String tagName;

    /** 最新发布记录id<br>Column: [ingress_id] */
    @Column(value = "ingress_id")
    private Long ingressId;

    /** 是否合并到master（是：YES，否：NO）<br>Column: [is_merge_to_master] */
    @Column(value = "is_merge_to_master")
    private String isMergeToMaster;

    /** 是否锁定 <br>Column: [is_lock] */
    @Column(value = "is_lock")
    private Boolean isLock;

}