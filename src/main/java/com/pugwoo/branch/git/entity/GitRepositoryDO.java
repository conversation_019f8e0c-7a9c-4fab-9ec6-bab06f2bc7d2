package com.pugwoo.branch.git.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("git_repository")
public class GitRepositoryDO extends AdminBaseDO {

    /** 仓库名称<br>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 仓库地址<br>Column: [url] */
    @Column(value = "url")
    private String url;

    /** 用户名<br>Column: [username] */
    @Column(value = "username")
    private String username;

    /** 用户密码<br>Column: [password] */
    @Column(value = "password")
    private String password;

    /** 排序 */
    @Column(value = "seq")
    private Integer seq;

    /**最后发布开发的ingress id*/
    @Column("last_develop_ingress_id")
    private Long lastDevelopIngressId;

    /**drone基础url，例如http://drone.abc.com*/
    @Column(value = "drone_base_url")
    private String droneBaseUrl;

    /**api pom的位置*/
    @Column("api_pom_location")
    private String apiPomLocation;

}