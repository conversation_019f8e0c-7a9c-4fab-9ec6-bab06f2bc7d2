package com.pugwoo.branch.git.config;

public interface Constants {

    /**仓库存在本地的位置*/
    String LOCAL_PATH = "/tmp/repos/";
    /**本地仓库文件复制出来的路径*/
    String LOCAL_COPY_PATH = "/tmp/repos_copy/";

    /**新合并的临时release分支*/
    String TAG_TEMP_NEW_RELEASE = "temp-release-tag-for-compare";

    /**开发tag*/
    String DEVELOP = "develop";
    /**转测tag*/
    String RELEASE = "release-";
    /**tag:发布api*/
    String TAG_API = "api-";

    /**全局：redis前缀*/
    String REDIS_NAME_SPACE = "itil:";

    String SEARCH = "search";
}
