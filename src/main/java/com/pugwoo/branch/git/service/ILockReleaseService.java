package com.pugwoo.branch.git.service;

import java.util.List;
import java.util.Map;

public interface ILockReleaseService {

    /**
     * 锁住指定的repo的release版本
     * @param repoId
     * @param releaseTag
     */
    void lock(Long repoId, String releaseTag);

    /**
     * 取消锁定指定的repo的release版本
     * @param repoId
     * @param releaseTag
     */
    void unlock(Long repoId, String releaseTag);

    /**
     * 查询是否已锁定
     * @param repoId
     * @param releaseTag
     * @return
     */
    boolean isLocked(Long repoId, String releaseTag);

    /**
     * 查询指定的tag对应的锁定状态
     * @param repoId
     * @param releaseTags
     * @return 返回可以进行锁开关状态还有releaseTag的锁定状态，如果不存在，则说明不能对其进行加锁或解锁
     */
    Map<String, Boolean> queryLocks(Long repoId, List<String> releaseTags);

}
