package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.branch.git.enums.YesOrNoEnum;
import com.pugwoo.branch.git.service.IGitTagsService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GitTagsServiceImpl implements IGitTagsService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public GitTagsDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return dbHelper.getByKey(GitTagsDO.class, id);
    }

    @Override
    public PageData<GitTagsDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(GitTagsDO.class, page, pageSize);
    }

    @Override
    public GitTagsDO insertOrUpdate(GitTagsDO gitTagsDO) {
        String sql = "where repository_id = ? and tag_name = ? ";
        List<Object> params = new ArrayList<>();
        params.add(gitTagsDO.getRepositoryId());
        params.add(gitTagsDO.getTagName());
        GitTagsDO one = dbHelper.getOne(GitTagsDO.class, sql, params.toArray());
        if (one == null) {
            dbHelper.insert(gitTagsDO);
        } else {
            gitTagsDO.setId(one.getId());
            dbHelper.update(gitTagsDO);
        }
        return gitTagsDO;
    }

    @Override
    public List<GitTagsDO> getMergeList(Long repositoryId) {
        String sql = "where is_merge_to_master = ? and repository_id = ? ";
        List<Object> params = new ArrayList<>();
        params.add(YesOrNoEnum.YES.getCode());
        params.add(repositoryId);
        return dbHelper.getAll(GitTagsDO.class, sql, params.toArray());
    }

}