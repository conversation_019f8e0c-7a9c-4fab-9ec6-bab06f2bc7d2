package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.config.Constants;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.enums.ItilErrorCode;
import com.pugwoo.branch.git.model.GitUserPasswordDTO;
import com.pugwoo.branch.git.service.IGitCloneService;
import com.pugwoo.branch.git.utils.GitUtils;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

@Slf4j
@Service
public class GitCloneServiceImpl implements IGitCloneService {

    // 这里用@Synchronized是想靠仓库id来控制，synchronized关键字不支持
    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 60000)
    @Override
    public void cloneNew(GitRepositoryDO gitRepositoryDO) throws Exception {
        GitUserPasswordDTO gitUserPasswordDTO = from(gitRepositoryDO);
        String localPath = Constants.LOCAL_PATH + gitRepositoryDO.getName();
        try {
            boolean success = GitUtils.cloneNew(gitRepositoryDO.getUrl(), localPath, gitUserPasswordDTO);
            if(!success) {
                throw new AdminInnerException(ItilErrorCode.PULL_ERROR);
            }
        } catch (Exception e) {
            // 如果拉取抛出了无法处理的异常，则删除目录之后再次拉取试试，尝试修复因磁盘或文件损坏的问题
            log.error("cloneNew error, url:{}, delete localPath and try again", gitRepositoryDO.getUrl(), e);
            FileUtils.deleteDirectory(new File(localPath));
            boolean success = GitUtils.cloneNew(gitRepositoryDO.getUrl(), localPath, gitUserPasswordDTO);
            if(!success) {
                throw new AdminInnerException(ItilErrorCode.PULL_ERROR);
            }
        }
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 60000)
    @Override
    public String copyRepo(GitRepositoryDO gitRepositoryDO) throws Exception {
        cloneNew(gitRepositoryDO);
        String localPath = Constants.LOCAL_PATH + gitRepositoryDO.getName();
        String copyPath = Constants.LOCAL_COPY_PATH +
                UUID.randomUUID().toString().replace("-", "") +
                gitRepositoryDO.getName();

        copyWithoutDotGit(localPath, copyPath);
        return copyPath;
    }

    private void copyWithoutDotGit(String localPath, String copyPath) throws IOException {
        File local = new File(localPath);
        File[] files = local.listFiles();
        for (File f : files) {
            if(!f.getName().equals(".git")) {
                File newFile = new File(copyPath + "/" + f.getName());
                if(f.isDirectory()) {
                    FileUtils.copyDirectory(f, newFile);
                } else {
                    FileUtils.copyFile(f, newFile);
                }
            }
        }
    }

    private GitUserPasswordDTO from(GitRepositoryDO gitRepositoryDO) {
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername(gitRepositoryDO.getUsername());
        gitUserPasswordDTO.setPassword(gitRepositoryDO.getPassword());
        return gitUserPasswordDTO;
    }
}
