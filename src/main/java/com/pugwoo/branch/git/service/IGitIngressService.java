package com.pugwoo.branch.git.service;

import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.vo.GitIngressVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;
import java.util.Map;

public interface IGitIngressService {
    /**
     * 通过主键获得数据
     */
    GitIngressDO getById(Long id);

    /**
     * 获得分页数据
     *
     * @param page     页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<GitIngressDO> getPage(int page, int pageSize);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    GitIngressDO insert(GitIngressDO gitIngressDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 根据名称获取最新的发布版本
     */
    GitIngressDO getLastReleaseByName(Long repositoryId, String name);

    /**
     * 查询带有分支列表的分页数据
     */
    PageData<GitIngressVO> getPageWith(Long repositoryId, List<IngressTypeEnum> typeEnums, int page, int pageSize);

    /**
     * 查询最新发布至开发环境的数据
     */
    GitIngressVO getLastOne(Long repositoryId, IngressTypeEnum typeEnum);

    /**
     * 查询仓库指定的tag对应的分支
     */
    Map<String, GitIngressVO> getTagBranches(Long repositoryId, List<String> tagNames);

}