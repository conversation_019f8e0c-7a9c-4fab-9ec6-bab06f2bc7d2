package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.code_scan.entity.GitCodeCheckRuleDO;
import com.pugwoo.branch.code_scan.entity.GitCodeCheckRuleDetailDO;
import com.pugwoo.branch.code_scan.model.GitCodeCheckRuleBO;
import com.pugwoo.branch.git.enums.ItilErrorCode;
import com.pugwoo.branch.git.model.CheckRuleCreateDTO;
import com.pugwoo.branch.git.model.CheckRuleDetailCreateDTO;
import com.pugwoo.branch.git.service.ICheckRuleService;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CheckRuleServiceImpl implements ICheckRuleService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    public void add(CheckRuleCreateDTO gitCheckCreateDTO) {
        GitCodeCheckRuleDO gitGitCodeCheckRuleDO = new GitCodeCheckRuleDO();
        gitGitCodeCheckRuleDO.setName(gitCheckCreateDTO.getName());
        if (dbHelper.insert(gitGitCodeCheckRuleDO) != 1) {
            throw new AdminInnerException(ItilErrorCode.CHECK_RULE_CREATE_ERROR);
        }
        List<GitCodeCheckRuleDetailDO> gitGitCodeCheckRuleDetailDOList = new ArrayList<>();
        for (CheckRuleDetailCreateDTO gitCheckRuleDetailCreateDTO : gitCheckCreateDTO.getCheckDetailCreateDTOList()) {
            GitCodeCheckRuleDetailDO gitGitCodeCheckRuleDetailDO = new GitCodeCheckRuleDetailDO();
            gitGitCodeCheckRuleDetailDO.setRuleId(gitGitCodeCheckRuleDO.getId());
            gitGitCodeCheckRuleDetailDO.setContentRegex(gitCheckRuleDetailCreateDTO.getContent());
            gitGitCodeCheckRuleDetailDO.setType(gitCheckRuleDetailCreateDTO.getType());
            gitGitCodeCheckRuleDetailDO.setLevel(gitCheckRuleDetailCreateDTO.getLevel());
            gitGitCodeCheckRuleDetailDOList.add(gitGitCodeCheckRuleDetailDO);
        }
        if (dbHelper.insert(gitGitCodeCheckRuleDetailDOList) < 1) {
            throw new AdminInnerException(ItilErrorCode.CHECK_RULE_CREATE_ERROR);
        }
    }

    @Override
    public PageData<GitCodeCheckRuleBO> getPage(String name, int page, int pageSize) {
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        if (StringTools.isNotEmpty(name)) {
            sql.append(" where name like ?");
            params.add("%" + name + "%");
        }
        return dbHelper.getPage(GitCodeCheckRuleBO.class, page, pageSize, sql.toString(), params.toArray());
    }

    @Override
    public GitCodeCheckRuleBO get(Long id) {
        return dbHelper.getOne(GitCodeCheckRuleBO.class, " where id = ?", id);
    }


}