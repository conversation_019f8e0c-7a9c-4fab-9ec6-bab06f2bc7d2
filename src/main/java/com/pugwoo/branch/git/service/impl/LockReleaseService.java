package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.service.ILockReleaseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LockReleaseService implements ILockReleaseService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public void lock(Long repoId, String releaseTag) {
        if (repoId == null || StringTools.isBlank(releaseTag)) {
            log.error("lock release with wrong param, repoId:{}, releaseTag:{}", repoId, releaseTag);
            return;
        }

        List<GitTagsDO> tagDO = dbHelper.getAll(GitTagsDO.class,
                "where repository_id=? and tag_name=? and is_merge_to_master='NO'",
                repoId, releaseTag);
        if (tagDO != null) {
            for (GitTagsDO tag : tagDO) {
                tag.setIsLock(true);
            }
            dbHelper.update(tagDO); // 批量更新
        }

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(repoId);
        gitIngressDO.setType(IngressTypeEnum.TEST_LOCK.getCode());
        gitIngressDO.setName(releaseTag);
        dbHelper.insert(gitIngressDO);

    }

    @Override
    public void unlock(Long repoId, String releaseTag) {
        if (repoId == null || StringTools.isBlank(releaseTag)) {
            log.error("lock release with wrong param, repoId:{}, releaseTag:{}", repoId, releaseTag);
            return;
        }

        List<GitTagsDO> tagDO = dbHelper.getAll(GitTagsDO.class,
                "where repository_id=? and tag_name=? and is_merge_to_master='NO'",
                repoId, releaseTag);
        if (tagDO != null) {
            for (GitTagsDO tag : tagDO) {
                tag.setIsLock(false);
            }
            dbHelper.update(tagDO); // 批量更新
        }

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(repoId);
        gitIngressDO.setType(IngressTypeEnum.TEST_UNLOCK.getCode());
        gitIngressDO.setName(releaseTag);
        dbHelper.insert(gitIngressDO);
    }


    @Override
    public boolean isLocked(Long repoId, String releaseTag) {
        if (repoId == null || StringTools.isBlank(releaseTag)) {
            return false;
        }
        List<GitTagsDO> all = dbHelper.getAll(GitTagsDO.class,
                "where repository_id=? and tag_name=? and is_merge_to_master='NO'",
                repoId, releaseTag);
        // 只要有一个锁住，就是锁住的
        for (GitTagsDO tagsDO : all) {
            if (tagsDO.getIsLock() != null && tagsDO.getIsLock()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Boolean> queryLocks(Long repoId, List<String> releaseTags) {
        if (repoId == null || releaseTags == null || releaseTags.isEmpty()) {
            return new HashMap<>();
        }

        // 只要没有合并到master的，已经合并到master的就没有所谓锁不锁
        List<GitTagsDO> all = dbHelper.getAll(GitTagsDO.class,
                "where repository_id=? and tag_name in (?) and is_merge_to_master='NO'",
                repoId, releaseTags);

        Map<String, Boolean> result = new HashMap<>();
        for (GitTagsDO tagsDO : all) {
            result.put(tagsDO.getTagName(), tagsDO.getIsLock() == null ? false : tagsDO.getIsLock());
        }

        return result;
    }
}
