package com.pugwoo.branch.git.service;

import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.model.ConflictResultDTO;
import com.pugwoo.branch.git.model.GitBranchInfoDTO;

import java.util.List;

public interface IJGitService {

    /**
     * 创建新分支
     *
     * @param
     * @return
     */
    boolean addBranch(GitRepositoryDO gitRepositoryDO,
                      String branchName) throws Exception;

    /**
     * 复制分支
     */
    boolean copyBranch(GitRepositoryDO gitRepositoryDO,
                       String srcBranchName, String branchName) throws Exception;

    /**
     * 开发分支合入master
     */
    boolean developBranchMergeMaster(GitRepositoryDO gitRepositoryDO,
                                     String branchName) throws Exception;

    /**
     * 开发分支合入其它分支
     */
    boolean developBranchMergeOtherBranch(GitRepositoryDO gitRepositoryDO,
                                     String branchName, List<String> otherBranch) throws Exception;

    /**
     * 开发分支合入remote另外一个仓库的分支
     */
    boolean developBranchMergeRemote(GitRepositoryDO gitRepositoryDO,
                                     String branchName, GitBranchConfigDO remoteConfig) throws Exception;

    /**
     * 获取所有开发分支
     */
    List<String> getDevelopList(GitRepositoryDO gitRepositoryDO) throws Exception;

    /**
     * 获取远程所有开发分支，该接口用于查询页面和定时任务，因此有缓存
     */
    List<GitBranchInfoDTO> getDevelopListWithCommitId(GitRepositoryDO gitRepositoryDO, boolean showAllBranch) throws Exception;

    /**
     * 获取指定仓库上release-开头，但不包含release-api-的所有tag（不是存在数据库的）
     */
    List<String> getAllTagList(GitRepositoryDO gitRepositoryDO) throws Exception;

    /**
     * 获取所有tag，除了合并的
     *
     * @param
     * @return
     */
    List<String> getTagListWithoutMerged(GitRepositoryDO gitRepositoryDO) throws Exception;

    /**
     * 发布开发版本
     *
     * @param
     * @return
     */
    boolean deployDevelop(GitRepositoryDO gitRepositoryDO, List<String> develops) throws Exception;

    /**
     * 发布测试版本
     *
     * @param
     * @return 代码检测结果
     */
    CheckRuleLevelEnum developRelease(GitRepositoryDO gitRepositoryDO, List<String> develops,
                                      String tag) throws Exception;

    /**
     * 合并tag到master
     *
     * @param autoDeleteBranch 是否自动删除最后一次tag关联的分支
     * @return
     */
    boolean mergeToMaster(GitRepositoryDO gitRepositoryDO, String tag,
                          boolean autoDeleteBranch, boolean forceMerge) throws Exception;

    /**
     * 检测分支冲突
     */
    ConflictResultDTO checkConflict(GitRepositoryDO gitRepositoryDO, List<String> develops) throws Exception;

    /**
     * 临时比对测试，源分支为Constants.TAG_TEMP_RELEASE，新分支为Constants.TAG_TEMP_NEW_RELEASE
     */
    boolean compareRelease(GitRepositoryDO gitRepositoryDO, List<String> develops, String tag) throws Exception;

    /**
     * 删除分支
     *
     * @param
     * @return
     */
    boolean deleteBranch(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception;

    /**
     * 查询发布的snapshot分支的pom版本
     */
    String getBranchSnapshotApiVersion(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception;

    /**
     * 发布分支snapshot api
     */
    boolean releaseBranchSnapshotApi(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception;

    /**
     * 查询要发布的api的版本
     */
    String getReleaseApiVersion(GitRepositoryDO gitRepositoryDO) throws Exception;

    /**
     * 发布api，实际上就是打一个tag
     */
    boolean releaseApi(GitRepositoryDO gitRepositoryDO, String apiVersion) throws Exception;

    /**
     * 删除tag列表
     */
    boolean deleteTag(GitRepositoryDO gitRepositoryDO, List<String> tags) throws Exception;
    
    /**
     * 回退分支版本
     * @param gitRepositoryDO 仓库
     * @param branchName      分支名
     * @param i               回退多少个版本
     * @return true成功
     */
    boolean resetCommit(GitRepositoryDO gitRepositoryDO, String branchName, int i) throws Exception;
}
