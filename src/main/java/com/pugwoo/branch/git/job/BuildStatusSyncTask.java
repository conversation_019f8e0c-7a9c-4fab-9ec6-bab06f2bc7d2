package com.pugwoo.branch.git.job;

import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.enums.IngressBuildStatusEnum;
import com.pugwoo.branch.git.utils.drone.DroneBuildDTO;
import com.pugwoo.branch.git.utils.drone.DroneUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class BuildStatusSyncTask {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Value("${branch.mockCIDISync:false}")
    private Boolean mockCIDISync;

    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(fixedDelay = 3000) // 3秒钟一次
    public void updateBuildStatus() throws Exception {
        List<GitIngressDO> all = dbHelper.getAll(
                GitIngressDO.class, " where build_status in (?)",
                ListUtils.newArrayList(IngressBuildStatusEnum.NEW.getCode(),
                        IngressBuildStatusEnum.RUNNING.getCode()));

        // 对于超过24小时的，直接设置为超时
        List<GitIngressDO> over24 = ListUtils.filter(all, o -> isOver24Hour(o.getCreateTime()));
        for(GitIngressDO over : over24) {
            over.setBuildStatus(IngressBuildStatusEnum.QUERY_TIMEOUT.getCode());
        }
        dbHelper.update(over24);

        all = ListUtils.filter(all, o -> !isOver24Hour(o.getCreateTime()));

        List<Long> repoIds = ListUtils.transform(all, t -> t.getRepositoryId());
        if (repoIds.isEmpty()) {
            return;
        }

        // 如果是模拟CIDI联动，如果是未开始，则马上设置为已开始；如果是运行中，则等10秒后设置为成功
        if (mockCIDISync) {
            mockSync(all);
            return; // 以下不用再执行了
        }

        List<GitRepositoryDO> repositoryDOS = dbHelper.getAll(GitRepositoryDO.class,
                " where id in (?)", repoIds);
        //按照仓库为粒度进行处理
        for (GitRepositoryDO gitRepositoryDO : repositoryDOS) {

            // 尝试Drone同步状态
            if (StringTools.isNotBlank(gitRepositoryDO.getDroneBaseUrl())) {
                tryDroneSync(gitRepositoryDO, all);
            }

            // 尝试jenkins同步状态 TODO 待实现

        }
    }

    private IngressBuildStatusEnum ofDroneStatus(String droneStatus) {
        IngressBuildStatusEnum result = IngressBuildStatusEnum.SUCCESS;
        switch (droneStatus) {
            case "running":
                result = IngressBuildStatusEnum.RUNNING;
                break;
            case "success":
                result = IngressBuildStatusEnum.SUCCESS;
                break;
            case "failure":
                result = IngressBuildStatusEnum.FAILURE;
        }
        return result;
    }

    private boolean isOver24Hour(Date date) {
        long now = System.currentTimeMillis();
        return (now - date.getTime()) > 24 * 3600 * 1000;
    }

    /**模拟mock同步状态*/
    private void mockSync(List<GitIngressDO> all) {
        Set<Long> newToRunning = new HashSet<>();
        for (GitIngressDO gitIngressDO : all) {
            if (IngressBuildStatusEnum.NEW.getCode().equals(gitIngressDO.getBuildStatus())) {
                gitIngressDO.setBuildStatus(IngressBuildStatusEnum.RUNNING.getCode());
                dbHelper.update(gitIngressDO);
                newToRunning.add(gitIngressDO.getId());
            }
        }

        if (newToRunning.size() == all.size()) {
            return;
        }

        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            // ignore
        }

        for (GitIngressDO gitIngressDO : all) {
            if (!newToRunning.contains(gitIngressDO.getId()) &&
                    IngressBuildStatusEnum.RUNNING.getCode().equals(gitIngressDO.getBuildStatus())) {
                gitIngressDO.setBuildStatus(IngressBuildStatusEnum.SUCCESS.getCode());
                dbHelper.update(gitIngressDO);
            }
        }
    }

    /**尝试drone同步状态*/
    private void tryDroneSync(GitRepositoryDO gitRepositoryDO, List<GitIngressDO> all) {
        try {
            //查出仓库下的DroneList
            String droneToken = DroneUtils.getToken(gitRepositoryDO.getDroneBaseUrl(), gitRepositoryDO.getUsername(),
                    gitRepositoryDO.getPassword());
            String repo = DroneUtils.getGitUrlPath(gitRepositoryDO.getUrl());
            List<DroneBuildDTO> droneList = DroneUtils.getDroneBuildList(repo, gitRepositoryDO.getDroneBaseUrl(), droneToken);
            if (droneList == null || droneList.size() == 0) {
                return;
            }

            List<GitIngressDO> ingressDOs = ListUtils.filter(all, t -> t.getRepositoryId().equals(gitRepositoryDO.getId()));
            for (GitIngressDO gitIngressDO : ingressDOs) {
                // 这里要同时靠分支名称+commit id来判断才行，只靠commit id还不行
                // 2024年9月29日16:08:47 mark 由于移除了TAG-，之前的drone是靠分支来判断的，所以这里可能还有问题，等下次如果还有用drone再修复
                List<DroneBuildDTO> drones = ListUtils.filter(droneList,
                        o -> Objects.equals(o.getCommit(), gitIngressDO.getCommitId()) &&
                                Objects.equals(o.getBranch(), gitIngressDO.getName()));

                if(!drones.isEmpty()) {
                    ListUtils.sortDescNullLast(drones, o -> o.getCreatedAt()); // 按时间逆序
                    gitIngressDO.setBuildStatus(ofDroneStatus(drones.get(0).getStatus()).getCode());
                    dbHelper.update(gitIngressDO);
                }
            }
        } catch (Exception e) {
            log.error("sync drone status fail, repo:{}", gitRepositoryDO.getName(), e);
        }
    }

}
