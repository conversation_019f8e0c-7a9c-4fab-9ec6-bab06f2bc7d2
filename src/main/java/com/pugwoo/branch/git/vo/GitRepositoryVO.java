package com.pugwoo.branch.git.vo;

import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class GitRepositoryVO extends GitRepositoryDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "repository_id", extraWhere = "where is_merge_to_master='NO'")
    private List<GitTagsVO> gitTags;

    @RelatedColumn(localColumn = "last_develop_ingress_id", remoteColumn = "ingress_id")
    private List<GitIngressBranchDO> lastDevelopBranch;

    @RelatedColumn(localColumn = "last_develop_ingress_id", remoteColumn = "id")
    private GitIngressDO lastDevelopIngressDO; // 最后部署的开发ingress

    /**为了获得分支创建者名称和时间*/
    @RelatedColumn(localColumn = "id", remoteColumn = "repository_id",
            extraWhere = "where type='BRANCH_NEW' order by id desc")
    private List<GitIngressWithCreatorVO> createIngressLogs;

    @RelatedColumn(localColumn = "id", remoteColumn = "repo_id")
    private List<GitBranchConfigDO> gitBranchConfigs;

    public String getLastDevelopBuildStatus() {
        return lastDevelopIngressDO == null ? null : lastDevelopIngressDO.getBuildStatus();
    }

    public String getCreatorByBranch(String branchName) {
        if (createIngressLogs == null) {
            return "";
        }

        for (GitIngressWithCreatorVO branch : createIngressLogs) {
            if (branchName.equals(branch.getName())) {
                return branch.getCreateUserName();
            }
        }
        return "";
    }

}