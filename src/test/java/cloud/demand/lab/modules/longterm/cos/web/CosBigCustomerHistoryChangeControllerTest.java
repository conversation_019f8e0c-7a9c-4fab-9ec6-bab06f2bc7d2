package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.SaveBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CosBigCustomerHistoryChangeController 测试类
 */
class CosBigCustomerHistoryChangeControllerTest {

    @Mock
    private DBHelper cdLabDbHelper;

    @InjectMocks
    private CosBigCustomerHistoryChangeController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testQueryBigCustomerHistoryChange_Success() {
        // 准备测试数据
        QueryBigCustomerHistoryChangeReq req = new QueryBigCustomerHistoryChangeReq();
        req.setCategoryId(1L);
        req.setTaskId(0L);

        List<CosLongtermPredictInputBigCustomerChangeDO> mockData = createMockData();
        when(cdLabDbHelper.getAll(eq(CosLongtermPredictInputBigCustomerChangeDO.class), anyString(), any()))
                .thenReturn(mockData);

        // 执行测试
        QueryBigCustomerHistoryChangeResp resp = controller.queryBigCustomerHistoryChange(req);

        // 验证结果
        assertNotNull(resp);
        assertNotNull(resp.getDataList());
        assertEquals(2, resp.getDataList().size());
        verify(cdLabDbHelper, times(1)).getAll(eq(CosLongtermPredictInputBigCustomerChangeDO.class), anyString(), any());
    }

    @Test
    void testQueryBigCustomerHistoryChange_NullCategoryId() {
        // 准备测试数据
        QueryBigCustomerHistoryChangeReq req = new QueryBigCustomerHistoryChangeReq();
        req.setCategoryId(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            controller.queryBigCustomerHistoryChange(req);
        });

        assertEquals("方案id(categoryId)不能为空", exception.getMessage());
    }

    @Test
    void testSaveBigCustomerHistoryChange_Success() {
        // 准备测试数据
        SaveBigCustomerHistoryChangeReq req = new SaveBigCustomerHistoryChangeReq();
        req.setCategoryId(1L);
        req.setTaskId(100L);
        req.setDataList(createMockData());

        when(cdLabDbHelper.delete(eq(CosLongtermPredictInputBigCustomerChangeDO.class), anyString(), any(), any()))
                .thenReturn(2);
        when(cdLabDbHelper.insertBatchWithoutReturnId(anyList()))
                .thenReturn(2);

        // 执行测试
        String result = controller.saveBigCustomerHistoryChange(req);

        // 验证结果
        assertEquals("success", result);
        verify(cdLabDbHelper, times(1)).delete(eq(CosLongtermPredictInputBigCustomerChangeDO.class), anyString(), any(), any());
        verify(cdLabDbHelper, times(1)).insertBatchWithoutReturnId(anyList());
    }

    @Test
    void testSaveBigCustomerHistoryChange_NullCategoryId() {
        // 准备测试数据
        SaveBigCustomerHistoryChangeReq req = new SaveBigCustomerHistoryChangeReq();
        req.setCategoryId(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            controller.saveBigCustomerHistoryChange(req);
        });

        assertEquals("方案id(categoryId)不能为空", exception.getMessage());
    }

    private List<CosLongtermPredictInputBigCustomerChangeDO> createMockData() {
        List<CosLongtermPredictInputBigCustomerChangeDO> dataList = new ArrayList<>();

        CosLongtermPredictInputBigCustomerChangeDO data1 = new CosLongtermPredictInputBigCustomerChangeDO();
        data1.setId(1L);
        data1.setCategoryId(1L);
        data1.setTaskId(0L);
        data1.setCustomerName("快手");
        data1.setIsOutCustomer(true);
        data1.setStartDate(LocalDate.of(2024, 1, 1));
        data1.setEndDate(LocalDate.of(2024, 4, 30));
        data1.setNetChange(new BigDecimal("600"));
        dataList.add(data1);

        CosLongtermPredictInputBigCustomerChangeDO data2 = new CosLongtermPredictInputBigCustomerChangeDO();
        data2.setId(2L);
        data2.setCategoryId(1L);
        data2.setTaskId(0L);
        data2.setCustomerName("快手");
        data2.setIsOutCustomer(true);
        data2.setStartDate(LocalDate.of(2024, 4, 30));
        data2.setEndDate(LocalDate.of(2024, 12, 31));
        data2.setNetChange(new BigDecimal("600"));
        dataList.add(data2);

        return dataList;
    }
}
