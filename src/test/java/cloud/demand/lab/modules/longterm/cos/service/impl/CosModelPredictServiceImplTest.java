package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CosModelPredictServiceImplTest {

    @InjectMocks
    private CosModelPredictServiceImpl cosModelPredictService;

    @Test
    void testLinearFit() {
        // 准备测试数据
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();
        
        // 创建历史数据线
        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("内部");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());
        
        // 添加一些测试数据点（线性增长）
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        for (int i = 0; i < 10; i++) {
            LocalDate date = startDate.plusDays(i * 30); // 每30天一个点
            BigDecimal value = BigDecimal.valueOf(100 + i * 10); // 线性增长
            historyLine.getPoints().add(ListUtils.of(date, value));
        }
        
        lines.add(historyLine);
        
        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setLinearStartDate(LocalDate.of(2023, 1, 1));
        categoryConfig.setPredictEnd("2023-12");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "linearFit", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            method.invoke(cosModelPredictService, lines, categoryConfig);
            
            // 验证结果
            assertEquals(2, lines.size()); // 原来1个历史线 + 1个预测线
            
            QueryPredictResultResp.Line predictLine = lines.stream()
                    .filter(line -> "PREDICT".equals(line.getType()))
                    .findFirst()
                    .orElse(null);
            
            assertNotNull(predictLine);
            assertEquals("内部", predictLine.getScope());
            assertEquals("PREDICT", predictLine.getType());
            assertEquals("LINEAR_FIT", predictLine.getAlgorithm());
            assertNotNull(predictLine.getAlgorithmParams());
            
            // 验证算法参数
            assertTrue(predictLine.getAlgorithmParams().containsKey("a"));
            assertTrue(predictLine.getAlgorithmParams().containsKey("b"));
            assertTrue(predictLine.getAlgorithmParams().containsKey("r2"));
            
            // 验证预测点数量（从2023-01-01到2023-12-31）
            assertNotNull(predictLine.getPoints());
            assertTrue(predictLine.getPoints().size() > 0);
            
            // 验证r²值应该接近1（因为是完美的线性数据）
            BigDecimal r2 = (BigDecimal) predictLine.getAlgorithmParams().get("r2");
            assertTrue(r2.doubleValue() > 0.9, "R²值应该很高，因为测试数据是完美线性的");
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testLinearFitWithInsufficientData() {
        // 准备测试数据 - 只有一个数据点
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();
        
        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("内部");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());
        historyLine.getPoints().add(ListUtils.of(LocalDate.of(2023, 1, 1), BigDecimal.valueOf(100)));
        
        lines.add(historyLine);
        
        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setLinearStartDate(LocalDate.of(2023, 1, 1));
        categoryConfig.setPredictEnd("2023-12");
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "linearFit", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            method.invoke(cosModelPredictService, lines, categoryConfig);
            
            // 验证结果 - 应该只有原来的历史线，没有预测线
            assertEquals(1, lines.size());
            assertEquals("HISTORY", lines.get(0).getType());
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testLinearFitWithNullInput() {
        // 测试空输入
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "linearFit", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);
            
            // 测试null lines
            method.invoke(cosModelPredictService, null, new CosLongtermPredictCategoryConfigDO());
            
            // 测试null categoryConfig
            method.invoke(cosModelPredictService, new ArrayList<>(), null);
            
            // 如果没有抛出异常，测试通过
            assertTrue(true);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testLinearFitAccuracy() {
        // 准备测试数据 - 使用已知的线性关系 y = 2x + 50
        List<QueryPredictResultResp.Line> lines = new ArrayList<>();

        QueryPredictResultResp.Line historyLine = new QueryPredictResultResp.Line();
        historyLine.setScope("测试");
        historyLine.setType("HISTORY");
        historyLine.setPoints(new ArrayList<>());

        LocalDate startDate = LocalDate.of(2023, 1, 1);
        // 添加符合 y = 2x + 50 的数据点（x为天数索引）
        for (int i = 0; i < 5; i++) {
            LocalDate date = startDate.plusDays(i * 10);
            double expectedValue = 2.0 * (i * 10) + 50.0; // y = 2x + 50
            historyLine.getPoints().add(ListUtils.of(date, BigDecimal.valueOf(expectedValue)));
        }

        lines.add(historyLine);

        // 创建配置
        CosLongtermPredictCategoryConfigDO categoryConfig = new CosLongtermPredictCategoryConfigDO();
        categoryConfig.setLinearStartDate(startDate);
        categoryConfig.setPredictEnd("2023-02");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "linearFit", List.class, CosLongtermPredictCategoryConfigDO.class);
            method.setAccessible(true);

            method.invoke(cosModelPredictService, lines, categoryConfig);

            // 验证结果
            assertEquals(2, lines.size());

            QueryPredictResultResp.Line predictLine = lines.stream()
                    .filter(line -> "PREDICT".equals(line.getType()))
                    .findFirst()
                    .orElse(null);

            assertNotNull(predictLine);

            // 验证算法参数的准确性
            BigDecimal a = (BigDecimal) predictLine.getAlgorithmParams().get("a");
            BigDecimal b = (BigDecimal) predictLine.getAlgorithmParams().get("b");
            BigDecimal r2 = (BigDecimal) predictLine.getAlgorithmParams().get("r2");

            // 斜率应该接近2.0
            assertEquals(2.0, a.doubleValue(), 0.01, "斜率应该接近2.0");

            // 截距应该接近50.0
            assertEquals(50.0, b.doubleValue(), 0.01, "截距应该接近50.0");

            // R²应该接近1.0（完美拟合）
            assertTrue(r2.doubleValue() > 0.99, "R²应该接近1.0");

            // 验证预测点的准确性
            assertNotNull(predictLine.getPoints());
            assertTrue(predictLine.getPoints().size() > 0);

            // 检查第一个预测点（应该是起始日期的值）
            List<Object> firstPoint = predictLine.getPoints().get(0);
            LocalDate firstDate = (LocalDate) firstPoint.get(0);
            BigDecimal firstValue = (BigDecimal) firstPoint.get(1);

            assertEquals(startDate, firstDate);
            assertEquals(50.0, firstValue.doubleValue(), 0.01, "第一个预测点的值应该是50.0");

        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}
