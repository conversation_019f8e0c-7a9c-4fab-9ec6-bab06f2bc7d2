package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * CosQueryPredictTaskServiceImpl的单元测试
 */
public class CosQueryPredictTaskServiceImplTest {

    @Mock
    private DBHelper cdLabDbHelper;

    @InjectMocks
    private CosQueryPredictTaskServiceImpl cosQueryPredictTaskService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testQueryCategoryAndTaskList() {
        // 准备测试数据
        CosLongtermPredictTaskDO task1 = new CosLongtermPredictTaskDO();
        task1.setId(1L);
        task1.setCategoryId(100L);
        task1.setCategoryName("COS存储预测方案");
        task1.setIsEnable(true);
        task1.setPredictStart(LocalDate.of(2024, 1, 31));
        task1.setTaskStatus("SUCCESS");

        CosLongtermPredictTaskDO task2 = new CosLongtermPredictTaskDO();
        task2.setId(2L);
        task2.setCategoryId(100L);
        task2.setCategoryName("COS存储预测方案");
        task2.setIsEnable(true);
        task2.setPredictStart(LocalDate.of(2024, 7, 31));
        task2.setTaskStatus("RUNNING");

        CosLongtermPredictTaskDO task3 = new CosLongtermPredictTaskDO();
        task3.setId(3L);
        task3.setCategoryId(200L);
        task3.setCategoryName("COS年度预测方案");
        task3.setIsEnable(true);
        task3.setPredictStart(LocalDate.of(2024, 12, 31));
        task3.setTaskStatus("NEW");

        List<CosLongtermPredictTaskDO> mockTasks = Arrays.asList(task1, task2, task3);

        // Mock数据库查询
        when(cdLabDbHelper.getAll(eq(CosLongtermPredictTaskDO.class), eq("where is_enable=1")))
                .thenReturn(mockTasks);

        // 执行测试
        QueryCategoryAndTaskListReq req = new QueryCategoryAndTaskListReq();
        QueryCategoryAndTaskListResp resp = cosQueryPredictTaskService.queryCategoryAndTaskList(req);

        // 验证结果
        assertNotNull(resp);
        assertNotNull(resp.getCategoryList());
        assertEquals(2, resp.getCategoryList().size()); // 两个不同的方案

        // 验证第一个方案（categoryId=100）
        QueryCategoryAndTaskListResp.Category category1 = resp.getCategoryList().stream()
                .filter(c -> c.getCategoryId().equals(100L))
                .findFirst()
                .orElse(null);
        assertNotNull(category1);
        assertEquals(100L, category1.getCategoryId());
        assertEquals("COS存储预测方案", category1.getCategoryName());
        assertEquals("COS", category1.getCategoryType());
        assertEquals(2, category1.getPredictTaskList().size());

        // 验证任务按时间倒序排列（2024-07比2024-01新）
        QueryCategoryAndTaskListResp.Task firstTask = category1.getPredictTaskList().get(0);
        assertEquals(2L, firstTask.getTaskId());
        assertEquals("2024-07", firstTask.getYearMonth());
        assertEquals("RUNNING", firstTask.getTaskStatusCode());
        assertEquals("运行中", firstTask.getTaskStatusName());

        QueryCategoryAndTaskListResp.Task secondTask = category1.getPredictTaskList().get(1);
        assertEquals(1L, secondTask.getTaskId());
        assertEquals("2024-01", secondTask.getYearMonth());
        assertEquals("SUCCESS", secondTask.getTaskStatusCode());
        assertEquals("运行成功", secondTask.getTaskStatusName());

        // 验证第二个方案（categoryId=200）
        QueryCategoryAndTaskListResp.Category category2 = resp.getCategoryList().stream()
                .filter(c -> c.getCategoryId().equals(200L))
                .findFirst()
                .orElse(null);
        assertNotNull(category2);
        assertEquals(200L, category2.getCategoryId());
        assertEquals("COS年度预测方案", category2.getCategoryName());
        assertEquals("COS", category2.getCategoryType());
        assertEquals(1, category2.getPredictTaskList().size());

        QueryCategoryAndTaskListResp.Task task = category2.getPredictTaskList().get(0);
        assertEquals(3L, task.getTaskId());
        assertEquals("2024-12", task.getYearMonth());
        assertEquals("NEW", task.getTaskStatusCode());
        assertEquals("待预测", task.getTaskStatusName());
    }

    @Test
    void testQueryCategoryAndTaskListWithEmptyResult() {
        // Mock空结果
        when(cdLabDbHelper.getAll(any(), any())).thenReturn(Arrays.asList());

        QueryCategoryAndTaskListReq req = new QueryCategoryAndTaskListReq();
        QueryCategoryAndTaskListResp resp = cosQueryPredictTaskService.queryCategoryAndTaskList(req);

        assertNotNull(resp);
        assertNotNull(resp.getCategoryList());
        assertEquals(0, resp.getCategoryList().size());
    }

    @Test
    void testQueryCategoryAndTaskListWithSingleCategory() {
        // 准备单个方案的测试数据
        CosLongtermPredictTaskDO task = new CosLongtermPredictTaskDO();
        task.setId(1L);
        task.setCategoryId(100L);
        task.setCategoryName("COS存储预测方案");
        task.setIsEnable(true);
        task.setPredictStart(LocalDate.of(2024, 6, 30));
        task.setTaskStatus("PREDICT_SUCCESS");

        List<CosLongtermPredictTaskDO> mockTasks = Arrays.asList(task);

        when(cdLabDbHelper.getAll(eq(CosLongtermPredictTaskDO.class), eq("where is_enable=1")))
                .thenReturn(mockTasks);

        QueryCategoryAndTaskListReq req = new QueryCategoryAndTaskListReq();
        QueryCategoryAndTaskListResp resp = cosQueryPredictTaskService.queryCategoryAndTaskList(req);

        assertNotNull(resp);
        assertEquals(1, resp.getCategoryList().size());

        QueryCategoryAndTaskListResp.Category category = resp.getCategoryList().get(0);
        assertEquals(100L, category.getCategoryId());
        assertEquals("COS存储预测方案", category.getCategoryName());
        assertEquals("COS", category.getCategoryType());
        assertEquals(1, category.getPredictTaskList().size());

        QueryCategoryAndTaskListResp.Task taskResp = category.getPredictTaskList().get(0);
        assertEquals(1L, taskResp.getTaskId());
        assertEquals("2024-06", taskResp.getYearMonth());
        assertEquals("PREDICT_SUCCESS", taskResp.getTaskStatusCode());
        assertEquals("预测成功,待拆分", taskResp.getTaskStatusName());
    }
}
