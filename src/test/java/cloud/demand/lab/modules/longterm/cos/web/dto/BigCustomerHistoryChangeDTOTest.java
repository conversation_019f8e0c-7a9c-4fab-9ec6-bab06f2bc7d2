package cloud.demand.lab.modules.longterm.cos.web.dto;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BigCustomerHistoryChangeDTO 测试类
 */
class BigCustomerHistoryChangeDTOTest {

    @Test
    void testFromDO() {
        // 准备测试数据
        CosLongtermPredictInputBigCustomerChangeDO dO = new CosLongtermPredictInputBigCustomerChangeDO();
        dO.setId(1L);
        dO.setCategoryId(100L);
        dO.setTaskId(200L);
        dO.setCustomerName("测试客户");
        dO.setIsOutCustomer(true);
        dO.setStartDate(LocalDate.of(2024, 1, 1));
        dO.setEndDate(LocalDate.of(2024, 12, 31));
        dO.setNetChange(new BigDecimal("500.50"));

        // 执行转换
        BigCustomerHistoryChangeDTO dto = BigCustomerHistoryChangeDTO.fromDO(dO);

        // 验证结果
        assertNotNull(dto);
        assertEquals(100L, dto.getCategoryId());
        assertEquals(200L, dto.getTaskId());
        assertEquals("测试客户", dto.getCustomerName());
        assertTrue(dto.getIsOutCustomer());
        assertEquals(LocalDate.of(2024, 1, 1), dto.getStartDate());
        assertEquals(LocalDate.of(2024, 12, 31), dto.getEndDate());
        assertEquals(new BigDecimal("500.50"), dto.getNetChange());
    }

    @Test
    void testFromDO_Null() {
        // 测试null输入
        BigCustomerHistoryChangeDTO dto = BigCustomerHistoryChangeDTO.fromDO(null);
        assertNull(dto);
    }

    @Test
    void testToDO() {
        // 准备测试数据
        BigCustomerHistoryChangeDTO dto = new BigCustomerHistoryChangeDTO();
        dto.setCategoryId(100L);
        dto.setTaskId(200L);
        dto.setCustomerName("测试客户");
        dto.setIsOutCustomer(true);
        dto.setStartDate(LocalDate.of(2024, 1, 1));
        dto.setEndDate(LocalDate.of(2024, 12, 31));
        dto.setNetChange(new BigDecimal("500.50"));

        // 执行转换
        CosLongtermPredictInputBigCustomerChangeDO dO = dto.toDO();

        // 验证结果
        assertNotNull(dO);
        assertEquals(100L, dO.getCategoryId());
        assertEquals(200L, dO.getTaskId());
        assertEquals("测试客户", dO.getCustomerName());
        assertTrue(dO.getIsOutCustomer());
        assertEquals(LocalDate.of(2024, 1, 1), dO.getStartDate());
        assertEquals(LocalDate.of(2024, 12, 31), dO.getEndDate());
        assertEquals(new BigDecimal("500.50"), dO.getNetChange());
        // ID应该为null，因为DTO不包含ID字段
        assertNull(dO.getId());
    }
}
