package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictCategoryTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * CosCreatePredictTaskServiceImpl的单元测试
 */
public class CosCreatePredictTaskServiceImplTest {

    @Mock
    private DBHelper cdLabDbHelper;

    @InjectMocks
    private CosCreatePredictTaskServiceImpl cosCreatePredictTaskService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testQueryCategoryForCreate() {
        // 准备测试数据
        LongtermPredictCategoryConfigDO config1 = new LongtermPredictCategoryConfigDO();
        config1.setId(1L);
        config1.setCategory("COS存储预测方案");
        config1.setCategoryType("MODEL");
        config1.setIntervalMonth(6);
        config1.setPredictStart("CUR_MONTH");
        config1.setPredictEnd("LAST_MONTH_18");

        LongtermPredictCategoryConfigDO config2 = new LongtermPredictCategoryConfigDO();
        config2.setId(2L);
        config2.setCategory("COS年度预测方案");
        config2.setCategoryType("MODEL");
        config2.setIntervalMonth(12);
        config2.setPredictStart("2024-01-01");
        config2.setPredictEnd("2025-12-31");

        List<LongtermPredictCategoryConfigDO> mockConfigs = Arrays.asList(config1, config2);

        // Mock数据库查询
        when(cdLabDbHelper.getAll(eq(LongtermPredictCategoryConfigDO.class), 
                eq("where category_type != ?"), 
                eq(LongtermPredictCategoryTypeEnum.MERGE.getCode())))
                .thenReturn(mockConfigs);

        // 执行测试
        QueryCategoryForCreateReq req = new QueryCategoryForCreateReq();
        QueryCategoryForCreateResp resp = cosCreatePredictTaskService.queryCategoryForCreate(req);

        // 验证结果
        assertNotNull(resp);
        assertNotNull(resp.getCategoryList());
        assertEquals(2, resp.getCategoryList().size());

        // 验证第一个方案
        QueryCategoryForCreateResp.Item item1 = resp.getCategoryList().get(0);
        assertEquals(1L, item1.getCategoryId());
        assertEquals("COS存储预测方案", item1.getCategoryName());
        assertNotNull(item1.getPredictStart());
        assertNotNull(item1.getPredictEnd());
        assertNotNull(item1.getInputArgDateRanges());
        assertNotNull(item1.getStrategyTypes());
        assertEquals(3, item1.getStrategyTypes().size()); // 激进、中立、保守

        // 验证第二个方案
        QueryCategoryForCreateResp.Item item2 = resp.getCategoryList().get(1);
        assertEquals(2L, item2.getCategoryId());
        assertEquals("COS年度预测方案", item2.getCategoryName());
        assertNotNull(item2.getInputArgDateRanges());

        // 验证策略类型
        for (QueryCategoryForCreateResp.Item item : resp.getCategoryList()) {
            for (QueryCategoryForCreateResp.StrategyType strategy : item.getStrategyTypes()) {
                assertNotNull(strategy.getStrategyTypeCode());
                assertNotNull(strategy.getStrategyTypeName());
            }
        }

        // 验证输入参数日期范围
        for (QueryCategoryForCreateResp.Item item : resp.getCategoryList()) {
            for (QueryCategoryForCreateResp.InputArgDateRange range : item.getInputArgDateRanges()) {
                assertNotNull(range.getDateName());
                assertNotNull(range.getStartDate());
                assertNotNull(range.getEndDate());
            }
        }
    }

    @Test
    void testQueryCategoryForCreateWithEmptyResult() {
        // Mock空结果
        when(cdLabDbHelper.getAll(any(), any(), any())).thenReturn(Arrays.asList());

        QueryCategoryForCreateReq req = new QueryCategoryForCreateReq();
        QueryCategoryForCreateResp resp = cosCreatePredictTaskService.queryCategoryForCreate(req);

        assertNotNull(resp);
        assertNotNull(resp.getCategoryList());
        assertEquals(0, resp.getCategoryList().size());
    }
}
