package com.pugwoo.branch;


import com.pugwoo.branch.git.config.Constants;
import com.pugwoo.branch.git.model.GitUserPasswordDTO;
import com.pugwoo.branch.git.service.IJGitService;
import com.pugwoo.branch.git.utils.GitUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.PullResult;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.transport.RefSpec;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@SpringBootTest
public class TestGitUtils {

    /**
     * 通过本地路径path获取git实例，不存在返回null
     */
    private static Git getJGit(String localPath) throws IOException {
        Repository repository = GitUtils.getJRepository(localPath);
        if(repository == null) {
            return null;
        }
        return new Git(repository);
    }

    public static void main(String[] args) throws Exception {
        Git git = getJGit(Constants.LOCAL_PATH + "test1");
        GitUserPasswordDTO password = new GitUserPasswordDTO();
        password.setUsername("root");
        password.setPassword("root");

        // System.out.println(JSON.toJson(GitUtils.getRemoteTags(git, password)));

        Iterable<RevCommit> log = git.log().call();
        for (Iterator<RevCommit> iterator = log.iterator(); iterator.hasNext();) {
            RevCommit rev = iterator.next();
            System.out.println(rev.getId().toObjectId().getName());
           // logMessages.add(rev.getFullMessage());
        }

    }

    @Test
    public void testRemoteUrl() throws Exception {
        Git git = getJGit("/tmp/repos/branchResetTest/.git");
        assert git != null;
        GitUtils.setAutoMergeRemoteUrl(git, "autoMergeRemote", "https://code.pugwoo.com/pugwoo/branchResetTest.git");
        System.out.println("Remote 添加成功！");
    }

    @Test
    public void fetchRemoteUrlBranch() throws Exception {
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("xxxx");
        gitUserPasswordDTO.setPassword("xxxxxxxxxxxxxxxx");

        Git git = getJGit("/tmp/repos/branchResetTest/.git");
        assert git != null;
        PullResult result = git.pull().setRemote("autoMergeRemote").setRemoteBranchName("pgpg")
                .setCredentialsProvider(GitUtils.from(gitUserPasswordDTO))
                .setTimeout(60)
                .call();

        if (result.isSuccessful()) {
            System.out.println("Pull 成功。");
        } else {
            System.out.println("Pull 失败。原因：" + result.toString());
        }
    }

    @Test
    public void down() throws Exception {
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        GitUtils.cloneNew("http://10.100.99.41:32081/root/jgit.git",
                "/tmp/repos/biz-broker", gitUserPasswordDTO);
    }

    @Test
    public void getRemoteBranches() throws Exception {
        Git git = getJGit("/tmp/repos/biz-broker/.git");
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        GitUtils.deleteRemoteTag(git, gitUserPasswordDTO, "release-0.0.1");
//        List<String> remoteBranches = GitUtils.getRemoteBranches(git, gitUserPasswordDTO);
//
//        for (String name : remoteBranches) {
//            System.out.println(name);
//        }
//        List<String> localBranches = GitUtils.getLocalBranches(git);
//        for (String name : localBranches) {
//            System.out.println(name);
//        }
        //GitUtils.syncLocalBranchFromMasterToRemote(git, gitUserPasswordDTO, "develop-05");

        //GitUtils.deleteRemoteBranch(git, gitUserPasswordDTO, "develop-05");
        //GitUtils.deleteLocalBranch(git, "develop-05");

    }


    @Autowired
    IJGitService service;

    @Test
    public void deployDevelop() throws Exception {
        String url = "/tmp/repos/biz-broker/.git";
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        List<String> strings = new ArrayList<>();
        strings.add("develop-04");
        strings.add("develop-05");
        //service.deployDevelop(url, gitUserPasswordDTO, strings);
    }

    @Test
    public void deployRelease() throws Exception {

        String url = "/tmp/repos/biz-broker/.git";
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        List<String> strings = new ArrayList<>();
        strings.add("develop-04");
        strings.add("develop-05");
        String tag = "1.0.2";
        //service.developRelease(url, gitUserPasswordDTO, strings, tag);
    }

    @Test
    public void deploy() throws Exception {
        String url = "/tmp/repos/jtest/.git";
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        Repository repository = GitUtils.getJRepository(url);

        Git git = new Git(repository);
        git.checkout().setName("master").call();
        git.pull().call();
        String develop = "release-0.0.2";

        git.branchCreate().setName("tmp_tag")
                .setStartPoint("refs/tags/" + develop).call();
        git.checkout().setName("tmp_tag").call();

        git.checkout().setName("master").call();

        git.merge().include(repository.exactRef("refs/heads/tmp_tag"))
                .call();
        RefSpec refSpec = new RefSpec()
                .setDestination("refs/heads/master");
        git.push().setRefSpecs(refSpec)
                .setRemote("origin")
                .setCredentialsProvider(from(gitUserPasswordDTO))
                .call();
        git.branchDelete().setBranchNames("tmp_tag").call();
    }

    @Test
    public void down1() {
        String url = "/tmp/repos/jtest01.git";
        String gitUrl = "http://10.100.99.41:32081/root/jtest01.git";
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername("root");
        gitUserPasswordDTO.setPassword("admin");
        try {
            GitUtils.cloneNew(gitUrl, url, gitUserPasswordDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return;
    }

    private static UsernamePasswordCredentialsProvider from(GitUserPasswordDTO password) {
        return new UsernamePasswordCredentialsProvider(password.getUsername(), password.getPassword());
    }

}